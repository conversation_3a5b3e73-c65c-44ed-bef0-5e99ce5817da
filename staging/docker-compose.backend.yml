version: '3.8'
services:
  # dossier manager service
  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    environment:
      - DJANGO_ALLOWED_HOSTS=localhost 127.0.0.1 [::1] db.localhost localhost:8000 dms.localhost localhost dms.${BASE_DOMAIN} api.${BASE_DOMAIN} admin.${BASE_DOMAIN} service.${BASE_DOMAIN}
      - POSTGRES_HOST=pgcluster1.core-services
      - POSTGRES_DB=staging_django
      - POSTGRES_USER=staging_django
      - DEBUG=1
      - S3_ENDPOINT=minio.${BASE_DOMAIN}
      - S3_ACCESS_KEY=S3_ACCESS_KEY
      - S3_SECRET_KEY=S3_SECRET_KEY
      - S3_SECURE=true
      - ADMIN_S3_ENDPOINT_URL=https://minio.${BASE_DOMAIN}
      - BASE_DOMAIN=${BASE_DOMAIN}
      - KEYCLOAK_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB
    labels:
      caddy: dms.${BASE_DOMAIN}
      caddy.reverse_proxy: "{{upstreams 8000}}"
      caddy.import: tls
    networks:
      default:
        aliases:
          - dms.${BASE_DOMAIN}
          - dossier-manager-server
      core-services:
      caddy:
    secrets:
      - DMS_POSTGRES_PASSWORD
      - DMS_SECRET_KEY
    healthcheck:
      test: curl --fail http://localhost:8000/api/docs || exit 1
      start_period: 30s
      interval: 5s
      timeout: 5s
      retries: 20

  # dossier event consumer
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    environment:
      - POSTGRES_HOST=pgcluster1
      - POSTGRES_DB=staging_django
      - POSTGRES_USER=staging_django
      - DEBUG=1
      - S3_ENDPOINT=minio.${BASE_DOMAIN}
      - S3_ACCESS_KEY=S3_ACCESS_KEY
      - S3_SECRET_KEY=S3_SECRET_KEY
      - S3_SECURE=true
      - ADMIN_S3_ENDPOINT_URL=https://minio.${BASE_DOMAIN}
      - BASE_DOMAIN=${BASE_DOMAIN}
      - KEYCLOAK_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB
    command: python manage.py dossier_event_consumer
    networks:
      core-services:
    secrets:
      - DMS_POSTGRES_PASSWORD
      - DMS_SECRET_KEY

secrets:
  DMS_POSTGRES_PASSWORD:
    file: secrets/DMS_POSTGRES_PASSWORD.txt
  DMS_SECRET_KEY:
    file: secrets/DMS_SECRET_KEY.txt

networks:
  core-services:
    external: true