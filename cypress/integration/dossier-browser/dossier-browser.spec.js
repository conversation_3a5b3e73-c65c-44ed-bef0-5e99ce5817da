describe('check offline dossier browser', () => {
  beforeEach(() => {
    cy.visit('/result/package/001 Hypodossier Document Browser.html')
  })

  it('shows 15 documents', () => {
    cy.get('[view="page"]').should('have.length', 15)
  })

  it('shows 2 files errors', () => {
    cy.contains("sales_pitch_mix_with_errors.zip/sales_pitch_mix/error.zip")
    cy.contains("sales_pitch_mix_with_errors.zip/sales_pitch_mix/unreadable.pdf")
  })

})
