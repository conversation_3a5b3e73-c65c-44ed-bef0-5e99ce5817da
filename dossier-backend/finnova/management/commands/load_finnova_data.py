import djclick as click
import structlog
from django.contrib.auth import get_user_model
from faker import Faker

from finnova.factories import (
    FinnovaAccountFactoryFaker,
    load_finnova_document_categories,
)
from finnova.schemas import AccountName

User = get_user_model()
logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
@click.argument("default_bucket_name", type=click.STRING, default=None)
def load_account(
    account_key: str = AccountName.finnovadev.value, default_bucket_name: str = None
):
    """
    Create or update a finnova account with close to production config. This loads
    document categories. By default a finnova dev account will be created.

    Example:

    python manage.py reset-db
    python manage.py load_finnova_data load-account finnovadev

    """
    Faker.seed(234777)
    bfac = FinnovaAccountFactoryFaker(
        account_key=account_key,
    )
    bfac.account.save()

    if default_bucket_name:
        bfac.account.default_bucket_name = default_bucket_name
        bfac.account.save()


@grp.command()
@click.argument("account_key")
def update_document_categories(account_key: str):
    """
    Load / update all document categories for BCGE. Do not rely on the factory here
    as the factory changes the account itself on initialization.

    python manage.py load_finnova_data update-document-categories finnovadev

    @param account_key:
    @return:
    """
    load_finnova_document_categories(account_key)
