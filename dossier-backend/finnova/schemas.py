from datetime import datetime
from enum import Enum
from typing import Optional, Annotated
from uuid import UUID

from pydantic import BaseModel, Field, StringConstraints

from clientis.schemas.schemas import ClientisExternalDossierID
from dossier import schemas_external
from dossier.schemas import Language

# Must have 3 dots, no empty elements

PrincipalID = Annotated[str, StringConstraints(max_length=255)]
DossierName = Annotated[str, StringConstraints(max_length=255)]


class AccountName(str, Enum):
    # These strings are used as account keys
    finnovadev = "finnovadev"
    finnovatest = "finnovatest"
    finnovaprod = "finnovaprod"  # domain: no suffix, just finnovaprod.hypodossier.ch


class Scope(str, Enum):
    READ_ONLY = "read_only"
    READ_WRITE = "read_write"


class DossierAccessGrant(BaseModel):
    expires_at: datetime
    username: str = Field(max_length=150)
    external_dossier_id: ClientisExternalDossierID = Field(
        description="Mandant.Kundennummer.Rahmennummer.Antragsnummer"
    )
    scope: Scope


class Dossier(BaseModel):
    uuid: UUID
    external_dossier_id: ClientisExternalDossierID = Field(
        description="Mandant.Kundennummer.Rahmennummer.Antragsnummer"
    )


class CreateDossier(BaseModel):
    external_dossier_id: ClientisExternalDossierID
    name: DossierName
    lang: Optional[Language] = Language.de


class ChangeDossier(BaseModel):
    name: Optional[DossierName] = None
    lang: Optional[Language] = None
    sequence_number: Optional[
        Annotated[str, Field(description="Laufnummer", max_length=255)]
    ] = None
    financing_number: Optional[
        Annotated[
            str,
            Field(
                description="Finanzierungsnummer (Kundenummer/Rahmennummer/Laufnummer)",
                max_length=255,
            ),
        ]
    ] = None
    client_id: Optional[
        Annotated[str, Field(max_length=255, description="Kundennummer")]
    ] = None

    client_key: Optional[
        Annotated[str, Field(max_length=255, description="Kunden Schlüssel")]
    ] = None


class ChangeDossierResponse(ChangeDossier):
    external_dossier_id: ClientisExternalDossierID = Field(
        description="Mandant.Kundennummer.Rahmennummer.Antragsnummer"
    )


class DossierCloseReadyResponse(schemas_external.DossierCloseReadyResponse):
    pass


class DossierCloseResponse(schemas_external.DossierCloseResponse):
    pass
