# Generated by Django 4.2.16 on 2024-10-18 07:49

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("dossier", "0104_alter_whitelistaccess_account"),
    ]

    operations = [
        migrations.CreateModel(
            name="FinnovaDossierProperties",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "sequence_number",
                    models.CharField(
                        blank=True, help_text="Laufnummer", max_length=255, null=True
                    ),
                ),
                (
                    "financing_number",
                    models.CharField(
                        blank=True,
                        help_text="Finanzierungsnummer (Kundenummer/Rahmennummer/Laufnummer)",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "client_id",
                    models.Char<PERSON><PERSON>(
                        blank=True, help_text="Kundennummer", max_length=255, null=True
                    ),
                ),
                (
                    "client_key",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Kunden <PERSON>hlüssel",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dossier.account",
                    ),
                ),
                (
                    "dossier",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="finnova_properties",
                        to="dossier.dossier",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
