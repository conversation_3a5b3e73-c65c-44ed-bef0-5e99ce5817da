import pytest
from pydantic import BaseModel, ValidationError, Field

from clientis.schemas.schemas import ClientisExternalDossierID


# Create a Pydantic model using the ExternalDossierID
class ExampleModel(BaseModel):
    external_dossier_id: ClientisExternalDossierID = Field(
        description="Mandant.Kundenummer.Antragsnummer",
    )


# Test cases for valid ExternalDossierID values
@pytest.mark.parametrize(
    "valid_id",
    [
        "A.B.C.D",  # Minimum length
        "AB.C.D.123",
        "ab.c.D-123.def",
        "A1B2C3D4E" * 23 + "." + "A1B2C.3D4E.F",  # Total length 250 characters
        "Va.lid-ID.1.23",
        "va.l.id.id-456",
        "Te.st.I.D-123",
        "a.b-c.d.1-2-3",
        "Mandant.Kundennummer.Rahmennummer.Antragsnummer",
        "bankNr.KundenNr.RahmenNr.AntragsNr",
    ],
)
def test_valid_external_dossier_id(valid_id):
    model = ExampleModel(external_dossier_id=valid_id)
    assert model.external_dossier_id == valid_id


# Test cases for invalid ExternalDossierID values
@pytest.mark.parametrize(
    "invalid_id",
    [
        "",  # Empty string
        "a.b.c.d def",  # Contains space
        "a.b.c.d$123",  # Contains invalid character $
        "あ.い.う.えお",  # Contains non-ASCII characters
        "a.b.c.d!",  # Contains invalid character !
        "a.b.c.d_",  # Contains underscore, which is not allowed
        "a.b.c.d/def",  # Contains invalid character /
        "A.B.C.D_123",  # Underscore is not allowed
        "T.e.s.t@ID",  # @ is not allowed
        "M.k.A" + "A" * 256,  # Exceeds maximum length
        "." * 255,  # Maximum length of only full stops
        "Mandant.Kundenummer",  # only one dot
        "Mandant.Kundenummer.Antragsnummer",  # only two dots
        "ABC...",
        "A.B.C..",
        "A.B.C.",
    ],
)
def test_invalid_external_dossier_id(invalid_id):
    with pytest.raises(ValidationError):
        ExampleModel(external_dossier_id=invalid_id)
