
SECRET_KEY="some very long secret"
RABBIT_URL=amqp://dossier-manager:<EMAIL>:5672/dossier-processor

DJANGO_ALLOWED_HOSTS="localhost localhost:8000 dms.hypo.duckdns.org"
DJANGO_CORS_ORIGIN_WHITELIST="http://localhost:8000 https://service.hypo.duckdns.org https://service.hypo-staging-du.duckdns.org http://localhost:3000 http://localhost:1337"

POSTGRES_HOST=db
POSTGRES_DB=test_dms
POSTGRES_USER=dms
POSTGRES_PASSWORD=dms

DEBUG=1

# Enable this for Silk Django SQL profiling
# Some tests fail with silk because the do not need a db connection without silk but would need one with silk
DEBUG_ENABLE_SILK=False

S3_ENDPOINT=minio:9000
S3_ACCESS_KEY=S3_ACCESS_KEY
S3_SECRET_KEY=S3_SECRET_KEY
S3_SECURE=false
S3_REGION=ch-dk-2

ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY=DossierZipper.DossierZipRequestV1


ADMIN_S3_ENDPOINT_URL=https://notavailable.localhost
BASE_DOMAIN=hypo.duckdns.org
KEYCLOAK_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB
# Used for API used to Manage/create Accounts and JWKs
MANAGEMENT_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB

DOSSIER_EVENTS_WORKER_QUEUE_NAME=DossierEvents.dev.DossierEventV1
IMAGE_EXPORTER_WORKER_QUEUE_NAME=DossierImageExporter.dev.ImageExportRequest

DOSSIER_EVENT_CONSUMER_PREFETCH=20
DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD=10
DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE=80
DOSSIER_EVENT_CONSUMER_FINALIZE_PROCESS_BACKOFF_MAX_TRIES=6

# If this is True then the document category ZKB_VBV will be handled in a special way
# Semantic document is set to read-only directly after processing
# Download triggers download of extracted file, not of the processed file
# This only applies if the extracted file is a PDF
ZKB_VBV_USE_EXTRACTED_FILE=True

# If this is True and processing delivers a processed_page, semantic_document and/or semantic_page with a
# document_category which is not configured then the document category is added automatically.
# If this is False then the document_category will be mapped to DocumentCategory.UNKNOWN
ADD_NEW_DOCUMENT_CATEGORIES_FROM_PROCESSING=False
KEYCLOAK_REALM_ENDPOINT=https://auth.hypo.duckdns.org/realms/dev-hypodossier/
ADMIN_KEYCLOAK_CLIENT_ID=django-admin
ADMIN_KEYCLOAK_SECRET_KEY=wqtvWuYecdJ6vfnM4g6PGM668k3D2ZeY

# If this is True, documents of certain document categories for BEKB will be grouped (= combined into one
# semantic document) directly before the BEKB archive export happens. Structure of semantic documents is
# permanently changed. Documents with different collaterals will NOT be merged into one document.
ENABLE_BEKB_ARCHIVE_GROUPING_BY_DOCUMENT_CATEGORY=True


# Namespace, e.g. BEKB, SWISSFEX, etc
# Inserted into RabbitMQ routing keys, as rabbitmq is currently shared between instances
DEPLOYMENT_NAMESPACE=test

RABBIT_URL=amqp://admin:admin@rabbitmq:5672/

INSTANCE_SIGNING_KEY={"d":"HMxExd13eBEogE6H-xF_vJWZVH4Zavzk4LVJsQ9qQmzXQyh6nIjXhg7OvOACq3U7A2QhIbmqU-gpH9s7Vba9xxNwY9LMDjxGz-ovaj0FPeOsTj5OTWaJBK4Gv_v1zUIpO_8Nvn_bVLLeoZF9jXgWmrxN9DHz9pRxT8zriU6bCgDKBgR3JAFC1QAyHjY0i4dW12NOH06Cv53PWelaoiZ5I49NLizVvSX1NQ4JYTiwKwfiXWEBUCudaXWt2k06lIPE3UAUv_tbteHrJtNoZ-Hhtnx9fbyII9k_gzjVV2cBVT-DodQxHRBR7m9StE2UT10ZyOUpS8e-pioN-UwfTA5kgQ","dp":"xnX34zYjq65DJU7tfPB7g_irMTS_RF9gCecPSHdWJ1p1mG4QacmAaJeIZAX58q2pqv2o2Y5kJCdEX-iabVGKvFEUcFdqDZ4mAj744tNUV-tgAsr-pMT9OgE7AnIyq8cYSUIc9KkmHBiBHwM3VIeJljb19MkX9hlEs54SiQHiPkE","dq":"w42P13rDwpGPFnDT9pXhsOqcpVqptTt5-5jeOG4KHX8ZO_Y2QDIrTJoYngvxFjD6d5TFhkPNu8gwH8PNawRgnLs3gcjOXVWTBufqjct2sZG1FXcSfUxj-IoMKktoVYCwCIxImRKa7JhT9iqWOoKpTqqrLFSTySZfQ24_dfkcwW0","e":"AQAB","kid":"567558dd-571a-47dd-9f28-408c08719f3d","kty":"RSA","n":"7YT62XnCtRUWqfK8VJYYS26RTcEsTUR9cScwd16BDtsBGGmZ97wWY04ObFbX-AHBQqmcDoYGwHueky5NezO5kRUNkm819x5h8oA0NLjIW4bu24yt1gPxq484cgIelNfv9fUaJWyAwkcdVP5cXb3DZK05K32WuwDVi5LTaxhulwmYZRz4C9HnNEqnwRBAzorZ42Jo3pMfQVaRyEL75IuR2SMyJZWI0bHzigNlSEqo3gGaAZR9MqWySQCWm20GQALTywuKwTqlmXjaV7mQq-O_hdraQeKksU6-D136X-sflTmTedrv2_E6sExbq_ZVWT0yZdEHnyFfWSQtjS9C8J3RSw","p":"-ljS3FNTyQTzgwLSifBBEGjF2ws_Klb2HzIyWyaYQFXrYM1_eNcLS00APoLh5YckPVVaLZW7wfh0Kkg_amwv4lX_UTGNX4tB4bsNEQpGR_-Q6Cf6euh8e1YCKp5QbOy76hg258HtjszUY8rGiPUZ4K8VHiQw1jYoDffZzEigcCE","q":"8uIBIXCy_fWCV-QuzjPMEAMD_ORBAtRSbx2dA5nlS2PjctW72zUab3BDL47lJGMrioJ-hnlAXAPmdf2jXgrnlRbQPGTz9_we1tL5dWi1tRzNZMm_xDkFs0bYu5SeFtipMPlDBtY_Pd-w5F4ubO7BqcvnE8idVhtsXVU3zeU2g-s","qi":"JbZZm8hGJgqEkPhI2eSLz2X5DGaiE4ZuqDLX1HcsxBCj2WjD_OAmtJSlo3eA_BoPe9c5Uul09U44oDNz-lV5LtYeJYNG8i7oljxhWd9Fk3c6P1vFA17ewklpbGq-53KBxMhu1jVUw0GV8Cg3SaO9r99s01i9Ykvr_6HjEn9oO9I"}

HYPYFREP_PDFA_CONVERT_REQUEST_ROUTING_KEY=Hypyfrep.PDFA.Convert.Request.RoutingKey

CDP_CONFIDENCE_THRESHOLD=0.7