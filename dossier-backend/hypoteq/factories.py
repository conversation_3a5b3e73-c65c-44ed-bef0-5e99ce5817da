from typing import Optional

from dossier.factories import (
    set_new_account_defaults,
    DefaultAccountFactoryFaker,
)
from dossier.models import Account, NavigationStrategy
from hypoteq.schemas import schemas


def update_or_create_hypoteq_account(account_key: str) -> Account:
    assert is_valid_account_key(
        account_key
    ), f"Invalid account key: '{account_key}'. Valid keys are {[a.value for a in schemas.AccountName]}"

    account, _ = Account.objects.update_or_create(key=account_key)

    if account_key == schemas.AccountName.hypoteq.value:
        account.name = "Hypoteq Production"
        account.default_bucket_name = (
            "production-v3-public-hypoteq20250121152734095000000001"
        )
        account.dmf_endpoint = "https://hypoteq.hypodossier.ch"

        account.navigation_strategy = NavigationStrategy.NO_DOSSIER_LIST_BRANDED
        account.enable_button_create = False
        account.enable_feedback_form = False
        account.enable_uploading_files = False
        account.enable_document_upload = False
        account.allow_dossier_listing = False

    else:
        raise ValueError(f"Invalid account key '{account_key}'")

    set_new_account_defaults(account)

    # Standard config for hypoteq
    account.max_dossier_expiry_duration_days = 500

    # To allow 20 days for final archiving process
    account.default_dossier_expiry_duration_days = 520
    account.valid_dossier_languages = ["De", "En"]
    account.valid_ui_languages = ["de", "en"]
    account.show_document_category_external = False
    account.show_business_case_type = False
    account.enable_virus_scan = False
    account.enable_rendering_hurdles_tab = False
    account.enable_dossier_permission = False
    account.enable_dossier_assignment = False

    # Download will most likely happen via API. That will provide the document_category.
    # No need to put it into the attachment for now
    account.enable_download_metadata_json = False
    account.enable_semantic_document_export = False

    return account


# Function to validate if the string is part of the enum
def is_valid_account_key(account_key: str) -> bool:
    try:
        # Attempt to match the account_key with an enum member
        schemas.AccountName(account_key)
        return True
    except ValueError:
        # If account_key is not found in the enum, return False
        return False


class HypoteqAccountFactoryFaker(DefaultAccountFactoryFaker):
    def __init__(
        self,
        account: Optional[Account] = None,
        account_key: Optional[str] = None,
    ):
        if account is None:
            if account_key is None:
                account_key = schemas.AccountName.hypoteq.value
        else:
            account_key = account.key

        account = update_or_create_hypoteq_account(account_key)

        super().__init__(account, "de_CH")
