# Generated by Django 3.2.16 on 2022-11-01 06:52

import adminsortable.fields
import colorfield.fields
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='StateCondition',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StateMachine',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, unique=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StateTransition',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransitionPrecondition',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('condition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='preconditions', to='statemgmt.statecondition')),
                ('transition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='preconditions', to='statemgmt.statetransition')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Status',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=255)),
                ('name_de', models.CharField(blank=True, max_length=255, null=True)),
                ('description_de', models.TextField(blank=True, null=True)),
                ('name_fr', models.CharField(blank=True, max_length=255, null=True)),
                ('description_fr', models.TextField(blank=True, null=True)),
                ('name_it', models.CharField(blank=True, max_length=255, null=True)),
                ('description_it', models.TextField(blank=True, null=True)),
                ('name_en', models.CharField(blank=True, max_length=255, null=True)),
                ('description_en', models.TextField(blank=True, null=True)),
                ('color', colorfield.fields.ColorField(default='#FF0000', image_field=None, max_length=18, samples=None)),
                ('order', models.PositiveIntegerField(db_index=True, default=0, editable=False)),
                ('state_machine', adminsortable.fields.SortableForeignKey(default='1699513f-a125-4d4c-9804-9354426b277c', on_delete=django.db.models.deletion.CASCADE, to='statemgmt.statemachine')),
            ],
            options={
                'verbose_name_plural': 'Status',
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='statetransition',
            name='from_state',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='from_states', to='statemgmt.status'),
        ),
        migrations.AddField(
            model_name='statetransition',
            name='state_machine',
            field=models.ForeignKey(default='1699513f-a125-4d4c-9804-9354426b277c', on_delete=django.db.models.deletion.CASCADE, related_name='transitions', to='statemgmt.statemachine'),
        ),
        migrations.AddField(
            model_name='statetransition',
            name='to_state',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='to_states', to='statemgmt.status'),
        ),
        migrations.AddConstraint(
            model_name='status',
            constraint=models.UniqueConstraint(fields=('state_machine', 'key'), name='unique_statemgmt_status'),
        ),
    ]
