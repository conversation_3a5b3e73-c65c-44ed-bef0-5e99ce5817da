[{"model": "statemgmt.statemachine", "pk": "94c0547a-331b-42c2-8e31-128a7645816c", "fields": {"created_at": "2023-01-18T13:22:51.004Z", "updated_at": "2023-01-18T13:22:51.029Z", "name": "BEKB", "start_status": "d92571e5-b99a-4211-bd20-6641cfc0b4c2"}}, {"model": "statemgmt.statecondition", "pk": "326fa9bc-7b5f-4233-8d33-3d8abdc31b6b", "fields": {"created_at": "2023-01-18T13:22:51.007Z", "updated_at": "2023-01-18T13:22:51.007Z", "name": "bekb_completeness_check_fulfilled"}}, {"model": "statemgmt.statecondition", "pk": "3914e762-5012-4cba-9597-6edc3ea99c12", "fields": {"created_at": "2023-01-18T13:22:51.009Z", "updated_at": "2023-01-18T13:22:51.009Z", "name": "is_false"}}, {"model": "statemgmt.statecondition", "pk": "52521ab4-5e49-4c12-bcc3-58ba5503159e", "fields": {"created_at": "2023-01-18T13:22:51.010Z", "updated_at": "2023-01-18T13:22:51.010Z", "name": "bekb_has_kbus_business_nr"}}, {"model": "statemgmt.statecondition", "pk": "638e61c2-7f6d-45a7-a76c-cfe2f64092d9", "fields": {"created_at": "2022-10-31T10:40:17.846Z", "updated_at": "2022-10-31T10:40:17.846Z", "name": "is_system"}}, {"model": "statemgmt.statecondition", "pk": "7186234f-32f2-46e7-aa30-4cab742efcbb", "fields": {"created_at": "2023-01-18T13:22:51.011Z", "updated_at": "2023-01-18T13:22:51.011Z", "name": "bekb_all_have_ekd_nr"}}, {"model": "statemgmt.statecondition", "pk": "81f7f940-ab1b-47b4-a8a1-7036adc39bfb", "fields": {"created_at": "2023-01-18T13:22:51.008Z", "updated_at": "2023-01-18T13:22:51.008Z", "name": "is_true"}}, {"model": "statemgmt.statecondition", "pk": "af0e5ed2-0918-428a-b909-2acc88802361", "fields": {"created_at": "2023-01-18T13:22:51.011Z", "updated_at": "2023-01-18T13:22:51.011Z", "name": "bekb_all_collaterals_mapped"}}, {"model": "statemgmt.statecondition", "pk": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "fields": {"created_at": "2022-10-31T10:40:13.153Z", "updated_at": "2022-10-31T10:40:13.153Z", "name": "is_user"}}, {"model": "statemgmt.status", "pk": "03342919-52d9-4234-85ec-cffd1db390f8", "fields": {"created_at": "2023-01-18T13:22:51.028Z", "updated_at": "2023-01-18T13:22:51.028Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "EXPORT_DONE", "name_de": "Archivierung erfolgreich", "description_de": "", "name_fr": "Archivage r<PERSON>i", "description_fr": "", "name_it": "Archiviazione riuscita", "description_it": "", "name_en": "Export done", "description_en": "", "color": "#DDF7F5", "order": 18}}, {"model": "statemgmt.status", "pk": "1448db7e-f622-48d9-aa91-f15663fc0ec8", "fields": {"created_at": "2023-01-18T13:22:51.017Z", "updated_at": "2023-01-18T13:22:51.017Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "IN_CREDIT_OFFICE", "name_de": "Dossier bei Kreditprüfung", "description_de": "KOFF prüft das Dossier und gibt es anschliessend an FICO zurück.", "name_fr": "Dossier au contrôle d'entrée", "description_fr": "KOFF examine le dossier et le renvoie ensuite à FICO.", "name_it": "Dossier con controllo del credito", "description_it": "KOFF esamina il dossier e poi lo restituisce a FICO.", "name_en": "In credit office", "description_en": "KOFF reviews the dossier and then returns it to FICO.", "color": "#FEF3CA", "order": 7}}, {"model": "statemgmt.status", "pk": "4da2ffca-cdb7-4457-8a5a-e11deaf8660f", "fields": {"created_at": "2023-01-18T13:22:51.021Z", "updated_at": "2023-01-18T13:22:51.021Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "READY_FOR_EXPORT_DEAL", "name_de": "Archivierung beauftragt", "description_de": "Verarbeitung schliesst Geschäftsfall ab und beauftragt Archivierung (und anschliessende Löschung aus HypoDossier).", "name_fr": "Demande transmise pour archivage", "description_fr": "Le traitement complète l'analyse de rentabilisation et commande l'archivage (et la suppression ultérieure d'HypoDossier).", "name_it": "Archiviazione commissionata", "description_it": "L'elaborazione completa l'archiviazione del business case e delle commissioni (e la successiva cancellazione da HypoDossier).", "name_en": "Ready for export", "description_en": "Processing completes business case and commissions archiving (and subsequent deletion from HypoDossier).", "color": "#ECCFF5", "order": 10}}, {"model": "statemgmt.status", "pk": "68a9d5c2-b46f-4d5e-a30d-b69e3226b2bf", "fields": {"created_at": "2023-01-18T13:22:51.023Z", "updated_at": "2023-01-18T13:22:51.023Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "EXPORT_ARCHIVE_AVAILABLE", "name_de": "<PERSON><PERSON><PERSON> wird archiviert", "description_de": "Export steht zur Verfügung und die Archivierung wird demnächst durchgeführt.", "name_fr": "Dossier en cours d'archivage", "description_fr": "L'exportation est disponible et l'archivage aura lieu bientôt.", "name_it": "<PERSON>asci<PERSON>lo archiv<PERSON>", "description_it": "L'esportazione è disponibile e l'archiviazione avverrà a breve.", "name_en": "Export archive available", "description_en": "Export is available and archiving will take place soon.", "color": "#F9E3F3", "order": 12}}, {"model": "statemgmt.status", "pk": "69f4108d-22fa-49f4-90ee-7eb77a0487f6", "fields": {"created_at": "2023-01-18T13:22:51.020Z", "updated_at": "2023-01-18T13:22:51.020Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "IN_BACK_OFFICE", "name_de": "Dossier bei Verarbeitung", "description_de": "Verarbeitung prüft Kreditauftrag und wickelt ab (evtl. Zurückweisung an FICO).", "name_fr": "Dossier en cours de traitement", "description_fr": "Traitement des contrôles et des processus de commande de crédit (rejet possible à FICO).", "name_it": "Dossier per l'elaborazione", "description_it": "Elaborazione controlli ed elaborazioni ordine di credito (eventuale rifiuto a FICO).", "name_en": "In back office", "description_en": "Processing checks and processes credit order (possible rejection to FICO).", "color": "#EBEED6", "order": 9}}, {"model": "statemgmt.status", "pk": "7993f480-5f32-4d2e-9c2f-8cef310502d9", "fields": {"created_at": "2023-01-18T13:22:51.024Z", "updated_at": "2023-01-18T13:22:51.024Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "READY_FOR_EXPORT_NO_DEAL", "name_de": "Nullabschluss beauftragt", "description_de": "FICO entscheidet, dass Geschäft nicht abgeschlossen wird und Daten archiviert werden sollen.", "name_fr": "Demande transmise pour archivage en tant que \"Dossier sans suite\"", "description_fr": "FICO décide que les affaires ne seront pas fermées et que les données doivent être archivées.", "name_it": "Chiusura zero commissionata", "description_it": "FICO decide che l'attività non sarà chiusa e i dati dovrebbero essere archiviati.", "name_en": "Ready for export no deal", "description_en": "FICO decides that business will not be closed and data should be archived.", "color": "#D7D7D7", "order": 13}}, {"model": "statemgmt.status", "pk": "8819cc57-d413-40cb-839a-de49c9314d7c", "fields": {"created_at": "2023-01-18T13:22:51.015Z", "updated_at": "2023-01-18T13:22:51.015Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "READY_FOR_CREDIT_OFFICE", "name_de": "Kreditprüfung beauftragt", "description_de": "FICO reicht Dossier weiter an Kreditprüfung (Inbox Kreditprüfung).", "name_fr": "Demande tranmise au contrôle d'entrée", "description_fr": "FICO soumet le dossier à la vérification de crédit de la boîte de réception.", "name_it": "Controllo del credito commissionato", "description_it": "FICO sottopone il dossier al controllo del credito della casella di posta.", "name_en": "Ready for credit office", "description_en": "FICO submits dossier to inbox credit check.", "color": "#FEE0C2", "order": 6}}, {"model": "statemgmt.status", "pk": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9", "fields": {"created_at": "2023-01-18T13:22:51.018Z", "updated_at": "2023-01-18T13:22:51.018Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "READY_FOR_BACK_OFFICE", "name_de": "Verarbeitung beauftragt", "description_de": "FICO reicht Dossier weiter an Verarbeitung (Inbox Verarbeitung).", "name_fr": "Demande transmise pour traitement", "description_fr": "FICO transmet le dossier au traitement (traitement de la boîte de réception).", "name_it": "Elaborazione commissionata", "description_it": "FICO passa il dossier all'elaborazione (elaborazione della posta in arrivo).", "name_en": "Ready for back office", "description_en": "FICO passes dossier to processing (inbox processing).", "color": "#D9D8B5", "order": 8}}, {"model": "statemgmt.status", "pk": "d92571e5-b99a-4211-bd20-6641cfc0b4c2", "fields": {"created_at": "2023-01-18T13:22:51.014Z", "updated_at": "2023-01-18T13:22:51.014Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "IN_FRONT_OFFICE", "name_de": "Dossier bei FICO", "description_de": "FICO erstellt und komplettiert das Dossier.", "name_fr": "Dossier chez le FICO", "description_fr": "FICO prépare et complète le dossier.", "name_it": "Dossier presso FICO", "description_it": "FICO prepara e completa il dossier.", "name_en": "In front office", "description_en": "FICO prepares and completes the dossier.", "color": "#D1F1FB", "order": 5}}, {"model": "statemgmt.status", "pk": "faade5fe-830e-437d-8b1e-8c4890497b29", "fields": {"created_at": "2023-01-18T13:22:51.026Z", "updated_at": "2023-01-18T13:22:51.026Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "key": "EXPORT_ERROR", "name_de": "Archivierung fehlerhaft", "description_de": "Während der Archivierung ist ein Fehler aufgetreten.", "name_fr": "Archivage incorrect", "description_fr": "Une erreur s'est produite lors de l'archivage.", "name_it": "Archiviazione non corretta", "description_it": "Si è verificato un errore durante l'archiviazione.", "name_en": "Export error", "description_en": "An error occurred during archiving.", "color": "#F2D2D2", "order": 17}}, {"model": "statemgmt.statetransition", "pk": "0797e761-1126-4dc0-8950-32350da9b74a", "fields": {"created_at": "2023-01-18T13:22:51.045Z", "updated_at": "2023-01-18T13:22:51.045Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "4da2ffca-cdb7-4457-8a5a-e11deaf8660f", "to_state": "68a9d5c2-b46f-4d5e-a30d-b69e3226b2bf"}}, {"model": "statemgmt.statetransition", "pk": "0abed97b-565e-45d8-bf58-ef86a7e03c68", "fields": {"created_at": "2023-01-18T13:22:51.052Z", "updated_at": "2023-01-18T13:22:51.052Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2", "to_state": "8819cc57-d413-40cb-839a-de49c9314d7c"}}, {"model": "statemgmt.statetransition", "pk": "1862f84b-f3a6-4480-b281-4f6a2463f04f", "fields": {"created_at": "2023-01-18T13:22:51.037Z", "updated_at": "2023-01-18T13:22:51.037Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "1448db7e-f622-48d9-aa91-f15663fc0ec8", "to_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2"}}, {"model": "statemgmt.statetransition", "pk": "19591932-47dd-4099-968a-b496d5bf9675", "fields": {"created_at": "2023-01-18T13:22:51.041Z", "updated_at": "2023-01-18T13:22:51.041Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "69f4108d-22fa-49f4-90ee-7eb77a0487f6", "to_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2"}}, {"model": "statemgmt.statetransition", "pk": "1f5aeccf-66fd-4d2a-9a88-c845163d7162", "fields": {"created_at": "2023-01-18T13:22:51.036Z", "updated_at": "2023-01-18T13:22:51.036Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "8819cc57-d413-40cb-839a-de49c9314d7c", "to_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9"}}, {"model": "statemgmt.statetransition", "pk": "265c900a-9c5c-411f-aa2a-86bdfdebb3c3", "fields": {"created_at": "2023-01-18T13:22:51.057Z", "updated_at": "2023-01-18T13:22:51.057Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9", "to_state": "8819cc57-d413-40cb-839a-de49c9314d7c"}}, {"model": "statemgmt.statetransition", "pk": "371d009b-eea1-4d27-8b59-635b816a835c", "fields": {"created_at": "2023-01-18T13:22:51.043Z", "updated_at": "2023-01-18T13:22:51.043Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "69f4108d-22fa-49f4-90ee-7eb77a0487f6", "to_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9"}}, {"model": "statemgmt.statetransition", "pk": "3777c5bd-1f60-4a1a-a28a-6107b579cb95", "fields": {"created_at": "2023-01-18T13:22:51.040Z", "updated_at": "2023-01-18T13:22:51.040Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9", "to_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9"}}, {"model": "statemgmt.statetransition", "pk": "3c39c579-023a-49cd-9d2b-68983b89d47c", "fields": {"created_at": "2023-01-18T13:22:51.063Z", "updated_at": "2023-01-18T13:22:51.063Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9", "to_state": "1448db7e-f622-48d9-aa91-f15663fc0ec8"}}, {"model": "statemgmt.statetransition", "pk": "5ad67009-bbeb-4c05-a926-e70e97e3437e", "fields": {"created_at": "2023-01-18T13:22:51.059Z", "updated_at": "2023-01-18T13:22:51.059Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "1448db7e-f622-48d9-aa91-f15663fc0ec8", "to_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9"}}, {"model": "statemgmt.statetransition", "pk": "5ae75580-10c0-4345-bdf6-7a04699fe21f", "fields": {"created_at": "2023-01-18T13:22:51.069Z", "updated_at": "2023-01-18T13:22:51.069Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2", "to_state": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9"}}, {"model": "statemgmt.statetransition", "pk": "60f181a7-ee96-45a7-b7f3-a5f7016d51a6", "fields": {"created_at": "2023-01-18T13:22:51.038Z", "updated_at": "2023-01-18T13:22:51.038Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "1448db7e-f622-48d9-aa91-f15663fc0ec8", "to_state": "8819cc57-d413-40cb-839a-de49c9314d7c"}}, {"model": "statemgmt.statetransition", "pk": "65a7075c-9a17-4566-ad8c-9831a119e207", "fields": {"created_at": "2023-01-18T13:22:51.065Z", "updated_at": "2023-01-18T13:22:51.065Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "68a9d5c2-b46f-4d5e-a30d-b69e3226b2bf", "to_state": "03342919-52d9-4234-85ec-cffd1db390f8"}}, {"model": "statemgmt.statetransition", "pk": "67f2e11d-c74d-43e5-92bd-b3a46f072405", "fields": {"created_at": "2023-01-18T13:22:51.033Z", "updated_at": "2023-01-18T13:22:51.033Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "faade5fe-830e-437d-8b1e-8c4890497b29", "to_state": "03342919-52d9-4234-85ec-cffd1db390f8"}}, {"model": "statemgmt.statetransition", "pk": "6aae00f6-ed6c-43cb-9477-c30fa9564c1b", "fields": {"created_at": "2023-01-18T13:22:51.068Z", "updated_at": "2023-01-18T13:22:51.068Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9", "to_state": "69f4108d-22fa-49f4-90ee-7eb77a0487f6"}}, {"model": "statemgmt.statetransition", "pk": "851fa51f-578d-4727-bdd1-d9c96ea3c922", "fields": {"created_at": "2023-01-18T13:22:51.031Z", "updated_at": "2023-01-18T13:22:51.031Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "68a9d5c2-b46f-4d5e-a30d-b69e3226b2bf", "to_state": "faade5fe-830e-437d-8b1e-8c4890497b29"}}, {"model": "statemgmt.statetransition", "pk": "8fc21c84-0b1b-4e6e-9bbf-e6de3cfdf223", "fields": {"created_at": "2023-01-18T13:22:51.072Z", "updated_at": "2023-01-18T13:22:51.072Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "69f4108d-22fa-49f4-90ee-7eb77a0487f6", "to_state": "4da2ffca-cdb7-4457-8a5a-e11deaf8660f"}}, {"model": "statemgmt.statetransition", "pk": "93472d09-9fbe-454f-8e92-56e58b53c7c7", "fields": {"created_at": "2023-01-18T13:22:51.034Z", "updated_at": "2023-01-18T13:22:51.034Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "8819cc57-d413-40cb-839a-de49c9314d7c", "to_state": "1448db7e-f622-48d9-aa91-f15663fc0ec8"}}, {"model": "statemgmt.statetransition", "pk": "9daf277b-d319-46b2-92aa-fc5b9d930c40", "fields": {"created_at": "2023-01-18T13:22:51.049Z", "updated_at": "2023-01-18T13:22:51.049Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9", "to_state": "69f4108d-22fa-49f4-90ee-7eb77a0487f6"}}, {"model": "statemgmt.statetransition", "pk": "aa097569-9950-4120-9f70-89fbf423b9a7", "fields": {"created_at": "2023-01-18T13:22:51.051Z", "updated_at": "2023-01-18T13:22:51.051Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9", "to_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2"}}, {"model": "statemgmt.statetransition", "pk": "b3827800-6171-44b5-9aa6-b48785c0d573", "fields": {"created_at": "2023-01-18T13:22:51.061Z", "updated_at": "2023-01-18T13:22:51.061Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "4da2ffca-cdb7-4457-8a5a-e11deaf8660f", "to_state": "69f4108d-22fa-49f4-90ee-7eb77a0487f6"}}, {"model": "statemgmt.statetransition", "pk": "d0765073-4d66-4046-8ab4-5db792c08048", "fields": {"created_at": "2023-01-18T13:22:51.055Z", "updated_at": "2023-01-18T13:22:51.055Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "8819cc57-d413-40cb-839a-de49c9314d7c", "to_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2"}}, {"model": "statemgmt.statetransition", "pk": "d407d463-aaa8-45cf-ba22-93c3c7215a97", "fields": {"created_at": "2023-01-18T13:22:51.070Z", "updated_at": "2023-01-18T13:22:51.070Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "1448db7e-f622-48d9-aa91-f15663fc0ec8", "to_state": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9"}}, {"model": "statemgmt.statetransition", "pk": "d5cb3438-4a69-4f59-8ea9-773f8706ff3d", "fields": {"created_at": "2023-01-18T13:22:51.032Z", "updated_at": "2023-01-18T13:22:51.032Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "69f4108d-22fa-49f4-90ee-7eb77a0487f6", "to_state": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9"}}, {"model": "statemgmt.statetransition", "pk": "da8897ab-2465-438c-be74-e5e71a6f992c", "fields": {"created_at": "2023-01-18T13:22:51.054Z", "updated_at": "2023-01-18T13:22:51.054Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9", "to_state": "68a9d5c2-b46f-4d5e-a30d-b69e3226b2bf"}}, {"model": "statemgmt.statetransition", "pk": "f48cdac4-b73d-4e3d-bcee-844013381556", "fields": {"created_at": "2023-01-18T13:22:51.058Z", "updated_at": "2023-01-18T13:22:51.058Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9", "to_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2"}}, {"model": "statemgmt.statetransition", "pk": "f758505c-d146-41cd-bb53-ab3020e35d49", "fields": {"created_at": "2023-01-18T13:22:51.066Z", "updated_at": "2023-01-18T13:22:51.066Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "d92571e5-b99a-4211-bd20-6641cfc0b4c2", "to_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9"}}, {"model": "statemgmt.statetransition", "pk": "fbe5f61b-0ec4-40bf-8e87-6b3f1a490c76", "fields": {"created_at": "2023-01-18T13:22:51.047Z", "updated_at": "2023-01-18T13:22:51.047Z", "state_machine": "94c0547a-331b-42c2-8e31-128a7645816c", "from_state": "7993f480-5f32-4d2e-9c2f-8cef310502d9", "to_state": "b754fd51-5af8-43ca-9d45-8f7f6c32ade9"}}, {"model": "statemgmt.transitionprecondition", "pk": "0fb7e128-f51d-46e3-9824-c290e2298a44", "fields": {"created_at": "2023-01-18T13:22:51.113Z", "updated_at": "2023-01-18T13:22:51.113Z", "transition": "b3827800-6171-44b5-9aa6-b48785c0d573", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "223c8f45-3f4a-484e-a24b-2f3338e6e69d", "fields": {"created_at": "2023-01-18T13:22:51.121Z", "updated_at": "2023-01-18T13:22:51.121Z", "transition": "fbe5f61b-0ec4-40bf-8e87-6b3f1a490c76", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "2347bc08-065f-4c5e-8c53-5deabfa29da1", "fields": {"created_at": "2023-01-18T13:22:51.092Z", "updated_at": "2023-01-18T13:22:51.092Z", "transition": "60f181a7-ee96-45a7-b7f3-a5f7016d51a6", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "235dde58-d744-4bff-9cef-06ba81180d15", "fields": {"created_at": "2023-01-18T13:22:51.084Z", "updated_at": "2023-01-18T13:22:51.084Z", "transition": "d407d463-aaa8-45cf-ba22-93c3c7215a97", "condition": "52521ab4-5e49-4c12-bcc3-58ba5503159e", "show_anyway": true, "overrideable": true, "warning_title_de": "Geschäfts-Nr. fehlt", "warning_title_en": "Business no. missing", "warning_title_fr": "No. de transaction manquant", "warning_title_it": "Numero di affari mancante", "warning_de": "Die Geschäfts-Nr. muss konsistent mit KBUS gesetzt sein. Bitte in den Dossier-Eigenschaften oder im Tab \"Archivierung\" zuweisen.", "warning_en": "The transaction no. must be set consistently with KBUS. Please assign in the dossier properties or in the \"Archiving\" tab.", "warning_fr": "Le numéro de transaction doit être défini de manière cohérente avec KBUS. Veuillez l'attribuer dans les propriétés du dossier ou dans l'onglet \"Archivage\".", "warning_it": "Il numero di transazione deve essere impostato in modo coerente con KBUS. Assegnare nelle proprietà del dossier o nella scheda \"Archiviazione\"."}}, {"model": "statemgmt.transitionprecondition", "pk": "29fd771b-9e1a-434e-a61c-017e3a3d8f8c", "fields": {"created_at": "2023-01-18T13:22:51.099Z", "updated_at": "2023-01-18T13:22:51.099Z", "transition": "8fc21c84-0b1b-4e6e-9bbf-e6de3cfdf223", "condition": "af0e5ed2-0918-428a-b909-2acc88802361", "show_anyway": true, "overrideable": false, "warning_title_de": "Deckungen nicht zugewiesen", "warning_title_en": "Coverages not assigned", "warning_title_fr": "Couvertures non attribuées", "warning_title_it": "Coperture non assegnate", "warning_de": "Die für die Archivierung verlangte Deckungszuweisung ist noch nicht abgeschlossen. Bitte im Tab \"Archivierung\" die Liste der geforderten Deckungen überprüfen.", "warning_en": "The coverage assignment required for archiving has not yet been completed. Please check the list of required coverages in the \"Archiving\" tab.", "warning_fr": "L'attribution de la couverture demandée pour l'archivage n'est pas encore terminée. Veuillez vérifier la liste des couvertures exigées dans l'onglet \"Archivage\".", "warning_it": "L'incarico di copertura richiesto per l'archiviazione non è ancora stato completato. Controllare l'elenco delle coperture richieste nella scheda \"Archiviazione\"."}}, {"model": "statemgmt.transitionprecondition", "pk": "38aa8db4-e8ee-4f92-8848-53f1bd3708a9", "fields": {"created_at": "2023-01-18T13:22:51.118Z", "updated_at": "2023-01-18T13:22:51.118Z", "transition": "0797e761-1126-4dc0-8950-32350da9b74a", "condition": "638e61c2-7f6d-45a7-a76c-cfe2f64092d9", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "45c1381b-a4f5-4d3e-a96a-3d4830453cf4", "fields": {"created_at": "2023-01-18T13:22:51.116Z", "updated_at": "2023-01-18T13:22:51.116Z", "transition": "da8897ab-2465-438c-be74-e5e71a6f992c", "condition": "638e61c2-7f6d-45a7-a76c-cfe2f64092d9", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "461ae617-d1ac-48a4-978f-c24913baf715", "fields": {"created_at": "2023-01-18T13:22:51.112Z", "updated_at": "2023-01-18T13:22:51.112Z", "transition": "65a7075c-9a17-4566-ad8c-9831a119e207", "condition": "638e61c2-7f6d-45a7-a76c-cfe2f64092d9", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "4fabe809-d927-426d-9274-28d19c084691", "fields": {"created_at": "2023-01-18T13:22:51.095Z", "updated_at": "2023-01-18T13:22:51.095Z", "transition": "5ad67009-bbeb-4c05-a926-e70e97e3437e", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "5c1d817e-81bb-4376-a494-3e53af02bdfa", "fields": {"created_at": "2023-01-18T13:22:51.131Z", "updated_at": "2023-01-18T13:22:51.131Z", "transition": "3777c5bd-1f60-4a1a-a28a-6107b579cb95", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "60824b3f-c9d2-47d3-b4f3-0f35f4e77e0a", "fields": {"created_at": "2023-01-18T13:22:51.076Z", "updated_at": "2023-01-18T13:22:51.076Z", "transition": "5ae75580-10c0-4345-bdf6-7a04699fe21f", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": "", "warning_title_en": "", "warning_title_fr": "", "warning_title_it": "", "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "6412aa50-aef4-4fbd-bf46-7d6342e5f8a2", "fields": {"created_at": "2023-01-18T13:22:51.085Z", "updated_at": "2023-01-18T13:22:51.086Z", "transition": "d407d463-aaa8-45cf-ba22-93c3c7215a97", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "66868cbb-e7cd-44ff-a404-f7c8dd78e880", "fields": {"created_at": "2023-01-18T13:22:51.103Z", "updated_at": "2023-01-18T13:22:51.103Z", "transition": "6aae00f6-ed6c-43cb-9477-c30fa9564c1b", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "66bf3686-60b9-47a9-8879-f43b7a54bcd5", "fields": {"created_at": "2023-01-18T13:22:51.123Z", "updated_at": "2023-01-18T13:22:51.123Z", "transition": "5ae75580-10c0-4345-bdf6-7a04699fe21f", "condition": "52521ab4-5e49-4c12-bcc3-58ba5503159e", "show_anyway": true, "overrideable": true, "warning_title_de": "Geschäfts-Nr. fehlt", "warning_title_en": "Business no. missing", "warning_title_fr": "No. de transaction manquant", "warning_title_it": "Numero di affari mancante", "warning_de": "Die Geschäfts-Nr. muss konsistent mit KBUS gesetzt sein. Bitte in den Dossier-Eigenschaften oder im Tab \"Archivierung\" zuweisen.", "warning_en": "The transaction no. must be set consistently with KBUS. Please assign in the dossier properties or in the \"Archiving\" tab.", "warning_fr": "Le numéro de transaction doit être défini de manière cohérente avec KBUS. Veuillez l'attribuer dans les propriétés du dossier ou dans l'onglet \"Archivage\".", "warning_it": "Il numero di transazione deve essere impostato in modo coerente con KBUS. Assegnare nelle proprietà del dossier o nella scheda \"Archiviazione\"."}}, {"model": "statemgmt.transitionprecondition", "pk": "684c3ca0-9e2c-48a2-b556-50ee107b8b9e", "fields": {"created_at": "2023-01-18T13:22:51.125Z", "updated_at": "2023-01-18T13:22:51.125Z", "transition": "851fa51f-578d-4727-bdd1-d9c96ea3c922", "condition": "638e61c2-7f6d-45a7-a76c-cfe2f64092d9", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "6e23f398-7fdc-4921-8b87-bcf88d8531e3", "fields": {"created_at": "2023-01-18T13:22:51.106Z", "updated_at": "2023-01-18T13:22:51.106Z", "transition": "d5cb3438-4a69-4f59-8ea9-773f8706ff3d", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "6e69661b-c728-4e02-8404-694734f63f15", "fields": {"created_at": "2023-01-18T13:22:51.126Z", "updated_at": "2023-01-18T13:22:51.126Z", "transition": "93472d09-9fbe-454f-8e92-56e58b53c7c7", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "6ef4fb08-8243-4e89-9a9c-97a8c4f920e2", "fields": {"created_at": "2023-01-18T13:22:51.083Z", "updated_at": "2023-01-18T13:22:51.083Z", "transition": "5ae75580-10c0-4345-bdf6-7a04699fe21f", "condition": "7186234f-32f2-46e7-aa30-4cab742efcbb", "show_anyway": true, "overrideable": true, "warning_title_de": "Dokumente nicht zugewiesen", "warning_title_en": "Documents not assigned", "warning_title_fr": "Documents non attribués", "warning_title_it": "Documenti non assegnati", "warning_de": "Jedem Dokument muss eine Dokumentenkategorie mit EKD-Nr. zugewiesen werden. Bitte Dokumente ohne EKD-Nr. dazu umbenennen oder löschen.", "warning_en": "A document category with EKD no. must be assigned to each document. Please rename or delete documents without EKD-No. for this purpose.", "warning_fr": "Une catégorie de document avec le no. EKD doit être attribuée à chaque document. Veuillez renommer ou supprimer les documents sans no. EKD.", "warning_it": "A ogni documento deve essere assegnata una categoria di documento con il numero EKD. Si prega di rinominare o cancellare i documenti senza il numero EKD a questo scopo."}}, {"model": "statemgmt.transitionprecondition", "pk": "701932b8-bc1c-487b-a3cd-ce1ff2336844", "fields": {"created_at": "2023-01-18T13:22:51.074Z", "updated_at": "2023-01-18T13:22:51.074Z", "transition": "5ae75580-10c0-4345-bdf6-7a04699fe21f", "condition": "326fa9bc-7b5f-4233-8d33-3d8abdc31b6b", "show_anyway": true, "overrideable": true, "warning_title_de": "Dokumente nicht vollständig", "warning_title_en": "Documents not complete", "warning_title_fr": "Documents incomplets", "warning_title_it": "Documenti non completi", "warning_de": "Das Dossier beinhaltet nicht alle gemäss Geschäftsfalltyp geforderten Dokumente. Bitte im Tab \"Vollständigkeit\" die Liste der geforderten Dokumente überprüfen.", "warning_en": "The dossier does not contain all documents required according to the business case type. Please check the list of required documents in the \"Completeness\" tab.", "warning_fr": "Le dossier ne contient pas tous les documents exigés par le type de transaction. Veuillez vérifier la liste des documents exigés dans l'onglet \"Intégralité\".", "warning_it": "Il dossier non contiene tutti i documenti richiesti in base al tipo di business case. Controllare l'elenco dei documenti richiesti nella scheda \"Completezza\"."}}, {"model": "statemgmt.transitionprecondition", "pk": "82b28e6e-df23-46d5-9d96-2bd4f0f2a338", "fields": {"created_at": "2023-01-18T13:22:51.090Z", "updated_at": "2023-01-18T13:22:51.090Z", "transition": "8fc21c84-0b1b-4e6e-9bbf-e6de3cfdf223", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": "", "warning_title_en": "", "warning_title_fr": "", "warning_title_it": "", "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "8772cd0a-8aa9-473b-b9d6-e84e3360498a", "fields": {"created_at": "2023-01-18T13:22:51.129Z", "updated_at": "2023-01-18T13:22:51.129Z", "transition": "d0765073-4d66-4046-8ab4-5db792c08048", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "89429cbc-6081-44bc-92e3-7206f3c97d9c", "fields": {"created_at": "2023-01-18T13:22:51.093Z", "updated_at": "2023-01-18T13:22:51.093Z", "transition": "f48cdac4-b73d-4e3d-bcee-844013381556", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "9412872e-c0ab-4bb8-8af8-0248b03fd3bc", "fields": {"created_at": "2023-01-18T13:22:51.128Z", "updated_at": "2023-01-18T13:22:51.128Z", "transition": "8fc21c84-0b1b-4e6e-9bbf-e6de3cfdf223", "condition": "52521ab4-5e49-4c12-bcc3-58ba5503159e", "show_anyway": true, "overrideable": false, "warning_title_de": "Geschäfts-Nr. fehlt", "warning_title_en": "Business no. missing", "warning_title_fr": "No. de transaction manquant", "warning_title_it": "Numero di affari mancante", "warning_de": "Die Geschäfts-Nr. muss konsistent mit KBUS gesetzt sein. Bitte in den Dossier-Eigenschaften oder im Tab \"Archivierung\" zuweisen.", "warning_en": "The transaction no. must be set consistently with KBUS. Please assign in the dossier properties or in the \"Archiving\" tab.", "warning_fr": "Le numéro de transaction doit être défini de manière cohérente avec KBUS. Veuillez l'attribuer dans les propriétés du dossier ou dans l'onglet \"Archivage\".", "warning_it": "Il numero di transazione deve essere impostato in modo coerente con KBUS. Assegnare nelle proprietà del dossier o nella scheda \"Archiviazione\"."}}, {"model": "statemgmt.transitionprecondition", "pk": "94e895a3-b820-42e2-ac90-238d7378f88d", "fields": {"created_at": "2023-01-18T13:22:51.096Z", "updated_at": "2023-01-18T13:22:51.096Z", "transition": "3c39c579-023a-49cd-9d2b-68983b89d47c", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "99b19084-3977-418a-b777-0a8860de580f", "fields": {"created_at": "2023-01-18T13:22:51.115Z", "updated_at": "2023-01-18T13:22:51.115Z", "transition": "1862f84b-f3a6-4480-b281-4f6a2463f04f", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "99d06ed7-b950-4259-a63f-951aab6fc9e9", "fields": {"created_at": "2023-01-18T13:22:51.079Z", "updated_at": "2023-01-18T13:22:51.079Z", "transition": "67f2e11d-c74d-43e5-92bd-b3a46f072405", "condition": "638e61c2-7f6d-45a7-a76c-cfe2f64092d9", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "9cfb3dd3-48fe-422f-8a9c-c13209b8bdea", "fields": {"created_at": "2023-01-18T13:22:51.101Z", "updated_at": "2023-01-18T13:22:51.101Z", "transition": "19591932-47dd-4099-968a-b496d5bf9675", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "9da9730b-0ab2-4202-b503-af6aa4c7e677", "fields": {"created_at": "2023-01-18T13:22:51.120Z", "updated_at": "2023-01-18T13:22:51.120Z", "transition": "8fc21c84-0b1b-4e6e-9bbf-e6de3cfdf223", "condition": "7186234f-32f2-46e7-aa30-4cab742efcbb", "show_anyway": true, "overrideable": false, "warning_title_de": "Dokumente nicht zugewiesen", "warning_title_en": "Documents not assigned", "warning_title_fr": "Documents non attribués", "warning_title_it": "Documenti non assegnati", "warning_de": "Jedem Dokument muss eine Dokumentenkategorie mit EKD-Nr. zugewiesen werden. Bitte Dokumente ohne EKD-Nr. dazu umbenennen oder löschen.", "warning_en": "A document category with EKD no. must be assigned to each document. Please rename or delete documents without EKD-No. for this purpose.", "warning_fr": "Une catégorie de document avec le no. EKD doit être attribuée à chaque document. Veuillez renommer ou supprimer les documents sans no. EKD.", "warning_it": "A ogni documento deve essere assegnata una categoria di documento con il numero EKD. Si prega di rinominare o cancellare i documenti senza il numero EKD a questo scopo."}}, {"model": "statemgmt.transitionprecondition", "pk": "b3f5ad9c-de17-47e8-ac2f-b78a891fc706", "fields": {"created_at": "2023-01-18T13:22:51.110Z", "updated_at": "2023-01-18T13:22:51.110Z", "transition": "1f5aeccf-66fd-4d2a-9a88-c845163d7162", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "bba4731d-95eb-4a85-8349-367ae9cbd3e3", "fields": {"created_at": "2023-01-18T13:22:51.077Z", "updated_at": "2023-01-18T13:22:51.077Z", "transition": "8fc21c84-0b1b-4e6e-9bbf-e6de3cfdf223", "condition": "326fa9bc-7b5f-4233-8d33-3d8abdc31b6b", "show_anyway": true, "overrideable": true, "warning_title_de": "Dokumente nicht vollständig", "warning_title_en": "Documents not complete", "warning_title_fr": "Documents incomplets", "warning_title_it": "Documenti non completi", "warning_de": "Das Dossier beinhaltet nicht alle gemäss Geschäftsfalltyp geforderten Dokumente. Bitte im Tab \"Vollständigkeit\" die Liste der geforderten Dokumente überprüfen.", "warning_en": "The dossier does not contain all documents required according to the business case type. Please check the list of required documents in the \"Completeness\" tab.", "warning_fr": "Le dossier ne contient pas tous les documents exigés par le type de transaction. Veuillez vérifier la liste des documents exigés dans l'onglet \"Intégralité\".", "warning_it": "Il dossier non contiene tutti i documenti richiesti in base al tipo di business case. Controllare l'elenco dei documenti richiesti nella scheda \"Completezza\"."}}, {"model": "statemgmt.transitionprecondition", "pk": "c0514b34-9e0c-43ab-a84d-17331237efdc", "fields": {"created_at": "2023-01-18T13:22:51.087Z", "updated_at": "2023-01-18T13:22:51.087Z", "transition": "371d009b-eea1-4d27-8b59-635b816a835c", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "cb54da48-e248-4fc8-afd1-ee8fbc53373c", "fields": {"created_at": "2023-01-18T13:22:51.089Z", "updated_at": "2023-01-18T13:22:51.089Z", "transition": "9daf277b-d319-46b2-92aa-fc5b9d930c40", "condition": "638e61c2-7f6d-45a7-a76c-cfe2f64092d9", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "d09c1a4e-603d-4834-80a7-97b03c1c3738", "fields": {"created_at": "2023-01-18T13:22:51.104Z", "updated_at": "2023-01-18T13:22:51.104Z", "transition": "d407d463-aaa8-45cf-ba22-93c3c7215a97", "condition": "7186234f-32f2-46e7-aa30-4cab742efcbb", "show_anyway": true, "overrideable": true, "warning_title_de": "Dokumente nicht zugewiesen", "warning_title_en": "Documents not assigned", "warning_title_fr": "Documents non attribués", "warning_title_it": "Documenti non assegnati", "warning_de": "Jedem Dokument muss eine Dokumentenkategorie mit EKD-Nr. zugewiesen werden. Bitte Dokumente ohne EKD-Nr. dazu umbenennen oder löschen.", "warning_en": "A document category with EKD no. must be assigned to each document. Please rename or delete documents without EKD-No. for this purpose.", "warning_fr": "Une catégorie de document avec le no. EKD doit être attribuée à chaque document. Veuillez renommer ou supprimer les documents sans no. EKD.", "warning_it": "A ogni documento deve essere assegnata una categoria di documento con il numero EKD. Si prega di rinominare o cancellare i documenti senza il numero EKD a questo scopo."}}, {"model": "statemgmt.transitionprecondition", "pk": "d1872bb3-1f7f-473e-853d-4f7c6d98cd9a", "fields": {"created_at": "2023-01-18T13:22:51.132Z", "updated_at": "2023-01-18T13:22:51.132Z", "transition": "265c900a-9c5c-411f-aa2a-86bdfdebb3c3", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": "", "warning_en": "", "warning_fr": "", "warning_it": ""}}, {"model": "statemgmt.transitionprecondition", "pk": "d8c94021-b6a8-45c2-86b3-d2ff1ca32903", "fields": {"created_at": "2023-01-18T13:22:51.098Z", "updated_at": "2023-01-18T13:22:51.098Z", "transition": "0abed97b-565e-45d8-bf58-ef86a7e03c68", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "dfdcbacb-1061-4590-add7-5e8d4cbc24ea", "fields": {"created_at": "2023-01-18T13:22:51.081Z", "updated_at": "2023-01-18T13:22:51.081Z", "transition": "f758505c-d146-41cd-bb53-ab3020e35d49", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}, {"model": "statemgmt.transitionprecondition", "pk": "e484da14-78a8-45aa-8576-d8689e871f29", "fields": {"created_at": "2023-01-18T13:22:51.134Z", "updated_at": "2023-01-18T13:22:51.134Z", "transition": "d407d463-aaa8-45cf-ba22-93c3c7215a97", "condition": "326fa9bc-7b5f-4233-8d33-3d8abdc31b6b", "show_anyway": true, "overrideable": true, "warning_title_de": "Dokumente nicht vollständig", "warning_title_en": "Documents not complete", "warning_title_fr": "Documents incomplets", "warning_title_it": "Documenti non completi", "warning_de": "Das Dossier beinhaltet nicht alle gemäss Geschäftsfalltyp geforderten Dokumente. Bitte im Tab \"Vollständigkeit\" die Liste der geforderten Dokumente überprüfen.", "warning_en": "The dossier does not contain all documents required according to the business case type. Please check the list of required documents in the \"Completeness\" tab.", "warning_fr": "Le dossier ne contient pas tous les documents exigés par le type de transaction. Veuillez vérifier la liste des documents exigés dans l'onglet \"Intégralité\".", "warning_it": "Il dossier non contiene tutti i documenti richiesti in base al tipo di business case. Controllare l'elenco dei documenti richiesti nella scheda \"Completezza\"."}}, {"model": "statemgmt.transitionprecondition", "pk": "f362a649-0a90-4022-a1cc-3c110944419a", "fields": {"created_at": "2023-01-18T13:22:51.108Z", "updated_at": "2023-01-18T13:22:51.108Z", "transition": "aa097569-9950-4120-9f70-89fbf423b9a7", "condition": "c13bbbe0-67da-4f53-a0f0-d0344e8045ed", "show_anyway": false, "overrideable": false, "warning_title_de": null, "warning_title_en": null, "warning_title_fr": null, "warning_title_it": null, "warning_de": null, "warning_en": null, "warning_fr": null, "warning_it": null}}]