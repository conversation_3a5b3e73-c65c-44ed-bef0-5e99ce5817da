import structlog

import pytest


from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
    create_context_for_semantic_document_state_transition,
)
from statemgmt.services import validate_state_transition, StateTransitionError

logger = structlog.get_logger(__name__)


pytestmark = pytest.mark.django_db


def test_idempotent_create_bcge_state_machine():
    # Test simply that we can run the command twice, with get or create
    create_semantic_document_state_machine()
    create_semantic_document_state_machine()


@pytest.mark.parametrize(
    "current_status, next_status, expected_success",
    [
        ("IN_FRONT_OFFICE", "READY_FOR_EXPORT", True),
        ("READY_FOR_EXPORT", "EXPORT_AVAILABLE", True),
        ("EXPORT_AVAILABLE", "EXPORT_DONE", True),
        # On error, allow for return to ready for export, to retry export function
        ("EXPORT_ERROR", "EXPORT_DONE", True),
    ]
    + [
        # All of these are expected to fail
        ("IN_FRONT_OFFICE", "EXPORT_AVAILABLE", False),
        ("READY_FOR_EXPORT", "IN_FRONT_OFFICE", False),
        ("EXPORT_AVAILABLE", "READY_FOR_EXPORT", False),
        ("READY_FOR_EXPORT", "EXPORT_ERROR", False),
        ("EXPORT_DONE", "EXPORT_AVAILABLE", False),
        ("EXPORT_ERROR", "EXPORT_AVAILABLE", False),
    ],
)
def test_transitions_success(current_status, next_status, expected_success):

    created_state_mgmt_objects = create_semantic_document_state_machine()

    states = created_state_mgmt_objects["states"]

    context = create_context_for_semantic_document_state_transition()

    if expected_success:
        validate_state_transition(
            context=context,
            current_status=states[current_status],
            next_status=states[next_status],
        )
    else:
        with pytest.raises(StateTransitionError):
            validate_state_transition(
                context=context,
                current_status=states[current_status],
                next_status=states[next_status],
            )
