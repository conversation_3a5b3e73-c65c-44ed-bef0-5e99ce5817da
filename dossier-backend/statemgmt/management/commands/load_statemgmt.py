from pathlib import Path

import djclick as click

from statemgmt.export import dump_state_machine, update_state_machine


@click.group()
def cli():
    pass


@cli.command()
@click.argument("state_machine_json_path")
@click.argument("state_machine_name")
def dump_state_machine_local(state_machine_json_path: str, state_machine_name: str):
    """
    Example:
        python manage.py load_statemgmt dump-state-machine-local statemgmt/configurations/bekb/bekb_state_machine_20240407.json bekbe
        python manage.py load_statemgmt dump-state-machine-local statemgmt/configurations/bekb/bekb_state_machine_20240420.json 'Dossier Status BEKB Initial'

        python manage.py load_statemgmt dump-state-machine-local statemgmt/configurations/dossier_default/dossier_default_state_machine_20250221.json 'Default Dossier State Machine'

        # For BEKB Fipla
        python manage.py load_statemgmt dump-state-machine-local statemgmt/configurations/bekb/fipla/bekbfipla_state_machine_20250103.json 'Dossier State Machine BEKB Fipla'

        # For clientis, bcge and finnova
        python manage.py load_statemgmt dump-state-machine-local statemgmt/configurations/default/default_state_machine_2025_02_27.json 'Default Dossier State Machine'


    @param state_machine_json_path:
    @param state_machine_name:
    @return:
    """
    dump_state_machine(state_machine_name, Path(state_machine_json_path))


@cli.command()
@click.argument("state_machine_json_path")
@click.argument("state_machine_name")
def update_state_machine_local(
    state_machine_json_path: str, state_machine_name: str = None
):
    """
    Example:

        # For BEKB
        python manage.py load_statemgmt update-state-machine-local statemgmt/configurations/bekb/bekb_state_machine_20240420.json 'Dossier Status BEKB Initial'

        # For BEKB Fipla
        python manage.py load_statemgmt update-state-machine-local statemgmt/configurations/bekb/fipla/bekbfipla_state_machine_20250103.json 'Dossier State Machine BEKB Fipla'

        # For clientis, bcge and finnova to set up the state machine for the first time
        python manage.py load_statemgmt update-state-machine-local statemgmt/configurations/default/default_state_machine_2025_02_27.json 'Default Dossier State Machine'

    @param state_machine_json_path:
    @param state_machine_name:
    @return:
    """
    update_state_machine(Path(state_machine_json_path), state_machine_name)
