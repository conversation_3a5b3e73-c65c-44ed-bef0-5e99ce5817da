import pytest
from dossier.models import account_specific_access_checks


@pytest.mark.django_db
def test_dossier_access_check_providers_exist():

    # Check loading of account_specific_access_checks dict on app startup
    assert len(account_specific_access_checks) == 6

    # Check that these are automatically created by django migration

    # assert DossierAccessCheckProvider.objects.count() == 3
    #
    # for provider_name in expected_providers:
    #     assert DossierAccessCheckProvider.objects.get(name=provider_name)
