import structlog

import djclick as click
from faker import Faker

from zkb.tests.factories import ZKBAccountFactoryFaker


logger = structlog.get_logger()


@click.command()
def load_zkb_account():
    """
    python manage.py load-zkb-account
    """
    Faker.seed(234355)
    zkb_account_factory = ZKBAccountFactoryFaker()
    zkb_account_factory.account.show_document_category_external = False
    zkb_account_factory.account.is_bekb = False
    zkb_account_factory.account.enable_real_estate_properties = True
    zkb_account_factory.account.enable_custom_semantic_document_date = True
    zkb_account_factory.account.save()

    zkb_account_factory.load_initial_document_categories()

    for i in range(10):
        zkb_account_factory.create_ready_for_export_dossier()
