{"openapi": "3.1.0", "info": {"title": "Hypodossier - ZKB API", "version": "1.13.0", "description": ""}, "paths": {"/partner/zkb/api/v1/ping": {"get": {"operationId": "zkb_api_ping", "summary": "<PERSON>", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier": {"post": {"operationId": "zkb_api_create_dossier", "summary": "Create Dossier", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDossier"}}}, "required": true}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}": {"patch": {"operationId": "zkb_api_update_dossier", "summary": "Update Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Updates a new Dossier based on the provided parameters\n\nWe provide a external_dossier_id as part of the URL and allow the client to change it\nas part of ChangeDossier", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeDossier"}}}, "required": true}, "security": [{"ZKBJWTAuth": []}]}, "delete": {"operationId": "zkb_api_delete_dossier", "summary": "Delete Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"202": {"description": "Accepted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/entities/realestateproperty": {"put": {"operationId": "zkb_api_create_or_update_real_estate_property", "summary": "Create Or Update Real Estate Property", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealEstatePropertyResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealEstatePropertyResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Create or update a new RealEstateProperty for a Dossier", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealEstatePropertyCreate"}}}, "required": true}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/entities/realestateproperties/": {"get": {"operationId": "zkb_api_get_real_estate_properties", "summary": "Get Real Estate Properties", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RealEstatePropertyResponse"}, "title": "Response", "type": "array"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Get all real estate properties assigned to a dossier", "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/original-files": {"post": {"operationId": "zkb_api_add_original_file", "summary": "Add Original File", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatedObjectReference"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Add an original file to a dossier and optionally link it to a real estate property\nIf the estate property does not exist, it will be created with the entity_key provided\nbut will be missing additional information such as title, floor, street, street_nr, zipcode, city", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"file": {"format": "binary", "title": "File", "type": "string"}, "entity_type": {"anyOf": [{"$ref": "#/components/schemas/EntityTypes"}, {"type": "null"}]}, "entity_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entity Key"}, "allow_duplicate_and_rename": {"default": false, "title": "Allow Duplicate And Rename", "type": "boolean"}}, "required": ["file"], "title": "MultiPartBodyParams", "type": "object"}}}, "required": true}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/filename/{original_filename}": {"get": {"operationId": "zkb_api_check_original_filename", "summary": "Check Original Filename", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "original_filename", "schema": {"title": "Original Filename", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}}, "description": "Check if a file with the same name already exists in the dossier. Return True if file already exists, else False", "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/file-status": {"get": {"operationId": "zkb_api_get_file_status", "summary": "Get File Status", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierProcessingStatus"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/details": {"get": {"operationId": "zkb_api_get_dossier", "summary": "Get Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/semantic-documents": {"get": {"operationId": "zkb_api_get_semantic_documents", "summary": "Get Semantic Documents", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "query", "name": "show_pages", "schema": {"default": false, "title": "Show Pages", "type": "boolean"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SemanticDocument"}, "title": "Response", "type": "array"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/semantic-document/{semantic_document_uuid}/external_dossier_id": {"get": {"operationId": "zkb_api_get_external_dossier_id_from_semantic_document", "summary": "Get External Dossier Id From Semantic Document", "parameters": [{"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"303": {"description": "See Other", "content": {"application/json": {"schema": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Response", "type": "string"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDossierExport"}}}}}, "description": "Get a dossier external id from a semantic document uuid", "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}": {"patch": {"operationId": "zkb_api_update_semantic_document", "summary": "Update Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"format": "uuid", "title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocument"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Update the external_semantic_document_id and/or\naccess_mode for a semantic document using a semantic_document_uuid", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocumentUpdate"}}}, "required": true}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/document-categories": {"get": {"operationId": "zkb_api_get_document_categories", "summary": "Get Document Categories", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentCategories"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}": {"post": {"operationId": "zkb_api_export_semantic_document_pdf", "summary": "Export Semantic Document Pdf", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocumentPDFExportRequest"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/export/{semantic_document_export_request_uuid}/status": {"get": {"operationId": "zkb_api_get_available_export", "summary": "Get Available Export", "parameters": [{"in": "path", "name": "semantic_document_export_request_uuid", "schema": {"title": "Semantic Document Export Request Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportStatus"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}": {"delete": {"operationId": "zkb_api_soft_delete_semantic_document", "summary": "Soft Delete Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"format": "uuid", "title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}/restore": {"put": {"operationId": "zkb_api_undelete_semantic_document", "summary": "Undelete Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"format": "uuid", "title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavingResultWithMessage"}}}}}, "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/show-dossier": {"get": {"operationId": "zkb_api_show_dossier", "summary": "Show Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"303": {"description": "See Other", "content": {"application/json": {"schema": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Response", "type": "string"}}}}}}}, "/partner/zkb/api/v1/dossier-file/{dossier_file_uuid}": {"get": {"operationId": "zkb_api_get_dossier_file", "summary": "Get Dossier File", "parameters": [{"in": "path", "name": "dossier_file_uuid", "schema": {"format": "uuid", "title": "Dossier File <PERSON>", "type": "string"}, "required": true}], "responses": {"200": {"description": "File", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/partner/zkb/api/v1/dossiers-unchanged/{num_days_documents_unchanged}": {"get": {"operationId": "zkb_api_get_unchanged_dossiers", "summary": "Get Unchanged Dossiers", "parameters": [{"in": "path", "name": "num_days_documents_unchanged", "schema": {"title": "Num Days Documents Unchanged", "type": "number"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Dossier"}, "title": "Response", "type": "array"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Show dossiers which have remained unchanged for >= num_days_documents_unchanged.Logic has been changed in October 2024 so 'unchanged' now means 'no new original files have been uploaded'.", "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/semanticdocument/{semantic_document_uuid}/unique-page-objects": {"get": {"operationId": "zkb_api_get_semantic_document_unique_page_objects", "summary": "Get Semantic Document Unique Page Objects", "parameters": [{"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PageObjectSchema"}, "title": "Response", "type": "array"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Return page objects for a specific semantic document - for a semantic page, if multiple page objects of the same type exist (same category key), only return the first one. This way the page objects are unique per semantic page", "security": [{"ZKBJWTAuth": []}]}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/unique-page-objects": {"get": {"operationId": "zkb_api_dossier_page_objects", "summary": "Dossier Page Objects", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PageObjectSchema"}, "title": "Response", "type": "array"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Return page objects for a dossier - for a semantic page, if multiple page objects of the same type exist (same category key), only return the first one. This way the page objects are unique per semantic page", "security": [{"ZKBJWTAuth": []}]}}}, "components": {"schemas": {"Message": {"properties": {"detail": {"title": "Detail", "type": "string"}}, "required": ["detail"], "title": "Message", "type": "object"}, "Dossier": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "name": {"title": "Name", "type": "string"}}, "required": ["uuid", "external_dossier_id", "updated_at", "created_at", "name"], "title": "Dossier", "type": "object"}, "Error": {"properties": {"code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}}, "required": ["code", "message"], "title": "Error", "type": "object"}, "CreateDossier": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "name": {"maxLength": 255, "title": "Name", "type": "string"}}, "required": ["external_dossier_id", "name"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "ChangeDossier": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "name": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Name"}, "lang": {"anyOf": [{"$ref": "#/components/schemas/Language"}, {"type": "null"}]}}, "required": ["external_dossier_id"], "title": "Change<PERSON><PERSON><PERSON>", "type": "object"}, "Language": {"enum": ["de", "fr", "it", "en"], "title": "Language", "type": "string"}, "RealEstatePropertyResponse": {"properties": {"key": {"maxLength": 255, "title": "Key", "type": "string"}, "title": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Title"}, "floor": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Floor"}, "street": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Street"}, "street_nr": {"anyOf": [{"maxLength": 10, "type": "string"}, {"type": "null"}], "title": "Street Nr"}, "zipcode": {"anyOf": [{"maxLength": 60, "type": "string"}, {"type": "null"}], "title": "Zipcode"}, "city": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "City"}, "dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}}, "required": ["key", "dossier_uuid", "external_dossier_id"], "title": "RealEstatePropertyResponse", "type": "object"}, "RealEstatePropertyCreate": {"properties": {"key": {"maxLength": 255, "title": "Key", "type": "string"}, "title": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Title"}, "floor": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Floor"}, "street": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Street"}, "street_nr": {"anyOf": [{"maxLength": 10, "type": "string"}, {"type": "null"}], "title": "Street Nr"}, "zipcode": {"anyOf": [{"maxLength": 60, "type": "string"}, {"type": "null"}], "title": "Zipcode"}, "city": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "City"}}, "required": ["key"], "title": "RealEstatePropertyCreate", "type": "object"}, "CreatedObjectReference": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}}, "required": ["uuid"], "title": "CreatedObjectReference", "type": "object"}, "EntityTypes": {"enum": ["realestateproperty", ""], "title": "EntityTypes", "type": "string"}, "DossierProcessingStatus": {"properties": {"dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "external_id": {"title": "External Id", "type": "string"}, "progress": {"title": "Progress", "type": "integer"}, "original_files": {"items": {"$ref": "#/components/schemas/OriginalFile"}, "title": "Original Files", "type": "array"}}, "required": ["dossier_uuid", "external_id", "progress", "original_files"], "title": "DossierProcessingStatus", "type": "object"}, "ExtractedFile": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "path_from_original": {"title": "Path From Original", "type": "string"}, "file_name": {"title": "File Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "file_url": {"title": "File Url", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["uuid", "path_from_original", "file_name", "status", "file_url", "created_at", "updated_at"], "title": "ExtractedFile", "type": "object"}, "FileStatus": {"enum": ["processing", "error", "processed"], "title": "FileStatus", "type": "string"}, "OriginalFile": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "name": {"title": "Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "extracted_files": {"items": {"$ref": "#/components/schemas/ExtractedFile"}, "title": "Extracted Files", "type": "array"}, "file_url": {"title": "File Url", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["uuid", "name", "status", "extracted_files", "file_url", "created_at", "updated_at"], "title": "OriginalFile", "type": "object"}, "AccessMode": {"description": "Access mode of a semantic document (not the same as access mode of a dossier)", "enum": ["read_only", "read_write"], "title": "AccessMode", "type": "string"}, "Confidence": {"enum": ["certain", "high", "medium", "low"], "title": "Confidence", "type": "string"}, "SemanticDocument": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "external_semantic_document_id": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "External Semantic Document Id"}, "title": {"title": "Title", "type": "string"}, "document_category_confidence": {"$ref": "#/components/schemas/Confidence"}, "document_category_key": {"title": "Document Category Key", "type": "string"}, "semantic_pages": {"items": {"$ref": "#/components/schemas/SemanticPage"}, "title": "Semantic Pages", "type": "array"}, "entity_type": {"anyOf": [{"$ref": "#/components/schemas/EntityTypes"}, {"type": "null"}]}, "entity_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entity Key"}, "access_mode": {"allOf": [{"$ref": "#/components/schemas/AccessMode"}], "default": "read_write"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "last_change": {"default": "2024-12-12T11:17:29.415970Z", "format": "date-time", "title": "Last Change", "type": "string"}, "semantic_document_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Semantic Document Date"}}, "required": ["uuid", "title", "document_category_confidence", "document_category_key", "semantic_pages", "updated_at"], "title": "SemanticDocument", "type": "object"}, "SemanticPage": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "number": {"description": "Page number is zero based. First page has page number 0", "title": "Number", "type": "integer"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "last_change": {"default": "2024-12-12T11:17:29.414902Z", "format": "date-time", "title": "Last Change", "type": "string"}}, "required": ["uuid", "number", "updated_at"], "title": "SemanticPage", "type": "object"}, "ExportDossierExport": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}}, "required": ["external_dossier_id"], "title": "ExportDossierExport", "type": "object"}, "SemanticDocumentUpdate": {"properties": {"external_semantic_document_id": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "External Semantic Document Id"}, "access_mode": {"anyOf": [{"$ref": "#/components/schemas/AccessMode"}, {"type": "null"}]}}, "required": ["external_semantic_document_id"], "title": "SemanticDocumentUpdate", "type": "object"}, "DocumentCategories": {"additionalProperties": {"$ref": "#/components/schemas/DocumentCategory"}, "title": "DocumentCategories", "type": "object"}, "DocumentCategory": {"properties": {"key": {"title": "Key", "type": "string"}, "id": {"title": "Id", "type": "string"}, "title_de": {"title": "Title De", "type": "string"}, "description_de": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description De"}}, "required": ["key", "id", "title_de"], "title": "DocumentCategory", "type": "object"}, "SemanticDocumentPDFExportRequest": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}}, "required": ["uuid"], "title": "SemanticDocumentPDFExportRequest", "type": "object"}, "ExportProcessingStatus": {"enum": ["PROCESSING", "ERROR", "PROCESSED"], "title": "ExportProcessingStatus", "type": "string"}, "ExportStatus": {"properties": {"semantic_document_export_request_uuid": {"format": "uuid", "title": "Semantic Document Export Request Uuid", "type": "string"}, "status": {"$ref": "#/components/schemas/ExportProcessingStatus"}, "dossier_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dossier Url"}, "dossier_file_uuid": {"anyOf": [{"format": "uuid", "type": "string"}, {"type": "null"}], "title": "Dossier File <PERSON>"}, "updated_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Updated At"}}, "required": ["semantic_document_export_request_uuid", "status"], "title": "ExportStatus", "type": "object"}, "SavingResultWithMessage": {"properties": {"message": {"title": "Message", "type": "string"}}, "required": ["message"], "title": "SavingResultWithMessage", "type": "object"}, "BBox": {"properties": {"ref_width": {"title": "<PERSON><PERSON>", "type": "integer"}, "ref_height": {"title": "Ref Height", "type": "integer"}, "top": {"title": "Top", "type": "integer"}, "left": {"title": "Left", "type": "integer"}, "right": {"title": "Right", "type": "integer"}, "bottom": {"title": "Bottom", "type": "integer"}}, "required": ["ref_width", "ref_height", "top", "left", "right", "bottom"], "title": "BBox", "type": "object"}, "MultilingualTitles": {"properties": {"de": {"title": "De", "type": "string"}, "en": {"title": "En", "type": "string"}, "fr": {"title": "Fr", "type": "string"}, "it": {"title": "It", "type": "string"}}, "required": ["de", "en", "fr", "it"], "title": "MultilingualTitles", "type": "object"}, "PageObjectSchema": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "key": {"title": "Key", "type": "string"}, "titles": {"$ref": "#/components/schemas/MultilingualTitles"}, "value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Value"}, "type": {"title": "Type", "type": "string"}, "bbox": {"$ref": "#/components/schemas/BBox"}, "page_number": {"title": "Page Number", "type": "integer"}, "confidence": {"title": "Confidence", "type": "number"}, "semantic_page_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "semantic_document_uuid": {"format": "uuid", "title": "Semantic Document Uuid", "type": "string"}, "semantic_document_titles": {"$ref": "#/components/schemas/MultilingualTitles"}, "semantic_document_category_key": {"title": "Semantic Document Category Key", "type": "string"}, "semantic_document_category_id": {"title": "Semantic Document Category Id", "type": "string"}}, "required": ["uuid", "key", "titles", "type", "bbox", "page_number", "confidence", "semantic_page_uuid", "semantic_document_uuid", "semantic_document_titles", "semantic_document_category_key", "semantic_document_category_id"], "title": "PageObjectSchema", "type": "object"}}, "securitySchemes": {"ZKBJWTAuth": {"type": "http", "scheme": "bearer"}}}, "servers": []}