# Generated by Django 4.2.15 on 2024-09-10 19:05

from django.db import migrations

from dossier.dossier_access_external import DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME, DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME, \
    DAP_DEFAULT_ACCESS_GRANT_CHECK


def update_dossier_access_check_providers(apps, schema_editor):
    DossierAccessCheckProvider = apps.get_model("dossier", "DossierAccessCheckProvider")

    # Weird, sometimes this failes as name is already changed in tests
    external_check_true = DossierAccessCheckProvider.objects.filter(
        name="external check always true"
    ).first()

    if external_check_true:
        external_check_true.name = "zkb external check always true"
        external_check_true.save()

    external_check_always_false = DossierAccessCheckProvider.objects.filter(
        name="external check always false"
    ).first()

    if external_check_always_false:
        external_check_always_false.name = "zkb external check always false"
        external_check_always_false.save()

    DossierAccessCheckProvider.objects.get_or_create(
        name=DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME
    )
    DossierAccessCheckProvider.objects.get_or_create(
        name=DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME
    )
    DossierAccessCheckProvider.objects.get_or_create(
        name=DAP_DEFAULT_ACCESS_GRANT_CHECK
    )

class Migration(migrations.Migration):

    dependencies = [
        ("zkb", "0005_delete_accesscache"),
    ]

    operations = [
        migrations.RunPython(update_dossier_access_check_providers),
    ]
