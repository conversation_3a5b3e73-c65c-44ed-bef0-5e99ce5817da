from django.utils import timezone

import pytest
from jwcrypto.jwk import JW<PERSON>
from jwcrypto.jwt import JWT

from cdp.factories import sample_cdp_dossier
from cdp.models import ReturnType
from core.authentication import AuthenticatedClient
from dossier.factories import DossierFactory
from dossier.models import Account
from dossier.services import create_expiration_date
from django.conf import settings


@pytest.fixture
def sample_dossier_cdp(dossier_for_default_account):
    dossier, field_set = dossier_for_default_account
    dossier = sample_cdp_dossier(dossier, field_set)
    return dossier


@pytest.fixture
def sample_dossier_cdp_with_hints(dossier_for_default_account):
    dossier, field_set = dossier_for_default_account
    dossier = sample_cdp_dossier(dossier, field_set, hints=True)
    return dossier


@pytest.fixture
def dossier_for_default_account():
    field_set = "DefaultFieldSet"
    account = Account.objects.get(key="default")
    account.cdp_field_set = field_set
    account.save()
    return DossierFactory(account=account), field_set


@pytest.fixture
def dossier_access_token(dossier_for_default_account):
    dossier, field_set = dossier_for_default_account

    key = JWK.from_json(settings.INSTANCE_SIGNING_KEY)
    jwt = JWT(
        header={"alg": "RS256", "typ": "JWT"},
        claims={
            "iss": "dms",
            "aud": "cdp",
            "iat": int(timezone.now().timestamp()),
            "exp": create_expiration_date(minutes=10),
            "nbf": int(timezone.now().timestamp()),
            "dossier_uuid": str(dossier.uuid),
            "field_set": field_set,
        },
    )
    jwt.make_signed_token(key)
    return jwt.serialize()


@pytest.fixture
def valid_token(dossier_access_token):
    return dossier_access_token


@pytest.fixture
def expired_token(dossier_for_default_account):
    dossier, field_set = dossier_for_default_account

    key = JWK.from_json(settings.INSTANCE_SIGNING_KEY)
    jwt = JWT(
        header={"alg": "RS256", "typ": "JWT"},
        claims={
            "iss": "dms",
            "aud": "cdp",
            "iat": int(timezone.now().timestamp()),
            "exp": create_expiration_date(minutes=-10),
            "nbf": int(timezone.now().timestamp()),
            "dossier_uuid": str(dossier.uuid),
            "field_set": field_set,
        },
    )
    jwt.make_signed_token(key)
    return jwt.serialize()


@pytest.fixture
def cdp_client(dossier_access_token):
    return AuthenticatedClient(dossier_access_token)


@pytest.fixture
def return_type_keys():
    ReturnType.objects.create(key="area")
    ReturnType.objects.create(key="volume")
    ReturnType.objects.create(key="currency")
    ReturnType.objects.create(key="integer")
    ReturnType.objects.create(key="float")
    ReturnType.objects.create(key="string")
    ReturnType.objects.create(key="boolean")
    ReturnType.objects.create(key="date")
    ReturnType.objects.create(key="street")
    ReturnType.objects.create(key="zip_code")
    ReturnType.objects.create(key="city")
    ReturnType.objects.create(key="value_ratio")
