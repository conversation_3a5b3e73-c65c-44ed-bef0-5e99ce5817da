from uuid import UUID
from typing import Optional, Literal, Union, TypeAlias, List, Dict

from pydantic import BaseModel, Field, RootModel, HttpUrl


class Area(BaseModel):
    return_type: Literal["area"]
    value: float
    unit: str | None = None

    def __hash__(self):
        return hash((self.value, self.unit, self.return_type))

    def __eq__(self, other):
        if isinstance(other, Area):
            return (
                self.value == other.value
                and self.unit == other.unit
                and self.return_type == other.return_type
            )
        return False


class Volume(BaseModel):
    return_type: Literal["volume"]
    value: float
    unit: str | None = None

    def __hash__(self):
        return hash((self.value, self.unit, self.return_type))

    def __eq__(self, other):
        if isinstance(other, Volume):
            return (
                self.value == other.value
                and self.unit == other.unit
                and self.return_type == other.return_type
            )
        return False


class Currency(BaseModel):
    return_type: Literal["currency"]
    currency: str | None = None
    value: float

    def __hash__(self):
        return hash((self.value, self.currency, self.return_type))

    def __eq__(self, other):
        if isinstance(other, Currency):
            return (
                self.value == other.value
                and self.currency == other.currency
                and self.return_type == other.return_type
            )
        return False


class IntegerValue(BaseModel):
    return_type: Literal["integer"]
    value: int

    def __hash__(self):
        return hash((self.value, self.return_type))

    def __eq__(self, other):
        if isinstance(other, IntegerValue):
            return self.value == other.value and self.return_type == other.return_type
        return False


class FloatValue(BaseModel):
    return_type: Literal["float"]
    value: float

    def __hash__(self):
        return hash((self.value, self.return_type))

    def __eq__(self, other):
        if isinstance(other, FloatValue):
            return self.value == other.value and self.return_type == other.return_type
        return False


class StringValue(BaseModel):
    return_type: Literal["string"]
    value: str

    def __hash__(self):
        return hash((self.value, self.return_type))

    def __eq__(self, other):
        if isinstance(other, StringValue):
            return self.value == other.value and self.return_type == other.return_type
        return False


class BooleanValue(BaseModel):
    return_type: Literal["boolean"]
    value: bool

    def __hash__(self):
        return hash((self.value, self.return_type))

    def __eq__(self, other):
        if isinstance(other, BooleanValue):
            return self.value == other.value and self.return_type == other.return_type
        return False


class Date(BaseModel):
    return_type: Literal["date"]
    value: str

    def __hash__(self):
        return hash((self.value, self.return_type))

    def __eq__(self, other):
        if isinstance(other, Date):
            return self.value == other.value and self.return_type == other.return_type
        return False


class Hint(BaseModel):
    return_type: Literal["hint"]
    expected_return_type: str

    def __hash__(self):
        return hash(self.return_type)

    def __eq__(self, other):
        if isinstance(other, Hint):
            return self.return_type == other.return_type
        return False


class StreetDetails(BaseModel):
    street_name: str
    street_number: int | None = None
    suffix: str | None = None
    page_object_value: str | None = None


# Separate street number / Street name

#
# class StreetNumber(BaseModel):
#     return_type: Literal["street_number"]
#     value: int
#     street_details: StreetDetails
#
#
# class StreetName(BaseModel):
#     return_type: Literal["street_name"]
#     value: str
#     street_details: StreetDetails


class Street(BaseModel):
    return_type: Literal["street"]
    value: StreetDetails

    def __hash__(self):
        return hash(
            (
                self.value.street_name,
                self.value.street_number,
                self.value.suffix,
                self.return_type,
            )
        )

    def __eq__(self, other):
        if isinstance(other, Street):
            return (
                self.value.street_name == other.value.street_name
                and self.value.street_number == other.value.street_number
                and self.value.suffix == other.value.suffix
                and self.return_type == other.return_type
            )
        return False

    def __str__(self) -> str:
        """
        Example Outputs:
        - "Musterstrasse 12" (if no suffix)
        - "Musterstrasse 12a" (if suffix exists)
        """
        street_number = (
            f"{self.value.street_number}{self.value.suffix}"
            if self.value.suffix
            else str(self.value.street_number)
        )
        if self.value.street_number:
            return f"{self.value.street_name} {street_number}"
        return self.value.street_name


class ZipCode(BaseModel):
    return_type: Literal["zip_code"]
    value: int

    def __hash__(self):
        return hash((self.value, self.return_type))

    def __eq__(self, other):
        if isinstance(other, ZipCode):
            return self.value == other.value and self.return_type == other.return_type
        return False

    def __str__(self) -> str:
        return str(self.value)


class City(BaseModel):
    return_type: Literal["city"]
    value: str

    def __hash__(self):
        return hash((self.value, self.return_type))

    def __eq__(self, other):
        if isinstance(other, City):
            return self.value == other.value and self.return_type == other.return_type
        return False

    def __str__(self) -> str:
        return self.value


FieldValue: TypeAlias = Union[
    IntegerValue,
    FloatValue,
    BooleanValue,
    StringValue,
    Area,
    Volume,
    Currency,
    Date,
    Street,
    ZipCode,
    City,
    Hint,
]


class BoundingBox(BaseModel):
    ref_height: int
    ref_width: int
    top: int
    left: int
    right: int
    bottom: int


class PageObjectTitle(BaseModel):
    key: str
    de: str
    en: str
    fr: str
    it: str


class SemanticPageImageData(BaseModel):
    image_url: HttpUrl
    semantic_page_uuid: UUID
    rotation_angle: int


class RecommendationSource(BaseModel):
    image_url: str
    bbox: BoundingBox
    page_object_uuid: UUID
    semantic_page_uuid: UUID
    processed_page_uuid: UUID
    semantic_document_uuid: UUID
    dossier_uuid: UUID
    semantic_document_title: str
    semantic_document_suffix: Optional[str] = None
    document_category: str
    document_category_translated: str
    page_object_title: PageObjectTitle
    document_date: str  # ISO format : YYYY-MM-DD
    semantic_page_rotation_angle: int
    semantic_document_page_number: int
    semantic_document_page_count: int
    page_images: List[SemanticPageImageData]
    page_index_preferred: int


class HintRecommendationSource(BaseModel):
    page_images: List[SemanticPageImageData]
    page_index_preferred: int
    priority: int
    semantic_document_title: str
    semantic_document_suffix: Optional[str] = None
    document_category: str
    document_category_translated: str
    document_date: str  # ISO format : YYYY-MM-DD
    uuid: UUID


class Confidence(BaseModel):
    confidence_value: float
    confidence_level: str
    confidence_formatted: str


class RecommendationResponse(BaseModel):
    confidence: Confidence
    source: RecommendationSource
    field_value: FieldValue = Field(..., discriminator="return_type")
    priority: int


class GroupedRecommendationResponse(BaseModel):
    field_value: FieldValue = Field(..., discriminator="return_type")
    source: List[RecommendationSource]
    max_confidence: Confidence  # max confidence of all sources
    highest_priority: int  # max priority of all sources


class HintRecommendationResponse(BaseModel):
    field_value: Hint
    source: HintRecommendationSource


class PageObjectDetails(BaseModel):
    bbox: BoundingBox
    page_object_uuid: UUID
    page_object_title: PageObjectTitle
    confidence: Confidence


class SourceSemanticPageDetails(BaseModel):
    semantic_page_uuid: UUID
    processed_page_uuid: UUID
    document_category: str
    rotation_angle: int
    image_url: HttpUrl
    searchable_pdf_url: HttpUrl
    searchable_txt_url: HttpUrl
    page_objects: List[PageObjectDetails]


class SourceDocumentDetails(BaseModel):
    dossier_uuid: UUID
    semantic_document_uuid: UUID
    document_category: str
    document_category_translated: str
    semantic_document_title: str
    semantic_document_suffix: Optional[str] = None
    document_date: str  # ISO format : YYYY-MM-DD
    semantic_document_page_count: int
    page_index_to_scroll: int
    semantic_pages: List[SourceSemanticPageDetails]


class UnifiedRecommendationSource(BaseModel):
    source_document_details: List[SourceDocumentDetails]
    priority: int


class UnifiedRecommendationResponse(BaseModel):
    field_value: FieldValue = Field(..., discriminator="return_type")
    source: UnifiedRecommendationSource
    is_hint: bool


class RecommendationRequest(BaseModel):
    field_definition: str
    field_context: Optional[dict] = {}


class FieldDefinitionResponse(BaseModel):
    field_definition_key: str
    field_definition_flavour: str | None = None
    field_definition_return_type: str


class NumRecommendationRequest(BaseModel):
    field_definitions: List[str]
    field_context: Optional[dict] = {}
    group_recommendations: bool = False


class NumRecommendations(BaseModel):
    total_recommendation_count: int  # total_recommendation_count
    grouped_sppo_recommendation_count: int  # grouped_sppo_recommendation_count
    hint_recommendation_count: int
    is_single_recommendation: bool
    single_recommendation: Optional[UnifiedRecommendationResponse] = None
    field_context: Optional[dict] = {}


class NumRecommendationsResponse(RootModel):
    root: Dict[str, NumRecommendations]


class AnalyticsRequest(BaseModel):
    payload: Optional[dict] = {}
