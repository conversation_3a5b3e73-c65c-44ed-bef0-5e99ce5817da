import pytest
from django.urls import reverse

from cdp.factories import (
    sample_dossier_cdp_with_multiple_occurences,
    DocumentCategoryFactory,
)
from cdp.models import (
    FieldDefinition,
    AssignedField,
    RelevantPageObject,
    GenericPriorityMapping,
)
from cdp.schemas import RecommendationRequest
from cdp.services import get_page_images_for_sppo
from core.authentication import AuthenticatedClient
from dossier.factories import (
    SemanticDocumentFactory,
    SemanticPageFactory,
    PageObjectFactory,
    PageObjectTitleFactory,
    SemanticPagePageObjectFactory,
    AccountFactory,
)
from dossier.models import DocumentCategory as DossierDocumentCategory
from semantic_document.models import (
    SemanticDocument,
    SemanticPagePageObject,
)


@pytest.mark.django_db
def test_sample_dossier_cdp(sample_dossier_cdp):
    assert SemanticDocument.objects.filter(dossier=sample_dossier_cdp).count() == 5
    assert GenericPriorityMapping.objects.filter().count() == 5
    assert FieldDefinition.objects.filter().count() == 2
    assert AssignedField.objects.filter(field_set__key="DefaultFieldSet").count() == 2
    field_definition_objects = FieldDefinition.objects.all()
    for field_definition in field_definition_objects:
        assert field_definition.flavour == "hd"
        assert RelevantPageObject.objects.filter(
            field_definition=field_definition
        ).count() in [2, 3]


@pytest.mark.django_db
def test_post_recommendations_returns_401_when_no_token(
    testuser1_client: AuthenticatedClient, sample_dossier_cdp
):
    recommendation_request: RecommendationRequest = RecommendationRequest(
        field_definition="field_1",
        field_context={"some_key": "some_value"},
    )

    response = testuser1_client.post(
        reverse("cdp-api:recommendation-post"),
        data=recommendation_request.model_dump(),
        content_type="application/json",
    )
    assert response.status_code == 401


@pytest.mark.django_db
def test_post_recommendations_returns_200_when_token(
    cdp_client: AuthenticatedClient, sample_dossier_cdp
):
    recommendation_request = RecommendationRequest(
        field_definition="field_0",
    )
    recommendation_request_dict = recommendation_request.model_dump()

    response = cdp_client.post(
        reverse("cdp-api:recommendation-post"),
        data=recommendation_request_dict,
        content_type="application/json",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2


@pytest.mark.django_db
def test_post_recommendations_for_field0(
    cdp_client: AuthenticatedClient, sample_dossier_cdp
):
    recommendation_request = RecommendationRequest(
        field_definition="field_0",
    )
    recommendation_request_dict = recommendation_request.model_dump()

    response = cdp_client.post(
        reverse("cdp-api:recommendation-post"),
        data=recommendation_request_dict,
        content_type="application/json",
    )
    assert response.status_code == 200
    assert response.json()["count"] == 2
    response0 = response.json()["items"][0]
    assert response0["confidence"]["confidence_value"] == 0.9
    assert response0["priority"] == 1
    response1 = response.json()["items"][1]
    assert response1["confidence"]["confidence_value"] == 0.95
    assert response1["priority"] == 2


@pytest.mark.django_db
def test_post_recommendations_for_field1(
    cdp_client: AuthenticatedClient, sample_dossier_cdp
):
    recommendation_request = RecommendationRequest(
        field_definition="field_1",
    )
    recommendation_request_dict = recommendation_request.model_dump()

    response = cdp_client.post(
        reverse("cdp-api:recommendation-post"),
        data=recommendation_request_dict,
        content_type="application/json",
    )
    assert response.status_code == 200
    assert response.json()["count"] == 3
    # response0 = response.json()["items"][0]
    response0 = response.json()["items"][0]
    assert response0["confidence"]["confidence_value"] == 0.8
    assert response0["priority"] == 1
    response1 = response.json()["items"][1]
    assert response1["confidence"]["confidence_value"] == 0.95
    assert response1["priority"] == 2
    response2 = response.json()["items"][2]
    assert response2["confidence"]["confidence_value"] == 0.9
    assert response2["priority"] == 2


@pytest.fixture
def sample_dossier_cdp_mutiple_occurences(dossier_for_default_account):
    dossier, field_set = dossier_for_default_account
    dossier = sample_dossier_cdp_with_multiple_occurences(dossier, field_set)
    return dossier


@pytest.mark.django_db
def test_sample_dossier_cdp_mutiple_occurences(sample_dossier_cdp_mutiple_occurences):
    assert (
        SemanticDocument.objects.filter(
            dossier=sample_dossier_cdp_mutiple_occurences
        ).count()
        == 12
    )


@pytest.mark.django_db
def test_post_grouped_recommendations_for_field0(
    cdp_client: AuthenticatedClient, sample_dossier_cdp_mutiple_occurences
):
    recommendation_request = RecommendationRequest(
        field_definition="field_0",
    )
    recommendation_request_dict = recommendation_request.model_dump()

    response = cdp_client.post(
        reverse("cdp-api:grouped-recommendation-post"),
        data=recommendation_request_dict,
        content_type="application/json",
    )
    assert response.status_code == 200
    assert response.json()["count"] == 2
    response0 = response.json()["items"][0]
    assert len(response0["source"]["source_document_details"]) == 3
    response1 = response.json()["items"][1]
    assert len(response1["source"]["source_document_details"]) == 3


@pytest.mark.django_db
def test_post_grouped_recommendations_for_field1(
    cdp_client: AuthenticatedClient, sample_dossier_cdp_mutiple_occurences
):
    recommendation_request = RecommendationRequest(
        field_definition="field_1",
    )
    recommendation_request_dict = recommendation_request.model_dump()

    response = cdp_client.post(
        reverse("cdp-api:grouped-recommendation-post"),
        data=recommendation_request_dict,
        content_type="application/json",
    )
    assert response.status_code == 200
    assert response.json()["count"] == 3
    response0 = response.json()["items"][0]
    assert len(response0["source"]["source_document_details"]) == 2
    response1 = response.json()["items"][1]
    assert len(response1["source"]["source_document_details"]) == 2
    response2 = response.json()["items"][2]
    assert len(response2["source"]["source_document_details"]) == 2


@pytest.mark.django_db
def test_get_page_images(sample_dossier_cdp):
    sppo_for_fetch = SemanticPagePageObject.objects.filter(
        semantic_page__dossier=sample_dossier_cdp
    ).first()
    print(sppo_for_fetch)
    pages_url, preferred_idx = get_page_images_for_sppo(sppo_for_fetch)
    assert len(pages_url) == 1
    assert preferred_idx == 0


@pytest.mark.django_db
def test_get_page_images_with_multiple_occurences(dossier_for_default_account):
    dossier, field_set = dossier_for_default_account
    semantic_document = SemanticDocumentFactory(
        dossier=dossier,
        document_category=DossierDocumentCategory.objects.get_or_create(
            name=DocumentCategoryFactory().key, account=AccountFactory()
        )[0],
    )
    for i in range(1, 4):
        semantic_page = SemanticPageFactory(
            dossier=dossier, semantic_document=semantic_document, page_number=i
        )
        page_object = PageObjectFactory(
            key=PageObjectTitleFactory(),
            processed_page=semantic_page.processed_page,
        )
        sppo = SemanticPagePageObjectFactory(
            page_object=page_object, semantic_page=semantic_page
        )

    pages_url, preferred_idx = get_page_images_for_sppo(sppo)
    assert len(pages_url) == 3
    assert preferred_idx == 2


@pytest.mark.django_db
def test_post_grouped_recommendations_for_field0_with_hints(
    cdp_client: AuthenticatedClient, sample_dossier_cdp_with_hints
):
    recommendation_request = RecommendationRequest(
        field_definition="field_0",
    )
    recommendation_request_dict = recommendation_request.model_dump()

    response = cdp_client.post(
        reverse("cdp-api:grouped-recommendation-post"),
        data=recommendation_request_dict,
        content_type="application/json",
    )
    assert response.status_code == 200
    assert response.json()["count"] == 4
