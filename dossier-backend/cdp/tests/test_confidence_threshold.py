import pytest
from cdp.services import apply_confidence_threshold
from semantic_document.models import SemanticPagePageObject, PageObject


@pytest.fixture
def sppo_with_high_confidence():
    page_object = PageObject(confidence_value=0.9)
    return SemanticPagePageObject(page_object=page_object)


@pytest.fixture
def sppo_with_low_confidence():
    page_object = PageObject(confidence_value=0.4)
    return SemanticPagePageObject(page_object=page_object)


@pytest.mark.django_db
def test_confidence_threshold_filters_out_low_confidence_sppos(
    sppo_with_high_confidence, sppo_with_low_confidence
):
    sppos = [sppo_with_high_confidence, sppo_with_low_confidence]
    result = apply_confidence_threshold(sppos)
    assert len(result) == 1
    assert result[0] == sppo_with_high_confidence


@pytest.mark.django_db
def test_onfidence_threshold_keeps_high_confidence_sppos(sppo_with_high_confidence):
    sppos = [sppo_with_high_confidence]
    result = apply_confidence_threshold(sppos)
    assert len(result) == 1
    assert result[0] == sppo_with_high_confidence


@pytest.mark.django_db
def test_confidence_threshold_filters_out_all_low_confidence_sppos(
    sppo_with_low_confidence,
):
    sppos = [sppo_with_low_confidence]
    result = apply_confidence_threshold(sppos)
    assert len(result) == 0


@pytest.mark.django_db
def test_confidence_threshold_handles_empty_list():
    sppos = []
    result = apply_confidence_threshold(sppos)
    assert len(result) == 0
