from django.contrib.contenttypes.fields import GenericForeign<PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.db import models

from core.behaviors import Timestampable


# Create your models here.
class FieldSet(Timestampable):
    key = models.CharField(
        max_length=255,
        unique=True,
    )

    def __str__(self):
        return self.key


class ReturnType(Timestampable):
    key = models.CharField(unique=True)
    description = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.key


class FieldDefinition(Timestampable):
    key = models.CharField(max_length=255)  # e.g incomeP1
    flavour = models.CharField(
        max_length=255, blank=True, null=True
    )  # customer specific
    return_type = models.ForeignKey(
        ReturnType, on_delete=models.SET_NULL, blank=True, null=True
    )
    relevant_field_context = models.ManyToManyField(
        "self",
        through="RelevantFieldContext",
        symmetrical=False,
        related_name="relevant_ctx",
    )
    field_context_strategy = models.CharField(
        max_length=255, blank=True, null=True, choices=[("TXT_FILTER", "txt_filter")]
    )

    class Meta:
        unique_together = ["key", "flavour"]

    def __str__(self):
        if self.flavour is None:
            return self.key
        return f"{self.key}_{str(self.flavour)}"


class RelevantFieldContext(Timestampable):
    field_definition = models.ForeignKey(
        FieldDefinition,
        on_delete=models.CASCADE,
        related_name="field_definition",
    )
    relevant_field_definition = models.ForeignKey(
        FieldDefinition,
        on_delete=models.CASCADE,
        related_name="relevant_ctx_field_definition",
    )
    weight = models.FloatField(default=1.0)

    class Meta:
        unique_together = ["field_definition", "relevant_field_definition"]

    def __str__(self):
        return f"Context for {self.field_definition} -> {self.relevant_field_definition}:(weight {self.weight})"


class AssignedField(Timestampable):
    field_set = models.ForeignKey(FieldSet, on_delete=models.CASCADE, to_field="uuid")
    field_definition = models.ForeignKey(FieldDefinition, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.field_set} - {self.field_definition}"

    class Meta:
        unique_together = ["field_set", "field_definition"]


class PageObjectType(Timestampable):
    key = models.CharField(
        max_length=255,
        unique=True,
    )

    def __str__(self):
        return self.key


class RelevantPageObject(Timestampable):
    field_definition = models.ForeignKey(FieldDefinition, on_delete=models.CASCADE)
    page_object = models.ForeignKey(PageObjectType, on_delete=models.CASCADE)
    generic_priority_mapping = GenericRelation(
        "GenericPriorityMapping",
        related_query_name="relevant_page_object",
        content_type_field="content_type",
        object_id_field="object_id",
    )

    def __str__(self):
        return f"{self.field_definition} -> {self.page_object}"

    class Meta:
        unique_together = ["field_definition", "page_object"]


class DocumentCategory(Timestampable):
    key = models.CharField(max_length=255, unique=True)

    def __str__(self):
        return self.key

    ordering = ["key"]


class GenericPriorityMapping(Timestampable):
    # relevant_pageobject = models.ForeignKey(
    #     RelevantPageObject, on_delete=models.CASCADE
    # )
    document_category = models.ForeignKey(DocumentCategory, on_delete=models.CASCADE)
    priority = models.PositiveIntegerField(default=0, blank=False, null=False)

    # Generic Foreign Key Fields
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.UUIDField()
    relevant_object = GenericForeignKey("content_type", "object_id")

    class Meta:
        unique_together = ["document_category", "content_type", "object_id"]
        ordering = ["priority"]

    def __str__(self):
        return f"{self.relevant_object} - {self.document_category} (Priority: {self.priority})"

    @property
    def field_definition(self):
        """Dynamically retrieve the FieldDefinition associated with the relevant object."""
        if hasattr(self.relevant_object, "field_definition"):
            return self.relevant_object.field_definition
        return None


class RelevantSemanticPage(Timestampable):
    RELEVANT_SEMANTIC_PAGE_CHOICES = [
        ("SEMANTIC_PAGE", "Semantic Page"),
    ]
    field_definition = models.ForeignKey(FieldDefinition, on_delete=models.CASCADE)
    relevant_object_type = models.CharField(
        max_length=255, choices=RELEVANT_SEMANTIC_PAGE_CHOICES
    )
    generic_priority_mapping = GenericRelation(
        GenericPriorityMapping,
        related_query_name="relevant_semantic_page",
        content_type_field="content_type",
        object_id_field="object_id",
    )

    def __str__(self):
        return f"{self.field_definition} -> {self.relevant_object_type}"

    class Meta:
        unique_together = ["field_definition", "relevant_object_type"]


class RelevantSemanticDocument(Timestampable):
    RELEVANT_SEMANTIC_DOCUMENT_CHOICES = [
        ("SEMANTIC_DOCUMENT", "Semantic Document"),
    ]
    field_definition = models.ForeignKey(FieldDefinition, on_delete=models.CASCADE)
    relevant_object_type = models.CharField(
        max_length=255, choices=RELEVANT_SEMANTIC_DOCUMENT_CHOICES
    )
    generic_priority_mapping = GenericRelation(
        GenericPriorityMapping,
        related_query_name="relevant_semantic_document",
        content_type_field="content_type",
        object_id_field="object_id",
    )

    def __str__(self):
        return f"{self.field_definition} -> {self.relevant_object_type}"

    class Meta:
        unique_together = ["field_definition", "relevant_object_type"]


#
# class BasePriorityMapping(Timestampable, SortableMixin):
#     document_category = models.ForeignKey(DocumentCategory, on_delete=models.CASCADE)
#     priority = models.PositiveIntegerField(default=0)
#
#     class Meta:
#         ordering = ["priority"]
#
#     def __str__(self):
#         related_object = self.get_related_object()
#         return (
#             f"{related_object} - {self.document_category} (Priority: {self.priority})"
#         )
#
#     def get_related_object(self):
#         raise NotImplementedError("This method should be implemented by child classes.")
#
#
# class PageObjectPriorityMapping(BasePriorityMapping):
#     relevant_pageobject = models.ForeignKey(
#         RelevantPageObject, on_delete=models.CASCADE
#     )
#
#     def get_related_object(self):
#         return self.relevant_pageobject
#
#     class Meta:
#         constraints = [
#             UniqueConstraint(
#                 fields=["relevant_pageobject", "document_category"],
#                 name="unique_pageobject_priority",
#             )
#         ]
#
#
# class SemanticPagePriorityMapping(BasePriorityMapping):
#     relevant_semantic_page = models.ForeignKey(
#         RelevantSemanticPage, on_delete=models.CASCADE
#     )
#
#     class Meta:
#         constraints = [
#             UniqueConstraint(
#                 fields=["relevant_semantic_page", "document_category"],
#                 name="unique_semantic_page_priority",
#             )
#         ]
#
#     def get_related_object(self):
#         return self.relevant_semantic_page
#
#
# class SemanticDocumentPriorityMapping(BasePriorityMapping):
#     relevant_semantic_document = models.ForeignKey(
#         RelevantSemanticDocument, on_delete=models.CASCADE
#     )
#
#     class Meta:
#         constraints = [
#             UniqueConstraint(
#                 fields=["relevant_semantic_document", "document_category"],
#                 name="unique_semantic_document_priority",
#             )
#         ]
#
#     def get_related_object(self):
#         return self.relevant_semantic_document
