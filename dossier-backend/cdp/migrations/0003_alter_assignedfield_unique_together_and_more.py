# Generated by Django 4.2.15 on 2024-09-23 13:10

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("cdp", "0002_returntype_alter_fielddefinition_flavour_and_more"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="assignedfield",
            unique_together={("field_set", "field_definition")},
        ),
        migrations.AlterUniqueTogether(
            name="prioritymapping",
            unique_together={("relevant_pageobject", "document_category")},
        ),
        migrations.AlterUniqueTogether(
            name="relevantpageobject",
            unique_together={("field_definition", "page_object")},
        ),
    ]
