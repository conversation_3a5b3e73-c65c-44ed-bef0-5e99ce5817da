from typing import Optional, Callable
from uuid import UUID

import structlog
from asgiref.sync import sync_to_async
from django.db.models import Q, QuerySet
from django.shortcuts import get_object_or_404
from ninja.errors import HttpError, AuthenticationError

from dossier import jwt_extract
from dossier.models import (
    <PERSON><PERSON><PERSON>,
    DossierUser,
    account_specific_access_checks,
    annotate_with_calculated_access_mode,
    Scope,
)
from dossier.services import check_valid_delegation_exists, is_pers

logger = structlog.get_logger()


def check_account_specific_access_check(
    jwt: dict,
    dossier_uuid: UUID,
    dossier_user: Dossier<PERSON><PERSON>,
    is_manager=bool,
    dossier_qs: QuerySet = Dossier.objects,
) -> Optional[Dossier]:

    logger.info("account specific access check")

    dossier_qs = dossier_qs.filter(uuid=dossier_uuid)

    if dossier_qs.exists():
        dossier = get_object_or_404(dossier_qs, uuid=dossier_uuid)
    else:
        logger.info(f"Dossier with uuid {dossier_uuid} does not exist")
        return None

    try:
        # Loads account from jwt
        account = jwt_extract.get_account(jwt)
    except Exception as e:
        logger.exception("Error in extracting account from JWT: %s", e)
        raise e

    if account is None:
        logger.info("Error Account is none")
        return None

    if account.dossier_access_check_provider is None:
        logger.info("Account Dossier access check is none")
        return None

    is_owner = dossier_user.user == dossier.owner

    # If the user is a manager or owner, they should have access
    # explicit specification : dossier manager == god
    if is_manager or is_owner:
        return None

    dap_name = None
    try:
        dap_name = account.dossier_access_check_provider.name

        account_specific_access_check: Callable[[dict, Dossier], None] = (
            account_specific_access_checks.get(dap_name)
        )

        if account_specific_access_check is None:
            logger.error(
                "account_specific_access_check is missing",
                account=account.name,
                access_check=account.dossier_access_check_provider.name,
            )
            return None

    except Exception as e:
        logger.exception("Error getting 'account_specific_access_check': %s", e)
        raise e

    try:
        account_specific_access_check(jwt, dossier)
        return dossier
    except AuthenticationError as ae:
        logger.warning(
            f"Failed access check in check_account_specific_access_check. jwt={jwt}, dossier_uuid={dossier_uuid}, dap_name={dap_name}"
        )
        raise ae
    except Exception as e:
        logger.error(
            f"general error in check_account_specific_access_check. jwt={jwt}, dossier_uuid={dossier_uuid}"
        )
        logger.exception(
            "general error in account specific access check", exc_info=True
        )
        raise e


def get_dossier_from_request_with_access_check(
    request,
    dossier_uuid: Optional[UUID] = None,
    dossier_qs: QuerySet[Dossier] = Dossier.objects,
) -> Dossier:
    # Check based off a request whether a user has access or not
    jwt = request.jwt

    dossier = check_account_specific_access_check(
        jwt=jwt,
        dossier_uuid=dossier_uuid,
        dossier_user=request.auth,
        is_manager=request.is_manager,
        dossier_qs=dossier_qs,
    )
    if dossier is not None:
        return dossier

    logger.info("get_dossier_with_access_check")
    external_dossier_id = jwt_extract.get_external_dossier_id(jwt)
    return get_dossier_with_access_check(
        dossier_user=request.auth,
        is_manager=request.is_manager,
        dossier_uuid=dossier_uuid,
        dossier_qs=dossier_qs,
        external_dossier_id=external_dossier_id,
    )


def get_writable_dossier_from_request_with_access_check(
    request,
    dossier_uuid: Optional[UUID] = None,
    dossier_qs: QuerySet[Dossier] = Dossier.objects,
) -> Dossier:
    """
    Checks that a dossier is writable (i.e. not readonly)
    plus existing access checks

    Calculate access mode for the dossier
    Note, the default manager for dossier already does this annotation.
    We also include it here incase dossier_qs is a custom queryset from a custom manager
    When testing, no of QS counts did not increase, so it looks like django does not duplicate

    @param request:
    @param dossier_uuid:
    @param dossier_qs:
    @return: Dossier or raise Exception
    """
    dossier_qs = annotate_with_calculated_access_mode(dossier_qs, request.auth.user)
    dossier = get_dossier_from_request_with_access_check(
        request, dossier_uuid, dossier_qs
    )

    check_annotated_dossier_writable(dossier)

    return dossier


def check_annotated_dossier_writable(dossier: Dossier):
    """
    Check if the dossier is writable and raise an error if it's read-only.
    This ensures that the dossier is not modified when it should not be.
    """
    if dossier.calculated_access_mode == Scope.READ_ONLY:
        raise HttpError(403, "Dossier is read-only and cannot be modified")


@sync_to_async
def get_dossier_from_request_with_access_check_async(
    request, dossier_uuid: Optional[UUID] = None, dossier_qs: QuerySet = Dossier.objects
) -> Dossier:

    return get_dossier_from_request_with_access_check(
        request=request, dossier_uuid=dossier_uuid, dossier_qs=dossier_qs
    )


def get_dossier_with_access_check(
    dossier_user: DossierUser,
    is_manager: bool,
    dossier_uuid: Optional[UUID] = None,
    external_dossier_id: str = None,
    dossier_qs: QuerySet = Dossier.objects,
) -> Dossier:
    """
    Single place where single dossiers should be fetched from db to centralize access checks
    """

    dqs = dossier_qs

    dqs = dqs.filter(uuid=dossier_uuid) if dossier_uuid else dqs
    dqs = dqs.filter(external_id=external_dossier_id) if external_dossier_id else dqs

    if dossier_uuid is None and external_dossier_id is None:
        raise HttpError(401, "Both dossier_uuid and external_dossier_id are None")

    # Always good to check for same account
    account = dossier_user.account
    # Add select related to avoid an extra lookup during auth and easier access during async
    dqs = dqs.filter(account=account).select_related("account")

    is_a_pers_user = is_pers(dossier_user)

    if not is_a_pers_user:
        dqs = dqs.filter(
            Q(bekbdossierproperties__isnull=True) | Q(bekbdossierproperties__pers=False)
        )

    dossier = get_object_or_404(dqs)  # , **filter_kwargs

    # Now check owner or access delegation
    is_owner = dossier_user.user == dossier.owner

    if not is_manager and not is_owner:
        # Check if the current user is a delegate
        if check_valid_delegation_exists(
            dossier_user.account, dossier.owner, dossier_user.user
        ):
            pass
        else:
            # TODO: change to 403 but check with UI if it depends on the 404
            raise HttpError(404, "No permission to access this dossier")

    return dossier


def check_custom_dossier_authorization_strategy(
    account, dossier, is_manager, is_owner, access_delegation_successful
):
    if not is_manager and not is_owner and not access_delegation_successful:
        # do the zkb call here to get true or false
        # if account.dossier_authorization_strategy == ZKB_EXTERNAL_DOSSIER_AUTHORIZATION (in models.TextChoices like export_strategy)
        # TODO: call the zkb auth service if configured.
        pass

    return True
