from dossier.helpers import create_export_filename


def test_create_export_filename():
    # Test 1: Standard Filename
    assert create_export_filename("example filename") == "example_filename"

    # Test 2: Slashes and Spaces
    assert create_export_filename("example / file / name") == "example_file_name"

    # Test 3: Double spaces
    assert create_export_filename("example  filename") == "example_filename"

    # Test 4: Only Spaces
    assert create_export_filename("    ") == "__"

    # Test 5: Only <PERSON>lashes
    assert create_export_filename("/////") == "___"

    # Test 6: Empty Filename
    assert create_export_filename("") == ""

    # Test 7: Special Characters
    assert create_export_filename("file@#$") == "file@#$"

    # Test 8: Numeric Filename
    assert create_export_filename("12345") == "12345"

    # Test 9: Diacritics
    assert create_export_filename("èxámplé fílénámé") == "example_filename"

    # Test 10: Non-ASCII Characters
    assert create_export_filename("例えば") == "???"

    # Test 11: Long Filename
    long_filename = "a" * 300
    expected_output = "a" * 220 + "..." + "a" * 30
    assert create_export_filename(long_filename) == expected_output

    # Test 12: Maximum Length
    max_length_filename = "a" * 250
    assert create_export_filename(max_length_filename) == max_length_filename
