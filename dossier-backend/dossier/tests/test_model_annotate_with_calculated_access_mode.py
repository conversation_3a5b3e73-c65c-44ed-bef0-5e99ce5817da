from pathlib import Path

import pytest
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.auth import get_user_model

from dossier.dossier_access_external import create_or_update_access_grant
from dossier.models import (
    Dossier,
    annotate_with_calculated_access_mode,
    DossierAccessGrant,
    <PERSON><PERSON>,
)
from dossier.statemachine_types import DOSSIER_READ_ONLY_WORK_STATUS
from statemgmt.export import update_state_machine
from statemgmt.models import Status, StateMachine

User = get_user_model()

pytestmark = pytest.mark.django_db


@pytest.fixture
def dossier():
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    assert dossier.access_mode == Dossier.DossierAccessMode.READ_WRITE

    return dossier


@pytest.fixture
def user():
    return User.objects.get(username="<EMAIL>")


@pytest.fixture
def work_status_read_only():
    return Status.objects.get(key=DOSSIER_READ_ONLY_WORK_STATUS[0])


def test_without_user_normal_status(dossier, user):
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    assert (
        annotate_with_calculated_access_mode(queryset, user)
        .first()
        .calculated_access_mode
        == Dossier.DossierAccessMode.READ_WRITE
    )


def test_without_user_read_only_status(dossier, work_status_read_only, user):
    dossier.work_status = work_status_read_only
    dossier.save()
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    assert (
        annotate_with_calculated_access_mode(queryset, user)
        .first()
        .calculated_access_mode
        == Dossier.DossierAccessMode.READ_ONLY
    )


def test_without_user_read_write_status(dossier, user):
    account = dossier.account
    update_state_machine(
        Path(__file__).parent.parent.parent
        / "statemgmt/configurations/default/default_state_machine_2025_02_21.json",
        "Dossier Default",
    )

    account.active_work_status_state_machine = StateMachine.objects.get(
        name="Dossier Default"
    )

    account.save()

    dossier.work_status = dossier.account.active_work_status_state_machine.start_status
    dossier.save()
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")

    assert (
        annotate_with_calculated_access_mode(queryset, user)
        .first()
        .calculated_access_mode
        == Dossier.DossierAccessMode.READ_WRITE
    )


def test_with_user_no_grant(user, dossier):
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_WRITE


def test_with_user_read_only_grant(user, dossier):
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_ONLY,
    )
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_ONLY


def test_with_user_read_write_grant(user, dossier):
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_WRITE,
    )
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_WRITE


def test_with_user_multiple_grants(user, dossier):
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_WRITE,
    )
    with pytest.raises(ValidationError):
        # This should raise an error because we have two grants for the same user
        DossierAccessGrant.objects.create(
            user=user,
            dossier=dossier,
            expires_at=timezone.now() + timezone.timedelta(days=2),
            has_access=True,
            scope=Scope.READ_ONLY,
        )
        # Code should not reach here, but keep it to show what we are testing
        queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
        result = annotate_with_calculated_access_mode(queryset, user)
        assert (
            result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_ONLY
        )


def test_with_expired_grant(user, dossier):
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() - timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_ONLY,
    )
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_WRITE


def test_user_read_only_status_read_only_grant(dossier, work_status_read_only, user):
    # Both have read only access
    dossier.work_status = work_status_read_only
    dossier.save()
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_ONLY,
    )
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_ONLY


def test_most_restrictive_dossier_access_grant(dossier, user):
    # Dossier Access Grant restricts access to read only, but work status is read-write
    dossier.work_status = Status.objects.get(key="IN_FRONT_OFFICE")
    dossier.save()
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_ONLY,
    )
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_ONLY


def test_most_restrictive_work_status(dossier, work_status_read_only, user):
    # work_status restricts access to read only, but DossierAccessGrant is read-write
    dossier.work_status = work_status_read_only
    dossier.save()
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_WRITE,
    )
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_ONLY


def test_most_restrictive_both_read_write(dossier, work_status_read_only, user):
    # work_status is read-write, but DossierAccessGrant is read-write
    dossier.work_status = Status.objects.get(key="IN_FRONT_OFFICE")
    dossier.save()
    create_or_update_access_grant(
        user=user,
        dossier=dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
        scope=Scope.READ_WRITE,
    )
    queryset = Dossier.objects.filter(name="sales pitch mix with errors dossier")
    result = annotate_with_calculated_access_mode(queryset, user)
    assert result.first().calculated_access_mode == Dossier.DossierAccessMode.READ_WRITE
