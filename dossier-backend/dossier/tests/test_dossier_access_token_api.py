import json

import pytest
from django.urls import reverse
from jwcrypto.jwk import JW<PERSON>
from jwcrypto.jwt import JWT
from django.conf import settings

from core.authentication import AuthenticatedClient
from dossier.models import Dossier, Account


@pytest.fixture
def dossier():
    """
    Create a dossier
    @return: Dossier object instance with the admin as the Dossier owner
    """
    # get the default account
    account = Account.objects.get(key="default")
    account.cdp_field_set = "DefaultFieldSet"
    account.save()
    dossier = Dossier.objects.create(
        account=Account.objects.get(key="default"),
        name="test dossier",
    )
    return dossier


@pytest.mark.django_db
def test_user_without_dossier_access_cannot_obtain_cdp_token(
    testuser1_client: AuthenticatedClient, dossier
):
    """
    Test that a user without access to a dossier cannot obtain a CDP token
    @param testuser1_client:
    @param dossier:
    @return:
    """

    response = testuser1_client.get(
        reverse("api:dossier-access-token", args=[dossier.uuid])
    )
    print(response.json())

    assert response.status_code == 404


@pytest.mark.django_db
def test_user_with_dossier_access_can_obtain_cdp_token(
    dossier_manager_client: AuthenticatedClient, dossier
):
    """
    Test that a user with access to a dossier can obtain a CDP token
    """
    response = dossier_manager_client.get(
        reverse("api:dossier-access-token", args=[dossier.uuid])
    )
    assert response.status_code == 200
    jwt = JWT(
        jwt=response.json(),
        key=JWK.from_json(settings.INSTANCE_SIGNING_KEY),
        expected_type="JWS",
    )
    claims = json.loads(jwt.claims)
    assert claims["dossier_uuid"] == str(dossier.uuid)
    assert claims["iss"] == "dms"
    assert claims["aud"] == "cdp"
    assert claims["exp"] > claims["iat"]
    assert claims["field_set"] == dossier.account.cdp_field_set
