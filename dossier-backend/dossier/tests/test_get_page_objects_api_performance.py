from typing import List

import pytest
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from pydantic import TypeAdapter

from dossier import schemas
from dossier.conftest import create_synthetic_dossier_performance_testing
from semantic_document.models import SemanticPagePageObject

User = get_user_model()

pytestmark = pytest.mark.django_db


# @pytest.mark.skip(reason="Slow test, don't run in CI (23 seconds)")
def test_sql_asserts_for_get_page_objects(
    testuser1_client,
    django_assert_max_num_queries,
):

    dossier, semantic_documents = create_synthetic_dossier_performance_testing()

    page_object_count = SemanticPagePageObject.objects.filter(
        semantic_page__dossier=dossier
    ).count()

    assert page_object_count > 0

    with django_assert_max_num_queries(17):
        start_time = timezone.now()
        response = testuser1_client.get(
            reverse(
                "api:dossier-all-page-objects", kwargs={"dossier_uuid": dossier.uuid}
            )
        )
        elapsed_time = (timezone.now() - start_time).total_seconds()
        print(f"Time taken for dossier-all-page-objects: {elapsed_time} seconds")

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.PageObjectApiDataWithUUID]).validate_json(
        response.content
    )

    assert len(parsed) == page_object_count

    with django_assert_max_num_queries(17):
        start_time = timezone.now()
        response = testuser1_client.get(
            reverse(
                "api:dossier-aggregate-page-objects",
                kwargs={"dossier_uuid": dossier.uuid},
            )
        )
        elapsed_time = (timezone.now() - start_time).total_seconds()
        print(f"Time taken for dossier-aggregate-page-objects: {elapsed_time} seconds")

    assert response.status_code == 200

    TypeAdapter(List[schemas.PageObjectApiDataWithUUID]).validate_json(response.content)

    with django_assert_max_num_queries(38):
        start_time = timezone.now()
        response = testuser1_client.get(
            reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
        )
        elapsed_time = (timezone.now() - start_time).total_seconds()
        print(f"Time taken for get-dossier-data-v2: {elapsed_time} seconds")

    assert response.status_code == 200


# @silk_profile(name="Get Page Objects Profile")
# def test_sql_asserts_for_get_page_objects(
#     testuser1_client,
# ):
#
#     dossier, semantic_documents = create_synthetic_dossier_performance_testing()
#
#     start_time = time.time()
#     response = testuser1_client.get(
#         reverse("api:dossier-all-page-objects", kwargs={"dossier_uuid": dossier.uuid})
#     )
#     assert response.status_code == 200
#     elapsed_time = time.time() - start_time
#     print(f"Time taken for dossier-all-page-objects: {elapsed_time} seconds")
#
#
# @silk_profile(name="Dossier Aggregate Page Objects Profile")
# def test_sql_asserts_for_dossier_aggregate_page_objects(
#     testuser1_client,
# ):
#
#     dossier, semantic_documents = create_synthetic_dossier_performance_testing()
#
#     start_time = time.time()
#     response = testuser1_client.get(
#         reverse(
#             "api:dossier-aggregate-page-objects",
#             kwargs={"dossier_uuid": dossier.uuid},
#         )
#     )
#     assert response.status_code == 200
#     elapsed_time = time.time() - start_time
#     print(f"Time taken for dossier-aggregate-page-objects: {elapsed_time} seconds")
#
#
# @silk_profile(name="Get Dossier Data V2 Profile")
# def test_sql_asserts_for_get_dossier_data_v2(
#     testuser1_client,
# ):
#
#     dossier, semantic_documents = create_synthetic_dossier_performance_testing()
#
#     start_time = time.time()
#     response = testuser1_client.get(
#         reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
#     )
#
#     elapsed_time = time.time() - start_time
#     print(f"Time taken for get-dossier-data-v2: {elapsed_time} seconds")
#
#     assert response.status_code == 200
