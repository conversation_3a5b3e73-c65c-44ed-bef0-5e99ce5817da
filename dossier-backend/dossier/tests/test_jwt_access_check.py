from dossier.jwt_extract import (
    has_user_role,
    is_manager,
    is_internal,
    get_external_dossier_id,
    get_account_key,
    get_account,
)
from dossier.models import Account
from projectconfig.roles import INTERNAL_ROLE, DOSSIER_MANAGER_ROLE


def test_has_user_role():
    assert has_user_role({}, "some role") is False


def test_is_manager():
    assert is_manager({"user_roles": ["other role", DOSSIER_MANAGER_ROLE]}) is True
    assert is_manager({"user_roles": ["other role", "and another role"]}) is False


def test_is_internal():
    assert is_internal({"user_roles": ["other role", INTERNAL_ROLE]}) is True
    assert is_internal({"user_roles": ["other role", "and another role"]}) is False


def test_get_external_dossier_id():
    assert get_external_dossier_id({"external_dossier_id": "some id"}) == "some id"
    assert get_external_dossier_id({}) is None


def test_get_account_key():
    assert get_account_key({"account_key": "some key"}) == "some key"
    assert get_account_key({}) is None


def test_get_account(db):
    account_key = "some key"
    account = Account.objects.create(key=account_key)
    assert get_account({"account_key": "some key"}) == account
    assert get_account({}) is None
