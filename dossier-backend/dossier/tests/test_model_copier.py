from uuid import uuid4

import pytest
import requests
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.base import ContentFile
from django.db import IntegrityError
from django.db.models import Exists, OuterRef

from core.helpers import remove_invalid_chars
from dossier.conftest import assert_dossier_fields
from dossier.helpers import (
    create_dossier_file_without_saving,
    get_all_s3_objects_in_bucket,
)
from dossier.helpers_model_copier import (
    copy_dossier_models,
    update_dossier_to_use_different_bucket,
    copy_single_dossier,
)
from dossier.models import (
    Dossier,
    DossierFile,
    OriginalFile,
    ExtractedFile,
    RealestateProperty,
    UserInvolvement,
    AssignedRealestatePropertyOriginalFile,
    CopyDossierHistory,
    FileException,
    FileStatus,
    DossierExport,
)
from dossier.services import get_dossier_s3_objects
from processed_file.models import ProcessedPage, ProcessedFile, PageObject
from semantic_document.helpers import semantic_restore_or_delete
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    SemanticDocumentPageObject,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPagePageObject,
)
from dossier.factories import DossierFileFactory


User: AbstractUser = get_user_model()


pytestmark = pytest.mark.django_db


def test_copy_dossier_synthetic_dossier_empty(synthetic_dossier):
    new_instances = list(
        copy_dossier_models(
            dossier=synthetic_dossier, external_id="new_external_id"
        ).values()
    )

    new_dossier: Dossier = new_instances[0]
    assert_dossier_fields(new_dossier, synthetic_dossier)
    assert synthetic_dossier.external_id == "test external id"
    assert new_dossier.external_id == "new_external_id"

    models_for_comparison_with_dossier_FK = [
        DossierFile,
        ExtractedFile,
        OriginalFile,
        RealestateProperty,
        ProcessedPage,
        SemanticDocument,
        UserInvolvement,
        ProcessedFile,
    ]

    for model in models_for_comparison_with_dossier_FK:
        assert (
            model._base_manager.filter(dossier=synthetic_dossier).count()
            == model._base_manager.filter(dossier=new_dossier).count()
        )


def test_copy_dossier_file_stand_alone(synthetic_dossier):
    # Should not copy, as there is no original file
    old_dossier = synthetic_dossier

    DossierFile.objects.create(
        uuid=uuid4(),
        dossier=old_dossier,
        bucket="dossier",
        data=ContentFile(b"", "my_filename.txt"),
    )

    assert old_dossier.dossierfile_set.count() == 1

    new_instances = list(
        copy_dossier_models(
            dossier=synthetic_dossier, external_id="new_external_id"
        ).values()
    )

    new_dossier: Dossier = new_instances[0]

    assert new_dossier.dossierfile_set.count() == 0


def test_copy_dossier_file_with_original_file(synthetic_dossier):
    # Should not copy, as there is no original file
    old_dossier = synthetic_dossier

    old_filenames = sorted(["my_filename_a.txt", "fileb.pdf"])

    for filename in old_filenames:
        dossier_file = DossierFile.objects.create(
            uuid=uuid4(),
            dossier=old_dossier,
            bucket="dossier",
            data=ContentFile(b"", filename),
        )
        OriginalFile.objects.create(dossier=synthetic_dossier, file=dossier_file)

    assert old_dossier.dossierfile_set.count() == len(old_filenames)

    new_instances = list(
        copy_dossier_models(
            dossier=synthetic_dossier, external_id="new_external_id"
        ).values()
    )

    new_dossier: Dossier = new_instances[0]

    assert new_dossier.dossierfile_set.count() == len(old_filenames)

    new_files = list(DossierFile.objects.filter(dossier=new_dossier).all())
    new_filenames = sorted([f.name for f in new_files])
    assert old_filenames == new_filenames


def _setup_dossier_models(temp_minio_bucket: str = None):
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    dossier.external_id = "test external id"
    if temp_minio_bucket:
        update_dossier_to_use_different_bucket(
            dossier=dossier, temp_minio_bucket=temp_minio_bucket
        )
    dossier.save()

    # Create Intermediate tables
    property_entity_mansion = RealestateProperty.objects.create(
        dossier=dossier, key="mansion"
    )

    original_file = OriginalFile.objects.filter(dossier=dossier).first()

    AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=original_file, realestate_property=property_entity_mansion
    )

    AssignedRealestatePropertyOriginalFile.objects.create(
        originalfile=OriginalFile.objects.filter(dossier=dossier).last(),
        realestate_property=property_entity_mansion,
    )

    semantic_document = SemanticDocument._base_manager.filter(dossier=dossier).first()

    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=semantic_document, realestate_property=property_entity_mansion
    )

    # We could have used the models_to_copy within copy_dossier_models
    # but I prefered to repeat them here, so a mistake in the list there
    # doesn't automatically transfer here
    models_for_comparison_with_dossier_FK = [
        (DossierFile, "dossier__uuid"),
        (OriginalFile, "dossier__uuid"),
        (ExtractedFile, "dossier__uuid"),
        (ProcessedFile, "dossier__uuid"),
        (ProcessedPage, "dossier__uuid"),
        (SemanticDocument, "dossier__uuid"),
        (SemanticPage, "dossier__uuid"),
        (PageObject, "processed_page__dossier__uuid"),
        (SemanticDocumentPageObject, "semantic_document__dossier__uuid"),
        (UserInvolvement, "dossier__uuid"),
        (
            RealestateProperty,
            "dossier__uuid",
        ),
        (
            AssignedRealestatePropertyOriginalFile,
            "originalfile__dossier__uuid",
        ),
        (
            AssignedRealEstatePropertySemanticDocument,
            "semantic_document__dossier__uuid",
        ),
        (
            SemanticPagePageObject,
            "semantic_page__dossier__uuid",
        ),
        (
            FileException,
            "dossier__uuid",
        ),
        # BEKB handling of specific fields not implemented
        # (
        #     BEKBDossierProperties,
        #     "dossier__uuid",
        # ),
    ]

    return (
        dossier,
        property_entity_mansion,
        original_file,
        semantic_document,
        models_for_comparison_with_dossier_FK,
    )


def check_model_counts(models_for_comparison, original_dossier, new_dossier):
    model_counts = {}
    for model, query_key in models_for_comparison:
        old_count = model._base_manager.filter(
            **{query_key: original_dossier.uuid}
        ).count()
        new_count = model._base_manager.filter(**{query_key: new_dossier.uuid}).count()
        assert old_count == new_count > 0
        model_counts[model.__name__] = new_count
    return model_counts


@pytest.mark.parametrize("copy_into_existing", [False, True])
def test_copy_dossier(temp_minio_bucket, copy_into_existing):
    (
        dossier,
        property_entity_mansion,
        original_file,
        semantic_document,
        models_for_comparison_with_dossier_FK,
    ) = _setup_dossier_models(temp_minio_bucket)

    # Keep track of this, so we have the uuid when we delete the dossier
    old_dossier_uuid = dossier.uuid

    if copy_into_existing:
        # Create target_dossier
        target_dossier = copy_single_dossier(
            dossier=dossier, external_id="new_external_id"
        )
        new_instances = list(
            copy_dossier_models(
                dossier=dossier,
                external_id="new_external_id",
                target_dossier=target_dossier,
            ).values()
        )
        new_dossier = target_dossier
        assert new_instances[0] == target_dossier
        assert new_instances[0].uuid == target_dossier.uuid
    else:
        new_instances = list(
            copy_dossier_models(dossier=dossier, external_id="new_external_id").values()
        )
        new_dossier = new_instances[0]

    assert_dossier_fields(new_dossier, dossier)
    dossier.refresh_from_db()
    assert dossier.external_id == "test external id"
    assert new_dossier.external_id == "new_external_id"

    model_counts = check_model_counts(
        models_for_comparison_with_dossier_FK, dossier, new_dossier
    )

    dossier.delete()

    for model, query_key in models_for_comparison_with_dossier_FK:
        new_count = model._base_manager.filter(**{query_key: new_dossier.uuid}).count()
        assert model_counts[model.__name__] == new_count
        # Check that we actually have entries
        assert new_count > 0

    assert CopyDossierHistory.objects.get(
        source_dossier_uuid=old_dossier_uuid, new_dossier_uuid=new_dossier.uuid
    )


@pytest.mark.parametrize(
    "soft_delete_semantic_document, soft_delete_semantic_page",
    [
        (False, False),  # No soft deletion (same as above test)
        (True, False),  # Soft delete semantic document
        (False, True),  # Soft delete semantic page
        (True, True),  # Soft delete both
    ],
)
def test_copy_dossier_soft_deleted(
    soft_delete_semantic_document, soft_delete_semantic_page, temp_minio_bucket
):
    # Specialised version where we soft delete some semantic documents and semantic pages
    (
        dossier,
        property_entity_mansion,
        original_file,
        semantic_document,
        models_for_comparison_with_dossier_FK,
    ) = _setup_dossier_models(temp_minio_bucket)

    # Keep track of this, so we have the uuid when we delete the dossier
    old_dossier_uuid = dossier.uuid

    # Test that we have more than 2 semantic documents (if we delete one)
    assert dossier.semantic_documents.count() > 1
    # Test that we have more than 2 semantic pages (if we delete one)
    assert semantic_document.semantic_pages.count() > 1

    # Assert we have not soft deleted documents and pages in dossier
    assert (
        SemanticDocument._base_manager.filter(
            dossier=dossier, deleted_at__isnull=False
        ).count()
        == 0
    )
    assert (
        SemanticPage._base_manager.filter(
            dossier=dossier, deleted_at__isnull=False
        ).count()
        == 0
    )

    semantic_page: SemanticPage = semantic_document.semantic_pages.first()
    assert semantic_page.deleted_at is None
    if soft_delete_semantic_page:
        semantic_restore_or_delete(
            dossier=dossier,
            model_in_db=SemanticPage,
            model_uuid=semantic_page.uuid,
            to_delete=True,
        )
        semantic_page.refresh_from_db()
        # Assert soft deletion via setting of timestamp at deleted_at
        assert semantic_page.deleted_at

    assert semantic_document.deleted_at is None
    if soft_delete_semantic_document:
        semantic_restore_or_delete(
            dossier=dossier,
            model_in_db=SemanticDocument,
            model_uuid=semantic_document.uuid,
            to_delete=True,
        )
        semantic_document.refresh_from_db()
        # Assert soft deletion via setting of timestamp at deleted_at
        assert semantic_document.deleted_at

    new_instances = list(
        copy_dossier_models(dossier=dossier, external_id="new_external_id").values()
    )

    new_dossier: Dossier = new_instances[0]
    assert_dossier_fields(new_dossier, dossier)
    dossier.refresh_from_db()
    assert dossier.external_id == "test external id"
    assert new_dossier.external_id == "new_external_id"

    model_counts = check_model_counts(
        models_for_comparison_with_dossier_FK, dossier, new_dossier
    )

    dossier.delete()

    for model, query_key in models_for_comparison_with_dossier_FK:
        new_count = model._base_manager.filter(**{query_key: new_dossier.uuid}).count()
        assert model_counts[model.__name__] == new_count
        # Check that we actually have entries
        assert new_count > 0

    history = CopyDossierHistory.objects.get(
        source_dossier_uuid=old_dossier_uuid, new_dossier_uuid=new_dossier.uuid
    )

    assert history
    assert history.status == FileStatus.PROCESSED

    if soft_delete_semantic_page:
        assert (
            SemanticPage._base_manager.filter(
                dossier=new_dossier, deleted_at__isnull=False
            ).count()
            == 1
        )

    if soft_delete_semantic_document:
        assert (
            SemanticDocument._base_manager.filter(
                dossier=new_dossier, deleted_at__isnull=False
            ).count()
            == 1
        )


@pytest.mark.parametrize(
    "model_to_mock",
    [
        # Introduce failure at the end, so that we can test behaviour of transaction rollback
        "semantic_document.models.AssignedRealEstatePropertySemanticDocument",
        # Introduce failure at the beginning, so that we can test behaviour of transaction rollback when no dossier
        # has been created
        "dossier.models.Dossier",
        # Introduce failure at Dossier File copy stage
        "dossier.models.DossierFile",
    ],
)
def test_copy_dossier_failure(model_to_mock, mocker, temp_minio_bucket):
    (
        dossier,
        property_entity_mansion,
        original_file,
        semantic_document,
        models_for_comparison_with_dossier_FK,
    ) = _setup_dossier_models(temp_minio_bucket)

    total_dossier_count = Dossier.objects.filter(account=dossier.account).count()
    total_semantic_pages_count = SemanticPage.objects.filter(
        semantic_document__dossier__account=dossier.account
    ).count()
    total_semantic_document_count = SemanticDocument.objects.filter(
        dossier__account=dossier.account
    ).count()

    mocker.patch(
        f"{model_to_mock}._base_manager.bulk_create",
        side_effect=IntegrityError,
    )

    mock_logger_error = mocker.patch("dossier.helpers_model_copier.logger.error")

    assert dossier.bucket == temp_minio_bucket

    initial_all_s3_objects = set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )

    assert len(initial_all_s3_objects) > 0

    with pytest.raises(IntegrityError):
        list(
            copy_dossier_models(dossier=dossier, external_id="new_external_id").values()
        )

    history = CopyDossierHistory.objects.filter(
        source_dossier_uuid=dossier.uuid
    ).first()

    assert history.source_dossier_uuid == dossier.uuid
    assert history.source_dossier_external_id == dossier.external_id
    assert history.status == FileStatus.ERROR

    mock_logger_error.assert_called_with(
        "Copy failed",
        new_dossier_uuid=history.new_dossier_uuid,
        new_dossier_external_id=history.new_dossier_external_id,
        duration_sec=mocker.ANY,
        error="",
    )

    assert (
        Dossier.objects.filter(account=dossier.account).count() == total_dossier_count
    )
    assert (
        SemanticPage.objects.filter(
            semantic_document__dossier__account=dossier.account
        ).count()
        == total_semantic_pages_count
    )
    assert (
        SemanticDocument.objects.filter(dossier__account=dossier.account).count()
        == total_semantic_document_count
    )

    # Failures should not change s3 bucket contents i.e. rollback should occur correctly
    assert initial_all_s3_objects == set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )


def test_copy_dossier_missing_s3_data(mocker, temp_minio_bucket):
    (
        dossier,
        property_entity_mansion,
        original_file,
        semantic_document,
        models_for_comparison_with_dossier_FK,
    ) = _setup_dossier_models(temp_minio_bucket)

    # Test the case where we have a DossierFile, but no associated s3 file exists
    # Hence the S3 copy should fail

    # Same code as used in dossier zipper worker
    empty_dossier_file = create_dossier_file_without_saving(
        dossier, f"{remove_invalid_chars(dossier.name)}.zip"
    )
    empty_dossier_file.save()

    # We need to create an original file, otherwise Dossier file will not get copied
    original_file = OriginalFile.objects.create(
        dossier=dossier, file=empty_dossier_file
    )

    total_dossier_count = Dossier.objects.filter(account=dossier.account).count()
    total_semantic_pages_count = SemanticPage.objects.filter(
        semantic_document__dossier__account=dossier.account
    ).count()
    total_semantic_document_count = SemanticDocument.objects.filter(
        dossier__account=dossier.account
    ).count()

    mock_logger_error = mocker.patch("dossier.helpers_model_copier.logger.error")

    initial_all_s3_objects = set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )

    with pytest.raises(requests.exceptions.HTTPError):
        new_instances = list(
            copy_dossier_models(dossier=dossier, external_id="new_external_id").values()
        )

    history = CopyDossierHistory.objects.filter(
        source_dossier_uuid=dossier.uuid
    ).first()

    assert history.source_dossier_uuid == dossier.uuid
    assert history.source_dossier_external_id == dossier.external_id
    assert history.status == FileStatus.ERROR

    mock_logger_error.assert_called_with(
        "Copy failed",
        new_dossier_uuid=history.new_dossier_uuid,
        new_dossier_external_id=history.new_dossier_external_id,
        duration_sec=mocker.ANY,
        error=mocker.ANY,
    )

    assert "404 Client Error: Not Found" in str(mock_logger_error.mock_calls)

    assert (
        Dossier.objects.filter(account=dossier.account).count() == total_dossier_count
    )
    assert (
        SemanticPage.objects.filter(
            semantic_document__dossier__account=dossier.account
        ).count()
        == total_semantic_pages_count
    )
    assert (
        SemanticDocument.objects.filter(dossier__account=dossier.account).count()
        == total_semantic_document_count
    )

    # Check no changes in s3 files, as failure should have rolled back changes

    assert initial_all_s3_objects == set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )

    # Delete Original file
    original_file.delete()
    # This should now be filtered out at copy stage and work correctly

    new_instances = list(
        copy_dossier_models(dossier=dossier, external_id="new_external_id").values()
    )

    new_dossier: Dossier = new_instances[0]
    assert_dossier_fields(new_dossier, dossier)
    dossier.refresh_from_db()
    model_counts = {}

    for model, query_key in models_for_comparison_with_dossier_FK:
        old_count = model._base_manager.filter(**{query_key: dossier.uuid}).count()
        new_count = model._base_manager.filter(**{query_key: new_dossier.uuid}).count()

        if model == DossierFile:
            assert old_count - 1 == new_count
        else:
            assert old_count == new_count

        # Check that we actually have entries
        assert old_count > 0
        model_counts[model.__name__] = new_count

    new_dossier_objects = set(get_dossier_s3_objects(dossier=new_dossier))

    assert len(new_dossier_objects) > 0
    assert (
        len(new_dossier_objects)
        == DossierFile.objects.filter(dossier=new_dossier)
        .exclude(Exists(DossierExport.objects.filter(file=OuterRef("pk"))))
        .count()
    )

    all_s3_objects_with_copy = set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )

    # Assert that the new total file count has increased by new dossier objects
    assert len(all_s3_objects_with_copy) == len(initial_all_s3_objects) + len(
        new_dossier_objects
    )

    assert new_dossier_objects.issubset(all_s3_objects_with_copy)


# These two tests are not supper DRY, but are much easier to debug
@pytest.mark.parametrize("copy_into_existing", [False, True])
def test_copy_dossier_file_with_replacement(temp_minio_bucket, copy_into_existing):
    """
    Test that file_replacement field in OriginalFile is properly handled during dossier copying
    when file_replacement has a value.
    """
    (
        dossier,
        property_entity_mansion,
        original_file,
        semantic_document,
        models_for_comparison_with_dossier_FK,
    ) = _setup_dossier_models(temp_minio_bucket)

    # Get an original file to test with
    original_file = OriginalFile.objects.filter(dossier=dossier).first()

    # Create a replacement file using the factory
    replacement_file = DossierFileFactory(dossier=dossier, bucket=dossier.bucket)

    # Set file_replacement
    original_file.file_replacement = replacement_file
    original_file.save()

    # Copy the dossier
    if copy_into_existing:
        # Create target_dossier
        target_dossier = copy_single_dossier(
            dossier=dossier, external_id="new_external_id"
        )
        new_instances = copy_dossier_models(
            dossier=dossier,
            external_id="new_external_id",
            target_dossier=target_dossier,
        )
        new_dossier = target_dossier
        assert list(new_instances.values())[0] == target_dossier
        assert list(new_instances.values())[0].uuid == target_dossier.uuid
    else:
        new_instances = copy_dossier_models(
            dossier=dossier, external_id="new_external_id"
        )
        new_dossier = list(new_instances.values())[0]

    # Get the copied original file
    new_original_file = OriginalFile.objects.get(
        dossier=new_dossier, file=new_instances[original_file.file.pk]
    )

    # Test that file_replacement was properly copied
    assert new_original_file.file_replacement == new_instances[replacement_file.pk]

    # Clean up
    dossier.delete()


@pytest.mark.parametrize("copy_into_existing", [False, True])
def test_copy_dossier_file_without_replacement(temp_minio_bucket, copy_into_existing):
    """
    Test that file_replacement field in OriginalFile is properly handled during dossier copying
    when file_replacement is None.
    """
    (
        dossier,
        property_entity_mansion,
        original_file,
        semantic_document,
        models_for_comparison_with_dossier_FK,
    ) = _setup_dossier_models(temp_minio_bucket)

    # Get an original file to test with
    original_file = OriginalFile.objects.filter(dossier=dossier).first()

    # Ensure file_replacement is None
    original_file.file_replacement = None
    original_file.save()

    # Copy the dossier
    if copy_into_existing:
        # Create target_dossier
        target_dossier = copy_single_dossier(
            dossier=dossier, external_id="new_external_id"
        )
        new_instances = copy_dossier_models(
            dossier=dossier,
            external_id="new_external_id",
            target_dossier=target_dossier,
        )
        new_dossier = target_dossier
        assert list(new_instances.values())[0] == target_dossier
        assert list(new_instances.values())[0].uuid == target_dossier.uuid
    else:
        new_instances = copy_dossier_models(
            dossier=dossier, external_id="new_external_id"
        )
        new_dossier = list(new_instances.values())[0]

    # Get the copied original file
    new_original_file = OriginalFile.objects.get(
        dossier=new_dossier, file=new_instances[original_file.file.pk]
    )

    # Test that file_replacement is None in the copied file
    assert new_original_file.file_replacement is None

    # Clean up
    dossier.delete()
