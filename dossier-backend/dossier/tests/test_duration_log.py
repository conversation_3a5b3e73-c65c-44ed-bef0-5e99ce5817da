import time

import pytest

from dossier.duration_log import DurationLog


@pytest.mark.asyncio
async def test_duration_log():
    d = DurationLog("testlog")
    time.sleep(0.1)

    d.add_event("e1")
    time.sleep(0.5)

    d.add_event("e2")
    time.sleep(1)
    d.add_event("e3")

    d.log_all_events()

    d.log_all_events(duration_threshold_for_slow_warning=1.11)


async def test_2_logs():
    d1 = DurationLog("d1")
    time.sleep(0.5)
    d1.add_event("e1")

    d2 = DurationLog("d2")

    d2.add_event("e2.1")
    time.sleep(0.7)
    d2.add_event("e2.2")

    d1.log_all_events()

    d2.log_all_events()

    assert 0.5 <= d1.get_duration_full() < 0.6

    assert 0.7 <= d2.get_duration_full() < 0.8
