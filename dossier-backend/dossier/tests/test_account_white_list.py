import json
import re

import pytest
from django.conf import settings
import jwt
from jwt import PyJWK

from core.authentication import create_token, AuthenticatedClient
from dossier import schemas
from dossier.models import Account, WhiteListAccess
from dossier.services import validate_whitelistaccess
from projectconfig.jwk import extract_public_jwk


def test_check_basic_whitelistaccess(db):
    account = Account.objects.create(
        key="some", name="Some strange financial institute"
    )
    WhiteListAccess.objects.create(
        account=account, jwt_attribute="email", regex=".*@hypodossier.ch$"
    )

    encoded_token = create_token("testuser", "testuser", "<EMAIL>")

    # Note: you must use a public key only (not private key) for jwt.decode
    jwk_dict = json.loads(settings.INSTANCE_SIGNING_KEY)
    token = jwt.decode(
        jwt=encoded_token,
        key=PyJWK(extract_public_jwk(jwk_dict)),
        audience="account",
        algorithms=["RS256"],
        options={"verify_signature": True, "exp": True},
    )

    validate_whitelistaccess(account, token)

    WhiteListAccess.objects.create(
        account=account, jwt_attribute="family_name", regex="unknown"
    )
    with pytest.raises(AssertionError):
        validate_whitelistaccess(account, token)


def test_sample_email_domain_regex():
    rule = re.compile(".*@hypodossier.ch$|.*@bekb.ch$")
    positives = ["<EMAIL>", "<EMAIL>"]
    for positiv in positives:
        print(positiv)
        assert rule.match(positiv) is not None

    negatives = ["<EMAIL>"]
    for negative in negatives:
        assert rule.match(negative) is None


def test_api_access_with_whitelistaccess_block(
    testuser1_client: AuthenticatedClient, db
):
    account = Account.objects.get(key="default")

    WhiteListAccess.objects.create(
        account=account, jwt_attribute="email", regex=".*@nothypodossier.ch$"
    )
    result = testuser1_client.post(
        "/api/dossier/",
        data=schemas.CreateDossier(name="Test Dossier", lang="fr").model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 401


def test_api_access_with_whitelistaccess_success(
    testuser1_client: AuthenticatedClient, db
):
    account = Account.objects.get(key="default")

    WhiteListAccess.objects.create(
        account=account, jwt_attribute="email", regex=".*@hypodossier.ch$"
    )
    result = testuser1_client.post(
        "/api/dossier/",
        data=schemas.CreateDossier(name="Test Dossier", lang="fr").model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200
