import structlog
from django.db import transaction

from dossier.models import OriginalFile, ExtractedFile, FileStatus
from dossier.processing_config import (
    OriginalFileProcessingConfig,
    SemanticDocumentSplittingStyle,
)
from dossier.tasks import process_original_file
from processed_file.models import ProcessedPage
from semantic_document.models import SemanticDocument, SemanticPage

logger = structlog.get_logger()


def delete_processing_results_for_original_file(of: OriginalFile):
    """
    Delete everything that has been created by processing for this original file.
    - Extracted Files
    --- FileExceptions
    - Processed Files
    - Semantic Documents
    @param of:
    @return:
    """
    with transaction.atomic():
        logger.info(
            "Delete processing artefacts for originalfile...", originalfile_uuid=of.uuid
        )

        # Step 1: Set status back to processing
        of.status = FileStatus.PROCESSING
        # Reset exceptions... new processing can add new exceptions
        of.exception_de = None
        of.exception_en = None
        of.exception_fr = None
        of.exception_it = None
        of.save()

        # Step 2: Find all related semantic documents and delete them because their deletion is not triggered
        # by cascading the deletion of extracted files
        extracted_files = ExtractedFile.objects.filter(
            dossier=of.dossier, original_file=of
        ).all()
        processed_pages = ProcessedPage.objects.filter(
            processed_file__extracted_file__in=extracted_files
        ).all()

        # Fetch semantic pages with the base manager because we also want deleted pages
        semantic_pages = SemanticPage._base_manager.filter(
            processed_page__in=processed_pages
        ).all()

        semantic_documents = set()
        for sp in semantic_pages:
            semantic_documents.add(sp.semantic_document)

        logger.info(
            "Found processing results that will be deleted",
            num_extracted_files=len(extracted_files),
            num_processed_pages=len(processed_pages),
            num_semantic_documents=len(semantic_documents),
        )

        for sd in semantic_documents:
            sd.hard_delete()

        # Step 3: delete cascading extracted_file, processed_page, semantic_page, semantic_page_objects
        for extracted_file in extracted_files:
            # logger.info("Delete extracted_file", uuid=extracted_file.uuid)
            extracted_file.delete()

        # Just to be sure also delete any semantic document that could have a conflict
        # with force_external_semantic_document_id (e.g. from another original file with the same value for
        # 'force_external_semantic_document_id'
        if of.force_external_semantic_document_id:
            try:
                sd = SemanticDocument._base_manager.get(
                    dossier=of.dossier,
                    external_semantic_document_id=of.force_external_semantic_document_id,
                )
                deleted, _ = sd.hard_delete()
                if deleted:
                    logger.info(
                        "Deleted semantic document before reprocessing original_file with same external_semantic_document_id",
                        force_external_semantic_document_id=of.force_external_semantic_document_id,
                        semantic_document_uuid=sd.uuid,
                    )

            except SemanticDocument.DoesNotExist:
                # Document does not yet exist. Nothing to do.
                pass


def recreate_processing_results_for_original_file(
    of: OriginalFile, use_replacement: bool = True
):
    """
    Delete everything that has been created by processing for this original file.
    Then start processing again.
    @param of: OriginalFile
    @param use_replacement: Whether to use the replacement file for processing
    @return:
    """

    style = (
        SemanticDocumentSplittingStyle(of.force_semantic_document_splitting_style)
        if of.force_semantic_document_splitting_style
        else None
    )

    processing_config = OriginalFileProcessingConfig(
        force_document_category_key=of.force_document_category_key,
        force_title_suffix=of.force_title_suffix,
        semantic_document_splitting_style=style,
        enable_virus_scan=of.dossier.account.enable_virus_scan,
        max_num_pages_allowed_input=of.dossier.account.max_num_pages_allowed_input,
    )

    process_original_file(
        original_file=of,
        processing_config=processing_config,
        use_replacement=use_replacement,
    )
