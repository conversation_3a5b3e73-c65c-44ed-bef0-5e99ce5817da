from bcge.factories import BCGEAccountFactoryFaker
from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.fakes import BekbAccountFactoryFaker
from clientis.factories import ClientisAccountFactoryFaker
from finnova.factories import FinnovaAccountFactoryFaker
from zkb.tests.factories import ZKBAccountFactoryFaker


def load_default_accounts():

    accounts = [
        (ClientisAccountFactoryFaker, "clientistest"),
        (ClientisAccountFactoryFaker, "boa"),
        (ClientisAccountFactoryFaker, "dcb"),
        (BCGEAccountFactoryFaker, "bcgeevo"),
        (BekbAccountFactoryFaker, "bekbe"),
        (BekbFiplaAccountFactory, "bekbfiplae"),
        (ZKBAccountFactoryFaker, "zkbd"),
        (FinnovaAccountFactoryFaker, "finnovadev"),
    ]
    for faker, account_key in accounts:
        account_fac = faker(
            account_key=account_key, default_bucket_name="dms-default-bucket"
        )
        account_fac.load_initial_document_categories()
        account = account_fac.account
        account.default_bucket_name = "dms-default-bucket"
        account.save()
        print(f"Account {account_key} created")
