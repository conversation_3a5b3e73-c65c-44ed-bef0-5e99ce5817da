import structlog
import random

import djclick as click
from faker import Faker
from uuid import UUI<PERSON>

from dossier.fakes import (
    add_fake_semantic_documents_for_merge_testing,
)
from dossier.models import (
    Account,
    RealestateProperty,
    Dossier,
    DocumentCategory,
)


logger = structlog.get_logger()


@click.command()
@click.argument("dossier_uuid", type=click.UUID)
@click.argument("account_key", type=click.STRING, default="default")
def create_semantic_documents_for_combine_pages_ui(
    dossier_uuid: UUID, account_key: str
):
    # Creates a semantic document and real estate property
    # Used to UI when we create a semantic document and a real estate property and attempt to merge them
    default_account = Account.objects.get(key=account_key)

    default_account.enable_real_estate_properties = True

    default_account.save()

    faker = Faker(locale="de_CH")

    faker.seed_instance(random.random())

    dossier = Dossier.objects.get(uuid=dossier_uuid, account=default_account)

    # Delete any pre-existing semantic documents
    dossier.semantic_documents.all().delete()

    # We want to test 4 categories: PROPERTY_PHOTOS, PLAN_FLOOR, PLAN_ANY, TAX_DECLARATION
    document_category_photo = DocumentCategory.objects.filter(
        account=dossier.account, name="PROPERTY_PHOTOS"
    ).first()

    document_category_floor = DocumentCategory.objects.filter(
        account=dossier.account, name="PLAN_FLOOR"
    ).first()

    document_category_plan = DocumentCategory.objects.filter(
        account=dossier.account, name="PLAN_ANY"
    ).first()

    DocumentCategory.objects.filter(
        account=dossier.account, name="TAX_DECLARATION"
    ).first()

    # Tax does not have a real estate property
    categories_with_real_estate_property = [
        document_category_photo,
        document_category_floor,
        document_category_plan,
    ]

    real_estate_property_mansion, _ = RealestateProperty.objects.update_or_create(
        defaults=dict(
            title="MANSION",
            floor=(
                random.randint(1, 20) if random.random() > 0.8 else None
            ),  # Generating random floor number
            street=faker.street_name(),
            street_nr=faker.building_number() if random.random() > 0.8 else None,
            zipcode=faker.postcode(),
            city=faker.city(),
        ),
        dossier=dossier,
        key="MANSION",
    )

    real_estate_property_flat, _ = RealestateProperty.objects.update_or_create(
        defaults=dict(
            title="FLAT",
            floor=(
                random.randint(1, 20) if random.random() > 0.8 else None
            ),  # Generating random floor number
            street=faker.street_name(),
            street_nr=faker.building_number() if random.random() > 0.8 else None,
            zipcode=faker.postcode(),
            city=faker.city(),
        ),
        dossier=dossier,
        key="FLAT",
    )

    # Create semantic documents for each category with real estate property
    for document_category in categories_with_real_estate_property:
        add_fake_semantic_documents_for_merge_testing(
            dossier=dossier,
            document_category=document_category,
            num_docs=2,
            real_estate_property=real_estate_property_mansion,
            min_num_pages=3,
            max_pages=3,
        )
        add_fake_semantic_documents_for_merge_testing(
            dossier=dossier,
            document_category=document_category,
            num_docs=2,
            real_estate_property=real_estate_property_flat,
            min_num_pages=3,
            max_pages=3,
        )
        add_fake_semantic_documents_for_merge_testing(
            dossier=dossier,
            document_category=document_category,
            num_docs=2,
            min_num_pages=3,
            max_pages=3,
        )
