import os
import structlog
from dossier.admin import DossierResource
from pathlib import Path
from tempfile import TemporaryDirectory

import djclick as click
import minio
from django.conf import settings

logger = structlog.get_logger()


@click.command()
@click.argument("bucket")
def create_dossier_export(bucket):
    resource = DossierResource()
    dataset = resource.export()
    filename = "dossier_export.xlsx"
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        filepath = os.path.join(temp_path, filename)
        with open(filepath, "wb") as f:
            f.write(dataset.xlsx)

        minio_client = minio.Minio(
            settings.S3_ENDPOINT,
            settings.S3_ACCESS_KEY,
            settings.S3_SECRET_KEY,
            secure=settings.S3_SECURE,
            region=settings.S3_REGION,
        )
        minio_client.fput_object(bucket, filename, filepath)
        logger.info(f"Export completed and saved to {filename}")
