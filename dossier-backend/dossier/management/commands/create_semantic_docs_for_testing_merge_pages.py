import structlog
import random

import djclick as click
from faker import Faker
from uuid import UUI<PERSON>

from dossier.fakes import (
    add_fake_semantic_documents_for_merge_testing,
)
from dossier.helpers import is_document_topic_property
from dossier.models import (
    Account,
    RealestateProperty,
    Dossier,
    DocumentCategory,
)


logger = structlog.get_logger()


@click.command()
@click.argument("dossier_uuid", type=click.UUID)
@click.argument("category_name", type=click.STRING, default="PROPERTY_PHOTOS")
@click.argument("account_key", type=click.STRING, default="default")
@click.argument("add_real_estate_property", type=click.BOOL, default=True)
def create_semantic_docs_for_testing_merge_pages(
    dossier_uuid: UUID, category_name: str, account_key: str, add_real_estate_property
):
    # Creates a semantic document and real estate property
    # Used to test updating of UI when we create a semantic document and a real estate property
    # view external API
    print(
        f"create_semantic_docs_for_testing_merge_pages({account_key = }, {category_name = }, {dossier_uuid = }, {add_real_estate_property = })..."
    )

    default_account = Account.objects.get(key=account_key)

    default_account.enable_real_estate_properties = True

    default_account.save()

    faker = Faker(locale="de_CH")

    faker.seed_instance(random.random())

    dossier = Dossier.objects.get(uuid=dossier_uuid, account=default_account)

    document_category = DocumentCategory.objects.filter(
        account=dossier.account, name=category_name
    ).first()

    real_estate_property = None

    if add_real_estate_property and is_document_topic_property(document_category):
        real_estate_property, _ = RealestateProperty.objects.update_or_create(
            defaults=dict(
                title=faker.sentence(nb_words=3),
                floor=(
                    random.randint(1, 20) if random.random() > 0.8 else None
                ),  # Generating random floor number
                street=faker.street_name(),
                street_nr=faker.building_number() if random.random() > 0.8 else None,
                zipcode=faker.postcode(),
                city=faker.city(),
            ),
            dossier=dossier,
            key=faker.bothify("PrN######"),
        )

    add_fake_semantic_documents_for_merge_testing(
        dossier=dossier,
        document_category=document_category,
        num_docs=2,
        real_estate_property=real_estate_property,
    )
