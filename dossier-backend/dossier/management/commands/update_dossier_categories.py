import structlog

import dj<PERSON><PERSON> as click

from dossier.doc_cat_helpers import (
    load_document_categories_from_path,
    DEFAULT_CATEGORIES,
)
from dossier.models import Account

logger = structlog.get_logger()


@click.command()
@click.argument("account_key")
@click.argument(
    "document_categories_json_path", type=click.STRING, default=DEFAULT_CATEGORIES
)
@click.option("-update_id", type=click.BOOL, default=True)
@click.option("-update_exclude_for_recommendation", type=click.BOOL, default=True)
def update_dossier_categories(
    account_key: str,
    document_categories_json_path: str = None,
    update_id: bool = True,
    update_exclude_for_recommendation: bool = True,
):
    """
    Example:
    # Load old categories first to see the changes
    python manage.py update_dossier_categories default assets/document_category/default/DocumentCategory-2023-12-01.json

    # Then update with current categories
    python manage.py update_dossier_categories default assets/document_category/default/DocumentCategory-2025-03-10.json

    # If custom document categories need to be loaded, update default categories first and then the account specific ones
    python manage.py update_dossier_categories zkbat assets/document_category/default/DocumentCategory-2025-03-10.json

    python manage.py update_dossier_categories bcgeevo assets/document_category/default/DocumentCategory-2025-03-10.json
    python manage.py update_dossier_categories bcgetst assets/document_category/default/DocumentCategory-2025-03-10.json
    python manage.py update_dossier_categories bcgeprd assets/document_category/default/DocumentCategory-2025-03-10.json

    python manage.py update_dossier_categories zkbat assets/document_category/zkb/DocumentCategory-2024-05-22zkb.json

    WARNING: this does not work for BEKB yet
    python manage.py update_dossier_categories bekbe bekb/data/document_categories/240723_production_before_upgrade/FullDocumentCategory-2024-07-23.json

    To copy files manually to container for an update without redeploying the container:

    Local:
    docker cp assets/document_category/default/DocumentCategory-2025-03-10.json t57lzxrrns7r:/app/assets/document_category/default/DocumentCategory-2024-05-23.json
    docker cp assets/document_category/zkb/DocumentCategory-2024-05-22zkb.json 967a02046f0d:/app/assets/document_category/zkb/DocumentCategory-2024-05-22zkb.json

    In Swarm:
    Copy to a swarm node that has a matching container:
    scp assets/document_category/default/DocumentCategory-2024-05-23.json ubuntu@swarm24:/home/<USER>/DocumentCategory-2024-05-23.json
    scp assets/document_category/zkb/DocumentCategory-2024-05-22zkb.json ubuntu@swarm24:/home/<USER>/DocumentCategory-2024-05-22zkb.json
    docker cp ./DocumentCategory-2024-05-23.json f8353f5fe8c0:/app/assets/document_category/default/DocumentCategory-2024-05-23.json
    docker cp ./DocumentCategory-2024-05-22zkb.json f8353f5fe8c0:/app/assets/document_category/zkb/DocumentCategory-2024-05-22zkb.json

    python manage.py update_dossier_categories zkb assets/document_category/default/DocumentCategory-2024-05-23.json
    python manage.py update_dossier_categories zkb assets/document_category/zkb/DocumentCategory-2024-05-22zkb.json

    @param update_exclude_for_recommendation:
    @param update_id:
    @param account_key:
    @param document_categories_json_path:
    @return:
    """
    account = Account.objects.get(key=account_key)
    assert account, f"Account '{account_key}' does not exist"

    load_document_categories_from_path(
        account,
        document_categories_json_path,
        info_logging=True,
        update_id=update_id,
        update_exclude_for_recommendation=update_exclude_for_recommendation,
    )
