from django.utils import timezone

import djclick as click
import structlog
from django.db import transaction
from faker import Faker
from uuid import UUID

from dossier.doc_cat_helpers import UNKNOWN_DOCUMENT_CATEGORY_KEYS
from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import (
    Dossier<PERSON><PERSON>,
    Account,
    DocumentCate<PERSON>y,
    DossierRole,
    UserInvolvement,
    Dossier,
)
from dossier.services import create_dossier
from semantic_document.models import SemanticDocument

logger = structlog.get_logger(__name__)

fake = Faker()


@click.command()
@click.argument("account_key", type=click.STRING)
@click.argument(
    "create_unknown",
    type=click.BOOL,
    default=True,
)  # decides if the UNKNOWN, UNKNOWN_DE, ... are used or not
@click.option(
    "--dossier-uuid",
    type=click.UUID,
    help="Optional UUID of an existing dossier to add documents to. If not provided, a new dossier will be created.",
)
@transaction.atomic
def create_dossier_test_archiving_possible_document_category(
    account_key: str, create_unknown: bool, dossier_uuid: UUID | None = None
):
    """
    Create a dossier to test the archiving of every possible document category

    Example:
        python manage.py create_dossier_test_archiving_possible_document_category bcgeevo

        Without UNKNONWN categories (5 less):
        python manage.py create_dossier_test_archiving_possible_document_category bcgeevo False

        Using an existing dossier:
        python manage.py create_dossier_test_archiving_possible_document_category bcgeevo --dossier-uuid=123e4567-e89b-12d3-a456-************

    @param account_key:
    @param create_unknown:
    @param dossier_uuid: Optional UUID of an existing dossier to add documents to
    @return:
    """

    # AccountName.bcged.value
    logger.info(
        "Creating dossier",
        account_key={account_key},
        create_unknown={create_unknown},
        dossier_uuid=dossier_uuid,
    )
    start = timezone.now()

    account = Account.objects.filter(key=account_key).first()
    if not account:
        logger.error(
            f"Failed to find account {account_key}, valid keys are",
            valid_account_keys=Account.objects.all().values_list("key", flat=True),
        )
        return

    document_categories = DocumentCategory.objects.filter(account=account)

    if create_unknown is False:
        document_categories = document_categories.exclude(
            name__in=UNKNOWN_DOCUMENT_CATEGORY_KEYS
        )

    if dossier_uuid:
        try:
            new_dossier = Dossier.objects.get(uuid=dossier_uuid, account=account)
            logger.info(
                "Using existing dossier",
                dossier_uuid=dossier_uuid,
                dossier_name=new_dossier.name,
            )
        except Dossier.DoesNotExist:
            logger.error(
                f"Failed to find dossier with UUID {dossier_uuid} for account {account_key}"
            )
            return
    else:
        dossier_name = fake.name() + " Document Category Test Dossier"

        dossier_user = DossierUser.objects.filter(account=account).first()

        new_dossier = create_dossier(
            account=account,
            dossier_name=dossier_name,
            language="de",
            owner=dossier_user.user,
            external_id=dossier_name,
        )
        new_dossier.save()

        assignee_role = DossierRole.objects.filter(
            account=account, key="ASSIGNEE"
        ).first()

        if assignee_role is None:
            assignee_role, _ = DossierRole.objects.get_or_create(
                account=account, key="ASSIGNEE"
            )

        UserInvolvement.objects.get_or_create(
            dossier=new_dossier, role=assignee_role, user=dossier_user
        )

    for document_category in document_categories:
        # this needs to upload the dummy page to S3
        sem_doc: SemanticDocument = add_some_fake_semantic_documents(
            dossier=new_dossier,
            num_docs=1,
            valid_document_category_keys=[document_category.name],
            max_pages=1,
            min_num_pages=1,
            # no_page_objects_per_page=2,
            log=False,
            allow_empty_docs=False,
        )[0]

        sem_doc.title_suffix = (
            f"doc cat check {document_category.name} {document_category.en}"
        )

        sem_doc.save()

    logger.info(
        f"Added Semantic document to dossier {new_dossier.name}",
        dossier_name=new_dossier.name,
        dossier_uuid=new_dossier.uuid,
        document_category_count=document_categories.count(),
    )

    duration = timezone.now() - start

    logger.info(
        "Process finished",
        duration=duration,
        dossier_uuid=str(new_dossier.uuid),
        dossier_name=new_dossier.name,
        dossier_owner=new_dossier.owner,
        num_documents_added=document_categories.count(),
    )
