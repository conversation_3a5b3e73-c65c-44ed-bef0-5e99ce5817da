import json
import structlog
import os
import uuid

import djclick as click

from dossier.services import create_expiration_date
import jwt
from jwcrypto import jwk
from django.conf import settings
from projectconfig.jwk import load_jwk_from_env
from dossier.tests.data import DATA_PATH


from faker import Faker

logger = structlog.get_logger()


def create_token_data(minutes=10080):
    faker = Faker(locale="de_CH")
    return {
        "aud": "account",
        "exp": create_expiration_date(minutes=10080),
        "name": "service-default-first service-default-last",
        "given_name": "service-default-first",
        "family_name": "service-default-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        "external_dossier_id": faker.sentence(),  # Not currently used, as we directly provide this
        # during dossier creation via API parameter
        "user_roles": ["api_role"],
        "account_key": "default",
        "account_name": "default development account",
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


def generate_jwt_token(token_data):
    jwks_public_private = load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()

    jwk_key = jwk.JWK.from_json(json.dumps(jwks_public_private["keys"][0]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,
        algorithm="RS256",
    )


@click.command()
def run():
    # Generate a long-lived JWT token for testing purposes based off privatekey in
    # tests/data/jwks-example.json
    # Currently lasts a week
    # Run via python manage.py generate_default_account_jwt

    print("TOKEN for account key = 'default':\n")
    print(generate_jwt_token(create_token_data()))
