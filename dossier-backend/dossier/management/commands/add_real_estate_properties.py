import structlog
import random

import djclick as click
from faker import Faker

from dossier.helpers import is_document_topic_property
from dossier.models import (
    Account,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
)
from semantic_document.models import (
    SemanticDocument,
    AssignedRealEstatePropertySemanticDocument,
)


logger = structlog.get_logger()


@click.command()
def add_real_estate_properties():
    default_account = Account.objects.get(key="default")

    default_account.enable_real_estate_properties = True

    default_account.save()

    faker = Faker(locale="de_CH")

    semantic_documents = SemanticDocument.objects.filter(
        dossier__account=default_account
    )

    for semantic_document in semantic_documents:
        # If a document category is a property, then create a property assignment
        if is_document_topic_property(semantic_document.document_category):
            realestate_property, _ = RealestateProperty.objects.update_or_create(
                defaults=dict(
                    title=faker.sentence(nb_words=3),
                    floor=(
                        random.randint(1, 20) if random.random() > 0.8 else None
                    ),  # Generating random floor number
                    street=faker.street_name(),
                    street_nr=(
                        faker.building_number() if random.random() > 0.8 else None
                    ),
                    zipcode=faker.postcode(),
                    city=faker.city(),
                ),
                dossier=semantic_document.dossier,
                key=faker.bothify("PrN######"),
            )

            AssignedRealEstatePropertySemanticDocument.objects.create(
                semantic_document=semantic_document,
                realestate_property=realestate_property,
            )

            original_files = semantic_document.dossier.original_files.all()

            for original_file in original_files:
                AssignedRealestatePropertyOriginalFile.objects.create(
                    originalfile=original_file,
                    realestate_property=realestate_property,
                )
