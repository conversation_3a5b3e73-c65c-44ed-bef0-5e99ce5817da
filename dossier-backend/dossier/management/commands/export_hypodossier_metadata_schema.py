"""
Export the schema for the metadata that is attached to the pdf documents

poetry shell
python manage.py export_hypodossier_metadata_schema

"""

import logging
import structlog
from pathlib import Path

import djclick as click

from semantic_document.services_pdf import PdfSemanticDocumentMetadata

logger = structlog.get_logger()


def export_schema():
    p = Path("hypodossier_semantic_dossier_export_schema_v1.0.0.json")
    logger.info(f"Export schema to '{p}...")

    p.write_text(PdfSemanticDocumentMetadata.schema_json(indent=4))


@click.command()
def start():
    logging.basicConfig(level=logging.INFO)
    export_schema()
