# Generated by Django 3.2.20 on 2023-08-02 15:19

from django.db import migrations, models
import dossier.models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0054_auto_20230518_1822'),
    ]

    operations = [
        migrations.AddField(
            model_name='dossier',
            name='external_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='account',
            name='max_dossier_expiry_duration_days',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AlterField(
            model_name='dossier',
            name='expiry_date',
            field=models.DateTimeField(db_index=True, default=dossier.models.get_default_dossiers_date),
        ),
        migrations.AlterField(
            model_name='dossierfile',
            name='bucket',
            field=models.CharField(help_text='S3 bucket name', max_length=255),
        ),
        migrations.AlterUniqueTogether(
            name='dossier',
            unique_together={('account', 'external_id')},
        ),
    ]
