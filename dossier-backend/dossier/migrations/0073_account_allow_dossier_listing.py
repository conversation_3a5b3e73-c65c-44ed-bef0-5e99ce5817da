# Generated by Django 3.2.23 on 2023-11-01 14:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0072_account_enable_real_estate_properties'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='allow_dossier_listing',
            field=models.BooleanField(default=True, help_text='If True allow to query the dossier list else it is not allowed by default (there can be exceptions e.g dossier manager role)'),
        ),
    ]
