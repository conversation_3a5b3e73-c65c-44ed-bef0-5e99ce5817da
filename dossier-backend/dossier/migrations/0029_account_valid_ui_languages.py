# Generated by Django 4.1.2 on 2022-10-28 07:47

import django.contrib.postgres.fields
from django.db import migrations, models
import dossier.models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0028_rename_additional_search_terms_documentcategory_additional_search_terms_de_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='valid_ui_languages',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.TextField(max_length=255), default=dossier.models.get_valid_ui_languages_default, size=None),
        ),
    ]
