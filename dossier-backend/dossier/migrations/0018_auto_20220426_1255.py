# Generated by Django 3.2.13 on 2022-04-26 10:55

import json
from pathlib import Path
from django.db import migrations


def forwards(apps, schema_editor):
    DocumentCategory = apps.get_model('dossier', 'DocumentCategory')

    # Commented out as we are no longer loading configurable data in Migration files
    # They should be loaded as manage.py commands

    # categories = json.loads((Path(__file__).parent / 'updated_dossier_categories.json').read_text())
    # for name, details in categories.items():
    #     DocumentCategory.objects.update_or_create(name=name, defaults=details)


class Migration(migrations.Migration):
    dependencies = [
        ('dossier', '0017_auto_20220426_1240'),
        ('semantic_document', '0010_alter_semanticdocument_title_custom')
    ]

    operations = [
        migrations.RunPython(forwards)

    ]
