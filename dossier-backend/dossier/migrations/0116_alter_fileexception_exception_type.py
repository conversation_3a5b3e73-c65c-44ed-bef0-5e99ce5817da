# Generated by Django 4.2.18 on 2025-02-10 11:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0115_account_enable_rendering_bekb_mortgage_archiving_tab"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="fileexception",
            name="exception_type",
            field=models.IntegerField(
                choices=[
                    (1, "Unknown Exception"),
                    (2, "Not Readable"),
                    (3, "Password Protected"),
                    (4, "Unsupported Filetype"),
                    (5, "Too Small File"),
                    (6, "Xlsm File Cannot Be Converted"),
                    (7, "Ocr Filetype Processing"),
                    (8, "Virus Detected"),
                    (9, "Too Many Pages In Document"),
                    (999, "Unmapped Exception"),
                ],
                default=1,
            ),
        ),
    ]
