# Generated by Django 4.2.10 on 2024-03-05 15:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0089_merge_0086_jwk_description_0088_dossiercopystatus"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="processing_strategy",
            field=models.CharField(
                choices=[
                    (
                        "DEFAULT",
                        "DEFAULT: Documents are processed directly after upload",
                    ),
                    (
                        "SUSPENDED_ALL",
                        "SUSPENDED_ALL: Automatic processing after upload is disabled",
                    ),
                ],
                default="DEFAULT",
                help_text="Configures the automatic processing of uploaded files. Can be used to e.g. disable the processing temporarily. For SUSPENDED_ALL:  No messages are sent to processing queue, documents stay in status 'Processing' until processed manually.",
                max_length=20,
                verbose_name="Processing strategy",
            ),
        ),
    ]
