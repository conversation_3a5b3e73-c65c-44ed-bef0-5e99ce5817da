# Generated by Django 3.2.16 on 2023-02-07 14:13

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0048_auto_20230131_1738'),
    ]

    operations = [
        migrations.CreateModel(
            name='WhiteListAccess',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('jwt_attribute', models.CharField(max_length=255)),
                ('regex', models.CharField(max_length=255)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
