# Generated by Django 3.2.23 on 2023-11-02 12:46

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0075_account_dossier_access_provider'),
    ]

    operations = [
        migrations.CreateModel(
            name='DossierAccessCheckProvider',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, unique=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='account',
            name='dossier_access_provider',
        ),
        migrations.AddField(
            model_name='account',
            name='dossier_access_check_provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dossier.dossieraccesscheckprovider'),
        ),
    ]
