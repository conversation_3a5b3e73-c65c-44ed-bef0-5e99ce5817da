# Generated by Django 3.2.23 on 2023-11-16 12:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0078_processedpagecount_owner'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='instructions_menu_key',
            field=models.CharField(blank=True, default='default', help_text="If empty, no instructions '?' menu is shown. Alternative options for different menu content are 'default' (regular instructions), 'zkb' (custom zkb menu, German only)", max_length=20),
        ),
        migrations.AlterField(
            model_name='account',
            name='default_bucket_name',
            field=models.CharField(help_text='Name of S3 bucket in which all files related to this account will be stored.', max_length=255),
        ),
        migrations.AlterField(
            model_name='account',
            name='default_dossier_expiry_duration_days',
            field=models.PositiveIntegerField(default=28, help_text='Number of days after the created_at that the expiry date will be set to by default after a dossier is created.'),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_download_original_file_link',
            field=models.BooleanField(default=True, help_text='If True show link to download the original (uploaded) files. Else no link is shown.'),
        ),
        migrations.AlterField(
            model_name='account',
            name='key',
            field=models.CharField(default='default', help_text='Unique identifier for the account which should be used to identify the account in code. Must be URL compatible.', max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='account',
            name='max_dossier_expiry_duration_days',
            field=models.PositiveIntegerField(default=365, help_text='Maximum number of days that the expiry date can be (manually) set to after the created_at. Dates later than that cannot be selected by the user.'),
        ),
        migrations.AlterField(
            model_name='account',
            name='name',
            field=models.CharField(help_text='Pretty name of account. Must not be used as unique identifier in code (use key instead).', max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='account',
            name='photo_album_docx_template',
            field=models.CharField(blank=True, default='photo-album-docx-template-default-v01.docx', help_text="If empty, no Word-Template based photo album download is available. Else set name of deployed template. Default is 'photo-album-docx-template-default-v01.docx', alternative option is 'photo-album-docx-template-hbl-v01.docx'.", max_length=255),
        ),
        migrations.AlterField(
            model_name='account',
            name='show_business_case_type',
            field=models.BooleanField(default=False, help_text='If True the field will be shown on dossier creation and in dossier properties and available as filtering option in dossier list view.'),
        ),
        migrations.AlterField(
            model_name='account',
            name='show_document_category_external',
            field=models.BooleanField(default=False, help_text="If True, value 'de_external' of document category will be shown in addition to 'de' in certain contexts (e.g. rename dialog)."),
        ),
    ]
