import urllib
import uuid

import requests
import structlog
from minio.commonconfig import CopySource
from requests.adapters import HTTPAdapter
import minio

from django.conf import settings
from dossier.models import (
    DossierFile,
    generate_path,
)

from projectconfig.settings import (
    DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE,
)

logger = structlog.get_logger()


s = requests.Session()

# Pool connections is number of pools for different hosts. So for each host there is a pool of size up to pool_maxsize
s.mount(
    "https://",
    HTTPAdapter(
        pool_connections=10,
        pool_maxsize=DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE,
        max_retries=7,
    ),
)


# Code moved here to fix circular import
def create_dossier_file_from_url(
    dossier_uuid: uuid.UUID, bucket: str, file_url: str
) -> DossierFile:
    """Creates a dossier file from an url but does not save it to the database, so it can be used in multithreading without issues"""
    filename = urllib.parse.unquote(
        urllib.parse.urlparse(file_url).path.rpartition("/")[-1]
    )
    response = s.get(file_url)
    response.raise_for_status()
    dossier_uuid = dossier_uuid
    dossier_file = DossierFile(dossier_id=dossier_uuid, bucket=bucket)
    dossier_file.data.name = generate_path(dossier_file, filename)
    response = s.put(dossier_file.put_url, data=response.content)
    response.raise_for_status()
    return dossier_file


def create_dossier_file_from_minio_object(
    dossier_uuid: uuid.UUID, bucket: str, source_object_name: str
) -> DossierFile:
    minio_client = minio.Minio(
        settings.S3_ENDPOINT,
        settings.S3_ACCESS_KEY,
        settings.S3_SECRET_KEY,
        secure=settings.S3_SECURE,
        region=settings.S3_REGION,
    )
    """Creates a dossier file from an object in MinIO bucket but does not save it to the database."""
    dossier_file = DossierFile(dossier_id=dossier_uuid, bucket=bucket)
    new_object_name = generate_path(dossier_file, source_object_name)
    dossier_file.data.name = new_object_name

    minio_client.copy_object(
        bucket, new_object_name, CopySource(bucket, source_object_name)
    )

    return dossier_file
