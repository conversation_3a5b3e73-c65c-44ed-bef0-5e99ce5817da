import structlog
from pathlib import Path
from typing import List, <PERSON>, <PERSON>ple
from uuid import UUID

from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.http import (
    HttpRequest,
    HttpResponseRedirect,
    StreamingHttpResponse,
    HttpResponse,
)
from django.shortcuts import get_object_or_404
from ninja import NinjaAPI
from ninja.errors import ValidationError
from ninja.openapi.schema import OpenAPISchema

from pydantic import HttpUrl

from bekb.schemas import schemas
from bekb.fakes import BekbAccountFactoryFaker
from bekb.models import (
    BEKBDossierProperties,
    Partner,
    Attribute,
    BusinessCase,
    RealEstateProperty,
    Collateral,
    CollateralRealEstateProperty,
)
from bekb.schemas.schemas import (
    AccountNameMortgage,
    Parkey,
    DossierProperties,
    DossierShowJWTMortgage,
    JWTAuthSchemaMortgage,
)
from bekb.services import (
    create_or_update_partner,
    update_or_create_user,
    get_fico_role,
    list_actively_used_business_parkeys,
    process_ready_for_export_dossiers,
    get_attributes_as_dict,
)
from bekb.services_api import (
    decode_jwt_or_render_error,
    process_common_dossier_logic,
    fix_account_name,
    get_dossiers_for_business_partner,
    create_dossier_from_jwt,
    get_hypodossier_users,
    update_user_attributes_service,
    process_business_partner_update,
    process_partner_partner_update,
    api_create_or_update_attribute,
    api_download_file,
    api_add_dossier_export_feedback,
    create_test_dossiers_ready_for_export,
    validate_jwt_schema_with_http_error,
)
from core.schema import Message, Result
from dossier.duration_log import DurationLog
from dossier.helpers_access_check import get_dossier_from_request_with_access_check
from dossier.helpers_api import handle_api_validation_error
from dossier.models import Account, UserInvolvement
from dossier.services import (
    get_grant_token_for_dossier,
)
from projectconfig.authentication import JWTAuth, Auth
from statemgmt.services import log_work_status_transitions

from ninja.responses import Response


logger = structlog.get_logger()


class BEKBJWTAuth(JWTAuth):
    def authenticate(self, request, token, *args, **kw):
        """Authenticates a user using JWTAuth, only if the user has the role defined in
        settings.BEKB_API_ROLE.

               Args:
                   request (HttpRequest): The HTTP request from the client.
                   token (str): The token to be authenticated.
               Returns:
                   HttpResponse: The authentication response.
        """
        auth = Auth(token)

        validate_jwt_schema_with_http_error(JWTAuthSchemaMortgage, auth.decoded_payload)

        if not auth.has_role(settings.BEKB_API_ROLE):
            logger.error(
                "Authentication failed: Required BEKB API role not found in token"
            )
            return

        return super().authenticate(request, token, *args, **kw)


api = NinjaAPI(
    title="Hypodossier - BEKB API",
    csrf=False,
    auth=BEKBJWTAuth(),
    urls_namespace="bekb-api",
    version="0.7.0",
)


@api.get(
    path="/{dossier_uuid}/token",
    response={200: str, 401: Message},
    url_name="cdp-dossier-access-token",
)
def get_dossier_grant_token(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)
    return get_grant_token_for_dossier(dossier)


@api.get(
    "/openapi_3_1_0.json",
    url_name="openapi-3-1-0",
    auth=None,
    include_in_schema=False,
)
def bekb_openapi_3_1_0_schema(request):
    # Actual schema
    return Response(OpenAPISchema(api, str(Path(request.path).parent)))


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


@api.get("/dossier", response={303: HttpUrl}, auth=None, url_name="show-dossier")
def show_dossier(request, encoded_jwt: str) -> HttpResponse:
    """Shows all the dossiers associated with the corresponding businesscase_parkey
    and creates a redirect to the bekb_dossier list view.

    for the encoding of the encoded_jwt see @create_dossier

    Args:
    request (HttpRequest): The HTTP request from the client.
    encoded_jwt (str): The encoded JWT.

    Returns:
        HttpResponse: The HTTP response.
    """
    decoded_jwt_or_response = decode_jwt_or_render_error(
        encoded_jwt, error_redirect_url="https://bekb.hypodossier.ch/dashboard"
    )
    if isinstance(decoded_jwt_or_response, HttpResponse):
        # Decoding failed => return error page
        return decoded_jwt_or_response

    decoded_jwt = decoded_jwt_or_response

    # Adjust the account name from "bekb" -> "bekbp"
    fix_account_name(decoded_jwt, old_name="bekb", new_name="bekbp")

    # Process the shared dossier logic (with_fico = True if we want to update .fico)
    redirect_url = process_common_dossier_logic(
        decoded_jwt=decoded_jwt,
        schema_cls=DossierShowJWTMortgage,
        use_fico=True,  # For the second schema, we want the .fico logic
    )
    return HttpResponseRedirect(redirect_to=redirect_url)


@api.get(
    "/list-dossiers/{account_name}/{business_parkey}",
    response={200: List[schemas.DossierShow]},
    url_name="list-dossiers",
)
def list_dossiers(request, account_name: AccountNameMortgage, business_parkey: Parkey):
    """Lists all dossiers associated with the specified business partner."""
    account = get_object_or_404(Account, key=account_name)
    return get_dossiers_for_business_partner(account, business_parkey)


@api.get("/extra_types", response={200: None}, auth=None)
def extra_types(
    request: HttpRequest,
    extra_types: Union[
        schemas.DossierCreateJWTMortgage, schemas.DossierShowJWTMortgage
    ],
):
    """stub so the types are in the openapi scshema"""
    pass


@api.get("/dossier/create", response={302: str}, auth=None, url_name="create-dossier")
def create_dossier(request: HttpRequest, encoded_jwt: str):
    """Creates a new Dossier based on the provided parameters

    This endpoint is called via URL integration in the DBP (Digitales Berater Portal - Create a new Dossier).
    After the creation of the dossier, the user is redirected to the detail view of the newly created dossier.

    encoded_jwt is a jwt which contains the following parameters:
    * ***expiration*** defines the expiration time of the token timezone.now() + timedelta(minutes=60)
    * ***businesscase_parkey*** corresponds to the business case to which the dossier is associated.
    * ***pers*** sets the dossier property to pers, which indicates that only users with pers attributes are allow to open the dossier
    * ***fico*** sets the username (email address) of the responsible financial coach of the dossier

    * ***current_user*** is the username of the person trying to create the dossier.
        if the pers attribute is set, it is assumed that the current_user has the permission to view dossiers with the pers attribut of the dossier set to true

    ```python
    import jwt
    shared_secret = "a secret shared between bekb and hypodossier"
    parameters = {'exp': expiration, 'businesscase_parkey': '1234566', 'pers': True, "team": "team1", "fico": "<EMAIL>", "current_user": "<EMAIL>" }
    encoded_jwt = jwt.encode(parameters, shared_secret, algorithm='HS256')
    ```
    """
    result = create_dossier_from_jwt(
        encoded_jwt,
        schemas.DossierCreateJWTMortgage,
        use_fico=True,
        account_type="bekb",
    )

    if isinstance(result, HttpResponse):
        return result

    _, response_url = result
    return HttpResponseRedirect(redirect_to=response_url)


@api.put(
    "/dossier/properties",
    response={401: Message, 204: None, 404: Message},
    url_name="update-dossier-properties",
)
def update_dossier_properties(
    request, account_name: AccountNameMortgage, updates: List[DossierProperties]
) -> Tuple[int, str]:
    """Updates the properties of a Dossier.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountNameMortgage): The name of the account where the Dossier belongs.
        updates (List[DossierProperties]): The list of Dossier properties to update.

    Returns:
        int: HTTP status code indicating the result of the operation.
        str: Empty string.
    """

    account = get_object_or_404(Account, key=account_name)

    for update in updates:
        bekb_dossiers = BEKBDossierProperties.objects.filter(
            account=account, business_partner__parkey=update.business_parkey
        )

        if update.fico is not None:
            fico = update_or_create_user(update.fico, account)
            fico_role, _ = get_fico_role(account)

            for bekb_dossier in bekb_dossiers.all():
                UserInvolvement.objects.update_or_create(
                    dict(user=fico), role=fico_role, dossier=bekb_dossier.dossier
                )
    return 204, ""


@api.get(
    "/users",
    response={401: Message, 200: List[schemas.User]},
    url_name="list-hypodossier-users",
)
def list_hypodossier_users(
    request, account_name: AccountNameMortgage
) -> List[schemas.User]:
    """Lists all HypoDossier users for a specific account.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountNameMortgage): The name of the account.

    Returns:
        List[schemas.User]: The list of HypoDossier users for the specified account.
    """
    return get_hypodossier_users(account_name)


@api.put(
    "/users", response={401: Message, 204: str}, url_name="update-hypodossier-users"
)
def update_user_attributes(
    request, account_name: AccountNameMortgage, updates: List[schemas.UserAttributes]
) -> Tuple[int, str]:
    """Updates the attributes of a user.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountNameMortgage): The name of the account where the user belongs.
        updates (List[schemas.UserAttributes]): The list of user attributes to update.

    Returns:
        int: HTTP status code indicating the result of the operation.
        str: Empty string.
    """
    update_user_attributes_service(account_name, updates)
    return 204, ""


@api.get(
    "/business-parkeys",
    response={401: Message, 200: List[Parkey]},
    url_name="list-business-parkeys",
)
def list_business_parkeys(request, account_name: AccountNameMortgage) -> List[Parkey]:
    """Returns a list of all business parkeys (Geschäftsfall Parkey) actively used at HypoDossier.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountNameMortgage): The name of the account.

    Returns:
        List[Parkey]: The list of business parkeys.
    """
    ret = list_actively_used_business_parkeys(account_name)

    context = f"BEKBAPI GET /business-parkeys, account={account_name}"
    logger.info(f"{context}, {len(ret)} parkeys: {ret}")
    return ret


@api.put(
    "/business-partners",
    response={401: Message, 204: None},
    url_name="update-business-partners",
)
def update_business_partners(
    request, account_name: AccountNameMortgage, updates: schemas.BusinessPartnerUpdates
) -> Tuple[int, str]:
    """Updates the information of business partners.

    Args:
        request (HttpRequest): The HTTP request from the client.
        account_name (AccountNameMortgage): The name of the account where the business partners belong.
        updates (schemas.BusinessPartnerUpdates): The updates to be applied to the business partners.

    Returns:
        int: HTTP status code indicating the result of the operation.
        str: Empty string.
    """
    context = f"BEKBAPI PUT /business-partners, account={account_name}"
    logger.info(f"{context}, {len(updates.root)} updates: start processing...")

    account = Account.objects.get(key=account_name)

    for update in updates.root:
        old_business_partner = process_business_partner_update(account, update)
        process_partner_partner_update(account_name, account, update)

        logger.info(
            f"{context}, update={update}, old_business_partner={old_business_partner}"
        )

    logger.info(f"{context}, {len(updates.root)} processing done.")

    return 204, ""


@api.put(
    "/attributes", response={401: Message, 204: None}, url_name="update-attributes"
)
def create_or_update_attribute(
    request,
    account_name: AccountNameMortgage,
    attribute_updates: schemas.AttributeUpdates,
) -> Tuple[int, str]:
    """
    Creates or updates an attribute in the database.
    The key of the attribute consists of the entity and the key
    of the attribute.

    TODO: löscht BEKB Attribute oder werden die einfach nicht mehr referenziert?
    en: does BEKB delete attributes or are they simply no longer referenced?

    Args:
        request: HTTP request object.
        account_name: Account name.
        attribute_updates: Updates to the attributes.

    Returns:
        HTTP status code: 204 for successful update, 401 for unauthorized access.
    """

    api_create_or_update_attribute(
        account_name=account_name, attribute_updates=attribute_updates
    )
    return 204, ""


@api.put(
    "/businesscases",
    response={401: Message, 204: None},
    url_name="update-businesscases",
)
def update_list_of_businesscase(
    request, account_name: AccountNameMortgage, updates: schemas.BusinessCaseUpdates
) -> Tuple[int, str]:
    """
    Updates a list of business cases in the database.

    Args:
        request: HTTP request object.
        account_name: Account name.
        updates: Updates to the business cases.

    Returns:
        HTTP status code: 204 for successful update, 401 for unauthorized access.
    """

    context = f"BEKBAPI PUT /businesscases, account={account_name}"
    logger.info(f"{context}, {len(updates.root)} updates: start processing...")

    account = Account.objects.get(key=account_name)

    for update in updates.root:
        partner = Partner.objects.get(account=account, parkey=update.business_parkey)

        business_type = Attribute.objects.get(
            account=account,
            entity=Attribute.Entity.BusinessType.value,
            key=update.business_type,
        )
        business_status = Attribute.objects.get(
            account=account,
            entity=Attribute.Entity.BusinessStatus.value,
            key=update.business_status,
        )
        BusinessCase.objects.update_or_create(
            dict(
                business_type=business_type,
                business_status=business_status,
                mutation_date=update.mutation_date,
                mutation_user=update.mutation_user,
            ),
            account=account,
            business_number=update.business_number,
            business_partner=partner,
        )

        logger.info(f"{context}, update={update}")

    logger.info(f"{context}, {len(updates.root)} processing done.")

    return 204, ""


@api.put(
    "/realestateproperties",
    response={401: Message, 204: None},
    url_name="update-realestateproperties",
)
def update_realestate_properties(
    request,
    account_name: AccountNameMortgage,
    real_estate_properties: List[schemas.RealEstateProperty],
) -> Tuple[int, str]:
    """
    Updates real estate properties in the database. The 'property_number' is assumed to be the
    unique key for each property.

    Args:
        request: HTTP request object.
        account_name: Account name.
        real_estate_properties: Real estate properties to be updated.

    Returns:
        HTTP status code: 204 for successful update, 401 for unauthorized access.

    TODO:
        Haben die Liegenschaften einen Status (falls die gelöscht/deaktiviert werden?) oder benötigen
        wir ein Delete API Hypodossier geht davon aus, dass die property_number ein unique key ist
        für das property

        En: Do the properties have a status (in case they are deleted/deactivated?) or do we need a
        Delete API Hypodossier assumes that the property_number is a unique key for the property
    """

    log = DurationLog(
        f"BEKBAPI update_collateral_for_business_key(len={len(real_estate_properties)}"
    ).start()

    account = Account.objects.get(key=account_name)

    # Speedup by fetching attributes in advance is around 33% of overall execution
    property_types_dict = get_attributes_as_dict(
        account, Attribute.Entity.PropertyType.value
    )
    property_status_dict = get_attributes_as_dict(
        account, Attribute.Entity.RealEstatePropertyStatus.value
    )

    for update in real_estate_properties:
        property_partner, created = create_or_update_partner(
            account,
            update.property_partner.parkey,
            update.property_partner.name,
            update.property_partner.firstname,
        )
        property_type = property_types_dict[update.property_type]
        property_status = property_status_dict[update.status]
        RealEstateProperty.objects.update_or_create(
            dict(
                property_partner=property_partner,
                property_type=property_type,
                address_street=update.address_street,
                address_street_nr=update.address_street_nr,
                address_zip=update.address_zip,
                address_city=update.address_city,
                land_register_municipality=update.land_register_municipality,
                land_register_id=update.land_register_id,
                status=property_status,
            ),
            property_number=update.property_number,
            account=account,
        )
    log.add_event("done")
    log.log_all_events()
    return 204, ""


@api.put(
    "/businesscases/collateral",
    response={401: Message, 204: None},
    url_name="update-collateral",
)
def update_collateral_for_business_key(
    request, account_name: AccountNameMortgage, collaterals: List[schemas.Collateral]
) -> Tuple[int, str]:
    """
    Update the collateral information for a business case (Geschäftsfall Parkey).

    Designed to be used in batches

    Additional descriptive information might be required and has to be decided by bekb

    Args:
        request: The incoming request.
        account_name: The name of the account to update.
        collaterals: The list of collateral data to update.

    Returns:
        HTTP status code 204 and an empty string.
    """
    # Initialize the logging with the number of collaterals.
    log = DurationLog(
        f"BEKBAPI update_collateral_for_business_key(len={len(collaterals)}"
    ).start()
    log.add_event("before account")
    account = Account.objects.get(key=account_name)
    log.add_event("before loop started")

    optimize_query = True

    # If optimize_query is set to True, fetch all collateral types and statuses once,
    # store them in dictionaries for later use.
    if optimize_query:
        collateral_types_dict = get_attributes_as_dict(
            account, Attribute.Entity.CollateralType.value
        )
        collateral_status_dict = get_attributes_as_dict(
            account, Attribute.Entity.CollateralStatus.value
        )

    for update in collaterals:
        log.add_event("update loop started")
        business_partner = Partner.objects.get(
            account=account, parkey=update.business_parkey
        )
        log.add_event("business_partner_fetched")
        # logger.info(f'Found business_partner={business_partner}')
        business_case = BusinessCase.objects.get(
            account=account,
            business_partner=business_partner,
            business_number=update.business_number,
        )
        # logger.info(f"Found business case: {business_case}")

        # Check if the collateral partner is the same as the business partner.
        same_partner = (
            business_partner.parkey == update.collateral_partner.parkey
            and business_partner.name == update.collateral_partner.name
            and business_partner.firstname == update.collateral_partner.firstname
        )
        if optimize_query and same_partner:
            # If it is, use the same partner object.
            collateral_partner = business_partner
        else:
            # If not, create or update the partner.
            collateral_partner, created = create_or_update_partner(
                account,
                update.collateral_partner.parkey,
                update.collateral_partner.name,
                update.collateral_partner.firstname,
            )
        log.add_event("create_or_update_partner")

        # Get the collateral type and status either from the dictionary (if optimize_query is True)
        # or by individual queries (if optimize_query is False).
        if optimize_query:
            collateral_type = collateral_types_dict[update.collateral_type]
            collateral_status = collateral_status_dict[update.collateral_status]
        else:
            collateral_type = Attribute.objects.get(
                entity=Attribute.Entity.CollateralType.value,
                key=update.collateral_type,
                account=account,
            )

            collateral_status = Attribute.objects.get(
                entity=Attribute.Entity.CollateralStatus.value,
                key=update.collateral_status,
                account=account,
            )

        log.add_event("attributes fetched")

        # Update or create the collateral.
        collateral, created = Collateral.objects.update_or_create(
            dict(
                collateral_partner=collateral_partner,
                collateral_type=collateral_type,
                description=update.description,
                collateral_status=collateral_status,
                policy_number=update.policy_number,
            ),
            account=account,
            business_partner=business_partner,
            businesscase=business_case,
            collateral_number=update.collateral_number,
        )

        log.add_event("collateral updated")

        # Now add/delete relations for all real estate properties associated with this collateral.

        # delete old (unreferenced) ids
        new_prop_ids = [x.property_number for x in update.real_estate_properties]
        (
            CollateralRealEstateProperty.objects.filter(account=account)
            .filter(collateral=collateral)
            .exclude(realestate_property__property_number__in=new_prop_ids)
            .delete()
        )

        # Update or create all other ones
        for p in update.real_estate_properties:
            props, created = CollateralRealEstateProperty.objects.update_or_create(
                dict(
                    property_collateral_type=Attribute.objects.get(
                        entity=Attribute.Entity.PropertyCollateralType.value,
                        key=p.property_collateral_type,
                        account=account,
                    )
                ),
                account=account,
                collateral=collateral,
                realestate_property=RealEstateProperty.objects.get(
                    account=account, property_number=p.property_number
                ),
            )

        # relations = list(CollateralRealEstateProperty.objects.filter(account=account, collateral=collateral).all())
        # print(f'relations: {relations}')
        log.add_event("collateral with real estate all done")

    log.log_all_events()
    return 204, ""


@api.get(
    "/export/dossier-ready-for-export",
    response={401: Message, 200: List[schemas.DossierExport]},
    url_name="show-dossier-ready-for-export",
)
def show_dossier_ready_for_export(
    request, account_name: AccountNameMortgage
) -> List[schemas.DossierExport]:
    """
    Returns a list of dossier exports that are ready for export.

    Args:
        request: The incoming request object.
        account_name: The name of the account whose exports are to be retrieved.

    Returns:
        List[schemas.DossierExport]: A list of exports for the specified account.
    """

    # CAUTION: this shows all dossiers in work status EXPORT_ARCHIVE_AVAILABLE.
    # It does NOT show the work status READY_FOR_EXPORT_XXX
    #
    # Returns a list of dossier exports that have an export/archive ready and can be exported.

    account = Account.objects.get(key=account_name)

    # Fetching the BEKBDossierProperties associated with the account where the key is "EXPORT_ARCHIVE_AVAILABLE".
    candidates_for_export_bekb_dossiers = BEKBDossierProperties.objects.filter(
        account=account, dossier__work_status__key="EXPORT_ARCHIVE_AVAILABLE"
    ).all()

    ready_for_export_bekb_dossiers = candidates_for_export_bekb_dossiers.filter(
        dossier__bekbexport__isnull=False
    )

    broken_for_export_bekb_dossiers = candidates_for_export_bekb_dossiers.filter(
        dossier__bekbexport__isnull=True
    )

    for bd in broken_for_export_bekb_dossiers:
        dossier_uuid = str(bd.dossier.uuid)
        logger.error(
            "No dossier export found but dossier is in work status EXPORT_ARCHIVE_AVAILABLE",
            bekb_dossier=bd,
            dossier_uuid=bd.dossier.uuid,
        )

        log_work_status_transitions(
            dossier_uuid, context="Found these events for the broken dossier export"
        )

    return [
        schemas.DossierExport(
            dossier_uuid=bekb_dossier.dossier.uuid,
            export_uuid=bekb_dossier.dossier.bekbexport.uuid,
        )
        for bekb_dossier in ready_for_export_bekb_dossiers
    ]


@api.get(
    "/export/file",
    response={401: Message, 404: Message, 200: bytes},
    url_name="download-file",
)
def download_file(
    request, account_name: AccountNameMortgage, export_uuid: UUID
) -> StreamingHttpResponse:
    """
    Downloads a Dossier associated with a given account and export UUID.

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        export_uuid: The unique identifier of the export.

    Returns:
        StreamingHttpResponse: The file to be downloaded.
    """

    return api_download_file(account_name=account_name, export_uuid=export_uuid)


@api.post(
    "/export/feedback",
    response={
        401: Message,
        # 200: Message,
        204: None,
    },
    url_name="add-export-feedback",
)
def add_dossier_export_feedback(
    request,
    account_name: AccountNameMortgage,
    feedbacks: List[schemas.DossierExportFeedback],
    url_name="add-dossier-export-feedback",
) -> Tuple[int, Message]:
    """
    Adds feedback for a given export.

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        feedbacks: A list of DossierExportFeedback schema objects.

    Returns:
        int, str: 204 status code and an empty string indicating successful operation.
        HTTP 204 if update has been done successfully. HTTP 401 if not allowed.
    """
    api_add_dossier_export_feedback(account_name=account_name, feedbacks=feedbacks)

    return 204, ""
    # res_msg = ",".join(messages) if messages else ""
    # if res_msg:
    #     return 200, Message(detail=res_msg)
    # else:
    #     return 204, ""


@api.post(
    "/test/dossier/create/ready-for-export",
    url_name="add-some-sample-dossier-ready-for-export",
)
def add_some_sample_dossier_ready_for_export(
    request, account_name: AccountNameMortgage, count: int = 1
) -> Tuple[int, Message]:
    """
    Create up to count (max 10, default 1) fake dossier in state ready for export

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        count: The number of fake dossiers to be created (default is 1).

    Returns:
        int, Message: In case of a production account, returns a 403 status code and an error message.
    """

    try:
        create_test_dossiers_ready_for_export(
            account_name=account_name,
            factory_class=BekbAccountFactoryFaker,
            count=count,
        )
        return 200, Message(detail="Dossiers created successfully")
    except PermissionDenied as e:
        return 403, Message(detail=str(e))


@api.post(
    "/test/dossier/process-ready-for-export",
    response={403: Message, 201: List[Result[UUID]]},
    url_name="process-ready-for-export",
)
def process_ready_for_export(
    request,
    account_name: AccountNameMortgage,
    processing_request: schemas.ProcessReadyForExportRequest,
) -> Tuple[int, Message] or Tuple[int, List[Result[UUID]]]:
    """
    Checks whether a dossier is ready for export

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        processing_request: A ProcessReadyForExportRequest schema object containing dossier UUIDs
                             and 'not_updated_since' parameter.

    Returns:
        int, function: In case of a production account, returns a 403 status code and an error message.
        Otherwise, processes the dossiers and returns a 201 status code.
    """
    if account_name == AccountNameMortgage.bekbp:
        return 403, Message(
            detail=f"Unauthorized for production account {account_name}"
        )

    account = get_object_or_404(Account, key=account_name)

    return 201, process_ready_for_export_dossiers(
        account=account,
        dossier_uuids=processing_request.dossier_uuids,
        not_updated_since=processing_request.not_updated_since,
    )
