from typing import Optional, List

import pytest
from datetime import datetime
from uuid import UUID

from datetime import timezone as datetime_timezone
import structlog
from django.db import IntegrityError

from bekb.bekb_instance_type import get_bekb_instance_type, BekbInstanceType
from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.bekbload import load_document_category2ekd_mappings
from bekb.fakes import (
    BekbAccountFactoryFaker,
)

from bekb.models import (
    BEKBDossierProperties,
    CollateralAssignment,
    Partner,
    BusinessCase,
)
from bekb.schemas.schemas_services import DossierValidationType, DocumentKeys
from bekb.services import (
    create_index_file,
    SemanticDocumentExportFile,
    create_index_entry,
    determine_document_keys,
    validate_dossier,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
    prepare_document_package_for_export,
)
from core.temporary_path import temporary_path
from dossier.models import Dossier, BusinessCaseType
from semantic_document.models import SemanticDocument


from statemgmt.models import Status

logger = structlog.get_logger()


pytestmark = pytest.mark.django_db


@pytest.fixture
def mock_creation_time():
    return datetime(2023, 1, 2, 14, 30, 23, tzinfo=datetime_timezone.utc)


@pytest.fixture
def mock_batch_id():
    return UUID("********-1234-5678-1234-************")


@pytest.mark.parametrize(
    "instance_type,work_status,businesscase_type,expected_ekd,is_fipla,valid_document_category_keys",
    [
        # Normal mortgage stuff
        # Normal mortgage export with deal -> archived with mortgage mapping
        (
            BekbInstanceType.MORTGAGE,
            "READY_FOR_EXPORT_DEAL",
            "NEW_PURCHASE",
            None,
            False,
            None,
        ),
        # Fico export -> same as normal mortgage export but triggered by Fico instead of backoffice, archived with mortgage mapping
        (
            BekbInstanceType.MORTGAGE,
            "READY_FOR_EXPORT_FICO",
            "PROLONGATION",
            None,
            False,
            None,
        ),
        # No deal export -> -> archived with mortgage no deal mapping to EKD81
        (
            BekbInstanceType.MORTGAGE,
            "READY_FOR_EXPORT_NO_DEAL",
            "NEW_PURCHASE",
            "EKD81",
            False,
            None,
        ),
        # Fipla financial planning stuff -> archived with fipla mapping
        # Fipla export for "Fipla Diverses" (there are many categories that map there)
        (
            BekbInstanceType.FIPLA,
            "READY_FOR_EXPORT_FIPLA",
            "FINANCIAL_PLANNING",
            "EKD139",
            True,
            ["ANNUAL_REPORT_COMPANY"],
        ),
        # Fipla export for "Fipla Diverses" (there are many categories that map there)
        (
            BekbInstanceType.FIPLA,
            "READY_FOR_EXPORT_FIPLA",
            "FINANCIAL_PLANNING",
            "EKD139",
            True,
            ["ARCHITECT_CONTRACT"],
        ),
        # Fipla export for specific fipla category
        (
            BekbInstanceType.FIPLA,
            "READY_FOR_EXPORT_FIPLA",
            "FINANCIAL_PLANNING",
            "EKD120",
            True,
            ["BEKB_FIPLA_FORM"],
        ),
        # Fipla inheritance stuff -> all archived as EKD134
        # Fipla export for "Fipla Diverses" (there are many categories that map there)
        (
            BekbInstanceType.FIPLA,
            "READY_FOR_EXPORT_FIPLA",
            "INHERITANCE",
            "EKD134",
            True,
            ["ANNUAL_REPORT_COMPANY"],
        ),
        # Fipla export for "Fipla Diverses" (there are many categories that map there)
        (
            BekbInstanceType.FIPLA,
            "READY_FOR_EXPORT_FIPLA",
            "INHERITANCE",
            "EKD134",
            True,
            ["ARCHITECT_CONTRACT"],
        ),
        # Fipla export for specific fipla category
        (
            BekbInstanceType.FIPLA,
            "READY_FOR_EXPORT_FIPLA",
            "INHERITANCE",
            "EKD134",
            True,
            ["BEKB_FIPLA_FORM"],
        ),
    ],
)
def test_create_index_file_basic_scenarios(
    db,
    mock_creation_time,
    mock_batch_id,
    instance_type,
    work_status,
    businesscase_type,
    expected_ekd,
    is_fipla,
    valid_document_category_keys: Optional[List[str]],
):
    """

    :param db:
    :param instance_type:
    :param work_status:
    :param expected_ekd:
    :param is_fipla:
    :param mock_creation_time:
    :param mock_batch_id:
    :return:
    """
    # Create test dossier based on account type
    if instance_type == BekbInstanceType.MORTGAGE:
        bfac = BekbAccountFactoryFaker(
            update_statemgmt=True, default_bucket_name="dms-default-bucket"
        )
    else:
        bfac = BekbFiplaAccountFactory(
            update_statemgmt=True, default_bucket_name="dms-default-bucket"
        )

    # Create dossier with specific work status
    bekb_dossier_properties = bfac.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        possible_work_status_keys=[work_status],
        valid_document_category_keys=valid_document_category_keys,
    )

    bct = BusinessCaseType.objects.get(account=bfac.account, key=businesscase_type)
    bekb_dossier_properties.dossier.businesscase_type = bct

    semantic_document: SemanticDocument = (
        bekb_dossier_properties.dossier.semantic_documents.first()
    )
    assert semantic_document

    with temporary_path() as temp_path:
        document_package = prepare_document_package_for_export(
            bekb_dossier_properties, temp_path
        )

        # Generate index file
        index_file = create_index_file(
            mock_batch_id,
            bekb_dossier_properties,
            mock_creation_time,
            document_package,
        )

        # Verify common elements
        assert "CODEPAGE:819" in index_file
        assert f"COMMENT: {mock_creation_time.strftime('%d.%m.%Y')}" in index_file
        assert f"GROUP_FIELD_VALUE:{mock_batch_id}" in index_file

        # Verify document type specific elements
        if expected_ekd is None:
            instance_type = get_bekb_instance_type(
                bekb_dossier_properties.dossier.account.key
            )
            doc_key2ekd = load_document_category2ekd_mappings(instance_type)
            expected_ekd = doc_key2ekd[semantic_document.document_category.name]

        print(index_file)
        assert f"GROUP_FIELD_VALUE:{expected_ekd}" in index_file

        assert "GROUP_FIELD_NAME:PARKEY_G" in index_file

        # Verify FIPLA specific elements
        if is_fipla:
            assert "GROUP_FIELD_NAME:PARKEY_P" not in index_file
            assert "GROUP_FIELD_NAME:KRE_KEY" not in index_file
        else:
            assert "GROUP_FIELD_NAME:PARKEY_P" in index_file
            assert "GROUP_FIELD_NAME:KRE_KEY" in index_file


def test_create_index_file_with_collateral(db, mock_creation_time, mock_batch_id):
    # Create mortgage account with collateral
    account_factory = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    collateral_assignment = CollateralAssignment.objects.filter(
        account=bekb_dossier_properties.dossier.account,
        semantic_document__in=bekb_dossier_properties.dossier.semantic_documents.all(),
    ).first()

    with temporary_path() as temp_path:
        document_package = prepare_document_package_for_export(
            bekb_dossier_properties, temp_path
        )

        index_file = create_index_file(
            mock_batch_id,
            bekb_dossier_properties,
            mock_creation_time,
            document_package,
        )

        assert "GROUP_FIELD_NAME:PARKEY_D" in index_file
        assert (
            f"GROUP_FIELD_VALUE:{bekb_dossier_properties.partner_partner.parkey}"
            in index_file
        )
        assert (
            f"GROUP_FIELD_VALUE:{collateral_assignment.collateral.collateral_number}"
            in index_file
        )


def test_create_index_file_validation_errors(db, mock_creation_time, mock_batch_id):
    # Test with invalid work status
    account_factory = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties: BEKBDossierProperties = (
        account_factory.create_ready_for_export_dossier(
            min_num_documents=1,
            max_num_documents=1,
            possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
        )
    )

    work_status = bekb_dossier_properties.dossier.work_status
    work_status.key = "INVALID_STATUS"
    work_status.save()

    with pytest.raises(ValueError, match="Unexpected work status"):
        with temporary_path() as temp_path:
            document_package = prepare_document_package_for_export(
                bekb_dossier_properties, temp_path
            )
            create_index_file(
                mock_batch_id,
                bekb_dossier_properties,
                mock_creation_time,
                document_package,
            )

    # Test missing business partner for NO_DEAL
    with pytest.raises(
        IntegrityError,
        match='null value in column "business_partner_id" violates not-null constraint',
    ):
        bekb_dossier_properties.business_partner = None
        bekb_dossier_properties.save()


def test_create_index_file_document_formatting(db, mock_creation_time, mock_batch_id):
    account_factory = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    with temporary_path() as temp_path:
        document_package = prepare_document_package_for_export(
            bekb_dossier_properties, temp_path
        )
        index_file = create_index_file(
            mock_batch_id,
            bekb_dossier_properties,
            mock_creation_time,
            document_package,
        )

    # Verify file structure
    lines = index_file.split("\n")

    # Check header
    assert lines[0] == "COMMENT: OnDemand Generic Index File Format"
    assert "CODEPAGE:819" in lines

    # Check document entries
    doc_section = False
    for line in lines:
        if line.startswith("GROUP_FIELD_NAME:SCANDATUM"):
            doc_section = True

        if doc_section:
            # Verify field format
            if line.startswith("GROUP_FIELD_NAME:"):
                assert ":" in line
                assert len(line.split(":")) == 2
            elif line.startswith("GROUP_FIELD_VALUE:"):
                assert ":" in line
                assert len(line.split(":")) == 2


# Test Data Fixtures
@pytest.fixture
def mock_dossier_validation():
    return DossierValidationType(
        is_bekb_fipla=False,
        is_no_deal=False,
        parkey_g="123456",
        parkey_p="789012",
        businesscase_number="BC123",
        pers=True,
    )


@pytest.fixture
def mock_document_keys():
    return DocumentKeys(
        parkey_l="L123",
        parkey_d="D456",
        parkey_dok="DOK789",
        kre_key="KRE123",
        lie_key="LIE456",
        dec_key="DEC789",
        ekd_nr="EKD16",
    )


def test_basic_entry_creation(mock_dossier_validation, mock_document_keys):

    account_factory = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    semantic_document: SemanticDocument = (
        bekb_dossier_properties.dossier.semantic_documents.first()
    )

    semantic_document.created_at = datetime(
        2023, 1, 1, 10, 0, tzinfo=datetime_timezone.utc
    )

    semantic_document.save()

    batch_id = UUID("********-1234-5678-1234-************")
    creation = datetime(2023, 1, 2, 14, 30)

    document_files = {
        semantic_document.uuid: SemanticDocumentExportFile(filename="test.pdf", pages=5)
    }

    entry = create_index_entry(
        semantic_document,
        mock_document_keys,
        mock_dossier_validation,
        batch_id,
        creation,
        document_files,
    )

    # Verify required fields
    assert "GROUP_FIELD_NAME:SCANDATUM\nGROUP_FIELD_VALUE:01.01.2023" in entry
    assert "GROUP_FIELD_NAME:FORMULAR\nGROUP_FIELD_VALUE:EKD16" in entry
    assert f"GROUP_FIELD_VALUE:{batch_id}" in entry
    assert "GROUP_FIELD_VALUE:test.pdf" in entry
    assert "GROUP_FIELD_VALUE:5" in entry  # pages


def test_fipla_entry_creation(mock_document_keys):

    account_factory = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    semantic_document: SemanticDocument = (
        bekb_dossier_properties.dossier.semantic_documents.first()
    )

    semantic_document.created_at = datetime(
        2023, 1, 1, 10, 0, tzinfo=datetime_timezone.utc
    )

    semantic_document.save()

    fipla_validation = DossierValidationType(
        is_bekb_fipla=True,
        is_no_deal=None,
        parkey_g="123456",
        parkey_p="0",
        businesscase_number=None,
        pers=False,
    )

    entry = create_index_entry(
        semantic_document,
        mock_document_keys,
        fipla_validation,
        UUID("********-1234-5678-1234-************"),
        datetime(2023, 1, 2, 14, 30, tzinfo=datetime_timezone.utc),
        {
            semantic_document.uuid: SemanticDocumentExportFile(
                filename="test.pdf", pages=5
            )
        },
    )

    # Verify FIPLA-specific exclusions
    assert "GROUP_FIELD_NAME:PARKEY_P" not in entry
    assert "GROUP_FIELD_NAME:KRE_KEY" not in entry
    assert "GROUP_FIELD_NAME:SCANDATUM" in entry


def test_no_deal_keys(db):
    account_fact = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_fact.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        possible_work_status_keys=[VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE[0]],
    )

    dossier_validation = DossierValidationType(
        is_bekb_fipla=False,
        is_no_deal=True,
        parkey_g="123456",
        parkey_p="0",
        businesscase_number=None,
        pers=False,
    )

    doc_keys = determine_document_keys(
        semantic_document=SemanticDocument(),
        dossier_validation=dossier_validation,
        bekb_dossier=bekb_dossier_properties,
        doc_key2ekd={},
    )

    assert doc_keys.ekd_nr == "EKD81"
    assert doc_keys.parkey_l == "0"
    assert doc_keys.parkey_d == "0"


@pytest.mark.parametrize(
    "account_factory,businesscase_type,expected_document_keys",
    [
        (BekbFiplaAccountFactory, "INHERITANCE", DocumentKeys(ekd_nr="EKD134")),
        (
            BekbFiplaAccountFactory,
            "FINANCIAL_PLANNING",
            DocumentKeys(
                parkey_l="0",
                parkey_d="0",
                parkey_dok="0",
                kre_key="0",
                lie_key="0",
                dec_key="0",
                ekd_nr="EKD1888",
            ),
        ),
    ],
)
def test_fipla_inheritance_keys(
    db, account_factory, businesscase_type, expected_document_keys
):

    account_fact = account_factory(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_fact.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=[VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE[0]],
        businesscase_type_key=businesscase_type,
    )

    dossier: Dossier = bekb_dossier_properties.dossier

    semantic_document: SemanticDocument = (
        bekb_dossier_properties.dossier.semantic_documents.first()
    )

    dossier_validation = DossierValidationType(
        is_bekb_fipla=True,
        is_no_deal=None,
        parkey_g="123456",
        parkey_p="0",
        businesscase_number=None,
        pers=True,
    )

    dossier.businesscase_type, _ = BusinessCaseType.objects.get_or_create(
        key=businesscase_type, account=dossier.account
    )
    dossier.save()

    doc_keys = determine_document_keys(
        semantic_document=semantic_document,
        dossier_validation=dossier_validation,
        bekb_dossier=bekb_dossier_properties,
        doc_key2ekd={"CONSTRUCTION_PLAN": "EKD1888"},
    )

    assert doc_keys == expected_document_keys


def test_normal_case_with_collateral(db):

    account_fact = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_fact.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=[VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE[0]],
    )

    semantic_document: SemanticDocument = (
        bekb_dossier_properties.dossier.semantic_documents.first()
    )

    dossier_validation = DossierValidationType(
        is_bekb_fipla=False,
        is_no_deal=False,
        parkey_g="123456",
        parkey_p="789012",
        businesscase_number="BC123",
        pers=True,
    )

    doc_key2ekd = {semantic_document.document_category.name: "EKD16"}

    collateral_assignment = CollateralAssignment.objects.filter(
        account=bekb_dossier_properties.dossier.account,
        semantic_document=semantic_document,
    ).first()

    doc_keys = determine_document_keys(
        semantic_document=semantic_document,
        dossier_validation=dossier_validation,
        bekb_dossier=bekb_dossier_properties,
        doc_key2ekd=doc_key2ekd,
    )

    assert doc_keys.ekd_nr == "EKD16"
    assert (
        doc_keys.parkey_d == collateral_assignment.collateral.collateral_partner.parkey
    )
    assert doc_keys.parkey_l == collateral_assignment.property.property_partner.parkey
    assert doc_keys.lie_key == collateral_assignment.property.property_number
    assert doc_keys.dec_key == collateral_assignment.collateral.collateral_number


@pytest.mark.parametrize(
    "work_status,expected_fipla,expected_no_deal",
    [
        ("READY_FOR_EXPORT_DEAL", False, False),
        ("READY_FOR_EXPORT_NO_DEAL", False, True),
        ("READY_FOR_EXPORT_FIPLA", True, None),
    ],
)
def test_valid_status_combinations(db, work_status, expected_fipla, expected_no_deal):
    bekb_dossier = BEKBDossierProperties(
        dossier=Dossier(
            work_status=Status(key=work_status),
            businesscase_type=BusinessCaseType(key="FINANCIAL_PLANNING"),
        ),
        business_partner=Partner(parkey="123456"),
        partner_partner=(
            Partner(parkey="789012")
            if work_status != "READY_FOR_EXPORT_NO_DEAL"
            else None
        ),
        business_case=(
            BusinessCase(business_number="BC123")
            if work_status != "READY_FOR_EXPORT_NO_DEAL"
            else None
        ),
        pers=True,
    )

    validation = validate_dossier(bekb_dossier)

    assert validation.is_bekb_fipla == expected_fipla
    assert validation.is_no_deal == expected_no_deal
    assert validation.parkey_g == "123456"


def test_invalid_work_status(db):
    bekb_dossier = BEKBDossierProperties(
        dossier=Dossier(work_status=Status(key="INVALID_STATUS"))
    )

    with pytest.raises(ValueError, match="Unexpected work status"):
        validate_dossier(bekb_dossier)


def test_missing_required_fields_deal(db):
    bekb_dossier = BEKBDossierProperties(
        dossier=Dossier(work_status=Status(key="READY_FOR_EXPORT_DEAL")),
        business_partner=Partner(parkey="123456"),
        partner_partner=None,  # Missing required field
        business_case=None,  # Missing required field
        pers=True,
    )

    with pytest.raises(ValueError, match="Dossier is missing required fields"):
        validate_dossier(bekb_dossier)


def test_missing_business_partner_no_deal(db):
    account_factory = BekbAccountFactoryFaker(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_NO_DEAL"],
    )

    with pytest.raises(
        IntegrityError,
        match='null value in column "business_partner_id" violates not-null constraint',
    ):
        # This is already handled by business_partner being required by the model
        # but leave this logic here so its explicit
        bekb_dossier_properties.business_partner = None
        bekb_dossier_properties.save()
        # leave the validate_dossier so that we have a failover in case the model changes
        validate_dossier(bekb_dossier_properties)


def test_fipla_inheritance_validation(db):

    account_factory = BekbFiplaAccountFactory(
        update_statemgmt=True, default_bucket_name="dms-default-bucket"
    )

    bekb_dossier_properties = account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_FIPLA"],
    )

    dossier: Dossier = bekb_dossier_properties.dossier

    dossier.businesscase_type, _ = BusinessCaseType.objects.get_or_create(
        key="INHERITANCE", account=dossier.account
    )
    dossier.save()

    validation = validate_dossier(bekb_dossier_properties)

    assert validation.is_bekb_fipla is True
    assert validation.is_no_deal is None
    assert validation.parkey_g == bekb_dossier_properties.business_partner.parkey
