from faker import Faker

import doccheck
from bekb.fakes import BekbAccountFactoryFaker, handle_check_ekd_ids_and_external_titles


def test_load_bekbe_account(db):
    """
    Set up bekbe with
    - new document categries
    - all new businesscases
    - new doccheck
    @return:
    """

    Faker.seed(234342)
    bfac = BekbAccountFactoryFaker(update_statemgmt=True, update_doccheck=True)

    bc_types_doccheck = doccheck.models.BusinessCaseType.objects.filter(
        doc_check=bfac.account.active_doc_check
    ).all()
    assert len(bc_types_doccheck) >= 9

    account_key = bfac.account.key
    handle_check_ekd_ids_and_external_titles(account_key)
