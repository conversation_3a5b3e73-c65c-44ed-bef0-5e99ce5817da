from bekb.fakes import PATH_CURRENT_BEKB_STATEMGMT_EXPORT
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine, Status, StateCondition


def test_bekb_statemachine_loading(db, update_statemgmt: bool = True):
    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    assert state_machine
    if update_statemgmt:
        p = PATH_CURRENT_BEKB_STATEMGMT_EXPORT
        assert p.exists()
        str(p)
        update_state_machine(p, state_machine.name)

    state_machine_new = StateMachine.objects.get(name="Dossier Status BEKB Initial")

    states = list(Status.objects.filter(state_machine=state_machine_new))

    status_keys = sorted([s.key for s in states])

    assert len(states) == 11
    assert status_keys == [
        "EXPORT_ARCHIVE_AVAILABLE",
        "EXPORT_DONE",
        "EXPORT_ERROR",
        "IN_BACK_OFFICE",
        "IN_CREDIT_OFFICE",
        "IN_FRONT_OFFICE",
        "READY_FOR_BACK_OFFICE",
        "READY_FOR_CREDIT_OFFICE",
        "READY_FOR_EXPORT_DEAL",
        "READY_FOR_EXPORT_FICO",
        "READY_FOR_EXPORT_NO_DEAL",
    ]

    state_conditions = list(StateCondition.objects.all())
    state_condition_names = sorted([sc.name for sc in state_conditions])
    assert state_condition_names == [
        "all_have_edkd_nr",  # this could be deleted
        "bekb_all_collaterals_mapped",
        "bekb_all_coverings_mapped",
        "bekb_all_have_ekd_nr",
        "bekb_fico_can_archive",
        "bekb_fico_can_not_archive",
        "bekb_has_bekb_properties",
        "bekb_has_kbus_business_nr",
        "bekb_has_partner_partner",
        "has_at_least_one_document",
        "has_businesscase_type",
        "is_doccheck_fulfilled",
        "is_system",
        "is_true",
        "is_user",
    ]

    assert state_machine_new.start_status.key == "IN_FRONT_OFFICE"

    transitions_from_start = list(state_machine_new.start_status.from_states.all())
    assert len(transitions_from_start) == 3
