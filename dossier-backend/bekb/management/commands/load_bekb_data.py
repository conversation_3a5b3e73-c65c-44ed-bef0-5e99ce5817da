import structlog
from pathlib import Path
from typing import <PERSON>ple

import djclick as click
from django.contrib.auth import get_user_model
from faker import Faker

from bekb.constants import archive
from bekb.fakes import BekbAccountFactoryFaker, load_bekb_document_categories
from doccheck.export import import_doccheck
from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import Dossier, Languages, Account
from dossier.services import (
    add_case_to_dossiers_with_doccheck,
)

# Management commands to set up a) the bekbe account and b) create lots of useful test dossiers.
# a) python manage.py load_bekb_data load-account bekbe
# b) python manage.py load_bekb_data load-dossiers-all bekbe

User = get_user_model()
logger = structlog.get_logger()

DOSSIER_NAME_PREFIX_TEST = "HDTEST "


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
@click.option("-update_statemgmt", default=True, type=click.BOOL)
@click.option("-update_doccheck", default=True, type=click.BOOL)
def load_account(
    account_key: str,
    update_statemgmt: bool,
    update_doccheck: bool,
):
    """
    Create or update a bekb account with close to production config. This loads
    document categories and optionally updates the state machine and doccheck.

    Example:

    python manage.py reset-db
    python manage.py load_bekb_data load-account bekbe

    """
    Faker.seed(234342)
    bfac = BekbAccountFactoryFaker(
        update_statemgmt=update_statemgmt,
        update_doccheck=update_doccheck,
        account_key=account_key,
    )

    doc_check = import_doccheck(
        Path("doccheck/configurations/bekb_doccheck_240522.json"),
        bfac.account.key,
    )
    doc_check.save()
    bfac.account.active_doc_check = doc_check
    bfac.account.save()

    add_case_to_dossiers_with_doccheck(account_key)


@grp.command()
@click.argument("account_key")
def update_document_categories(account_key: str):
    """
    Load / update all document categories for BEKB. Do not rely on the factory here
    as the factory changes the account itself on initialization.

    python manage.py load_bekb_data update-document-categories bekbe

    @param account_key:
    @return:
    """
    load_bekb_document_categories(account_key)


@grp.command()
@click.argument("account_key")
@click.option("-num_export_archives", default=0, type=click.INT)
@click.option("-num_dossiers_ready_for_export", default=0, type=click.INT)
@click.option("-num_dossiers_sample", default=0, type=click.INT)
@click.option("-add_statemgmt_dossiers", default=False, type=click.BOOL)
def load_dossiers_misc(
    account_key: str,
    num_export_archives: int,
    num_dossiers_ready_for_export: int,
    num_dossiers_sample: int,
    add_statemgmt_dossiers: bool,
):
    """
    Load misc dossiers in various status situations

    python manage.py load_bekb_data load-dossiers-misc bekbe
    python manage.py load_bekb_data load-dossiers-misc bekbe -add_statemgmt_dossiers=True

    @param account_key:
    @param num_export_archives:
    @param num_dossiers_ready_for_export:
    @param num_dossiers_sample:
    @return:
    """
    if (
        not num_export_archives
        and not num_dossiers_ready_for_export
        and not num_dossiers_sample
        and not add_statemgmt_dossiers
    ):
        logger.error(
            "Number of dossiers to be created will be 0. Set at least one of -num_export_archives, -num_dossiers_ready_for_export, -num_dossiers_sample, -add_statemgmt_dossiers"
        )
        return

    do_load_dossiers_misc(
        account_key,
        num_export_archives,
        num_dossiers_ready_for_export,
        num_dossiers_sample,
        add_statemgmt_dossiers,
    )


def do_load_dossiers_misc(
    account_key: str,
    num_export_archives: int,
    num_dossiers_ready_for_export: int,
    num_dossiers_sample: int,
    add_statemgmt_dossiers: bool,
):
    """

    @param account_key:
    @param num_export_archives:
    @param num_dossiers_ready_for_export:
    @param num_dossiers_sample:
    @param add_statemgmt_dossiers:
    @return:
    """
    logger.info(
        f"load_bekbe_account(num_export_archives={num_export_archives}, num_dossiers_ready_for_export={num_dossiers_ready_for_export}, num_dossiers_sample={num_dossiers_sample})..."
    )
    account, bfac = get_account_and_bekb_factory(account_key)

    for i in range(num_export_archives):
        bd = bfac.create_export_archive_available_dossier()
        bd.dossier.name = f"{DOSSIER_NAME_PREFIX_TEST}{bd.dossier.name}"
        bd.dossier.save()

    for i in range(num_dossiers_ready_for_export):
        bd = bfac.create_ready_for_export_dossier()
        bd.dossier.name = f"{DOSSIER_NAME_PREFIX_TEST}{bd.dossier.name}"
        bd.dossier.save()

    for i in range(num_dossiers_sample):
        bd = bfac.create_sample_dossier()
        bd.dossier.name = f"{DOSSIER_NAME_PREFIX_TEST}{bd.dossier.name}"
        bd.dossier.save()

    if add_statemgmt_dossiers:
        bd = bfac.create_sample_dossier_prolongation()
        bd.save()
        d = Dossier.objects.get(uuid=bd.dossier.uuid)
        d.name = f"{DOSSIER_NAME_PREFIX_TEST}STATUS {bd.dossier.name}"
        d.save()


@grp.command()
@click.argument("account_key")
def load_dossiers_group_merge_testing(account_key: str):
    """
    Merge testing dossiers without collaterals

    python manage.py load_bekb_data load-dossiers-group-merge-testing bekbe

    @param account_key:
    @return:
    """
    do_load_dossiers_group_merge_testing(account_key)


def get_or_create_account_test_user(account_key: str):
    username = f"{account_key}<EMAIL>"
    user, _ = User.objects.get_or_create(username=username)
    return user


def do_load_dossiers_group_merge_testing(account_key: str):
    account, bfac = get_account_and_bekb_factory(account_key)
    user = get_or_create_account_test_user(account_key)

    d_merge_0 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}GROUPMERGE #1: Merge PLAN_ANY PLAN_FLOOR CONSTRUCTION_PLAN",
        num_business_cases=0,
        num_collaterals=0,
        collateral_type=bfac.attribute_collateral_grundpfand,
        add_semantic_documents=False,
    )

    dossier: Dossier = d_merge_0.dossier
    dossier.owner = user
    dossier.lang = Languages.ENGLISH
    dossier.save()

    add_some_fake_semantic_documents(
        d_merge_0.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["PLAN_ANY"],
        allow_empty_docs=False,
    )
    add_some_fake_semantic_documents(
        d_merge_0.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["PLAN_FLOOR"],
        allow_empty_docs=False,
    )
    add_some_fake_semantic_documents(
        d_merge_0.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        allow_empty_docs=False,
    )

    # One Doc per category, no combinations/merge
    d_merge_1 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}GROUPMERGE #2: One Doc per category no combinations merge",
        num_business_cases=0,
        num_collaterals=0,
        collateral_type=bfac.attribute_collateral_grundpfand,
        add_semantic_documents=False,
    )

    dossier: Dossier = d_merge_1.dossier
    dossier.owner = user
    dossier.lang = Languages.ENGLISH
    dossier.save()

    add_some_fake_semantic_documents(
        d_merge_1.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["PLAN_FLOOR"],
        allow_empty_docs=False,
    )
    add_some_fake_semantic_documents(
        d_merge_1.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["SALARY_BONUS"],
        allow_empty_docs=False,
    )
    add_some_fake_semantic_documents(
        d_merge_1.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["PROJECT_BUDGET"],
        allow_empty_docs=False,
    )

    # Mixture of everything, all categories for merging
    d_merge_2 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}GROUPMERGE #3: All categories for merging",
        num_business_cases=0,
        num_collaterals=0,
        collateral_type=bfac.attribute_collateral_grundpfand,
        add_semantic_documents=False,
    )

    dossier: Dossier = d_merge_2.dossier
    dossier.owner = user
    dossier.lang = Languages.ENGLISH
    dossier.save()

    for grouping_name, grouping_values in archive.ALL_GROUP_TYPES.items():
        add_some_fake_semantic_documents(
            d_merge_2.dossier,
            num_docs=5,
            max_pages=2,
            min_num_pages=2,
            no_page_objects_per_page=1,
            valid_document_category_keys=grouping_values,
            allow_empty_docs=False,
        )

    # Group by same category
    d_merge_1 = bfac.create_ready_for_export_dossier(
        min_num_documents=20,
        max_num_documents=20,
        valid_document_category_keys=["CONSTRUCTION_PLAN"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
        allow_empty_docs=False,
    )

    dossier: Dossier = d_merge_1.dossier
    dossier.owner = user
    dossier.name = f"{DOSSIER_NAME_PREFIX_TEST}GROUPMERGE #4: CONSTRUCTION_PLAN ALL"
    dossier.lang = Languages.ENGLISH
    dossier.save()


@grp.command()
@click.argument("account_key")
def load_dossiers_group_merge_testing_collateral(account_key: str):
    """
    Merge testing dossiers with collaterals

    python manage.py load_bekb_data load-dossiers-group-merge-testing-collateral bekbe

    @param account_key:
    @return:
    """
    do_load_dossiers_group_merge_testing_collateral(account_key)


def do_load_dossiers_group_merge_testing_collateral(account_key):
    account, bfac = get_account_and_bekb_factory(account_key)
    get_or_create_account_test_user(account_key)

    # Create DAG1 - each doc has own collateral
    bfac.create_ready_for_export_dossier(
        min_num_documents=5,
        max_num_documents=5,
        max_pages=2,
        min_num_pages=2,
        allow_empty_docs=False,
        valid_document_category_keys=archive.ALL_GROUP_TYPES["PLAN_ANY"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}GROUPMERGE_COLL #1:  Different collaterals, same category group, no merge expected",
    )

    # Create DAG2 - always 2 docs have same collateral
    bekb_dossier = bfac.create_ready_for_export_dossier(
        min_num_documents=5,
        max_num_documents=5,
        max_pages=2,
        min_num_pages=2,
        allow_empty_docs=False,
        valid_document_category_keys=archive.ALL_GROUP_TYPES["PLAN_ANY"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}GROUPMERGE_COLL #1: Different collaterals, same category group, duplicates, merge expected",
    )

    for sem_doc in bekb_dossier.dossier.semantic_documents.all():
        # Create a twin document with same collateral for each document
        new_sem_docs = add_some_fake_semantic_documents(
            bekb_dossier.dossier,
            num_docs=1,
            max_pages=2,
            min_num_pages=2,
            no_page_objects_per_page=1,
            valid_document_category_keys=archive.ALL_GROUP_TYPES["PLAN_ANY"],
            allow_empty_docs=False,
        )
        assert len(new_sem_docs) == 1
        new_sem_docs[0].collateralassignment = sem_doc.collateralassignment
        new_sem_docs[0].save()

    # Create DAG3 - Same as DAG2 but one without collateral
    bekb_dossier = bfac.create_ready_for_export_dossier(
        min_num_documents=5,
        max_num_documents=5,
        max_pages=2,
        min_num_pages=2,
        allow_empty_docs=False,
        valid_document_category_keys=archive.ALL_GROUP_TYPES["PLAN_ANY"],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}GROUPMERGE_COLL #1: Different collaterals, same category group, duplicates, one without collateral, merge "
        "expected",
    )

    for sem_doc in bekb_dossier.dossier.semantic_documents.all():
        new_sem_docs = add_some_fake_semantic_documents(
            bekb_dossier.dossier,
            num_docs=1,
            max_pages=2,
            min_num_pages=2,
            no_page_objects_per_page=1,
            valid_document_category_keys=archive.ALL_GROUP_TYPES["PLAN_ANY"],
            allow_empty_docs=False,
        )
        assert len(new_sem_docs) == 1
        new_sem_docs[0].collateralassignment = sem_doc.collateralassignment
        new_sem_docs[0].save()

    # One doc without a collateral
    add_some_fake_semantic_documents(
        bekb_dossier.dossier,
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=archive.ALL_GROUP_TYPES["PLAN_ANY"],
        allow_empty_docs=False,
    )


def get_account_and_bekb_factory(
    account_key, update_statemgmt=False, update_doccheck=False
) -> Tuple[Account, BekbAccountFactoryFaker]:
    account = Account.objects.get(key=account_key)
    assert account
    bfac = BekbAccountFactoryFaker(
        account=account,
        update_statemgmt=update_statemgmt,
        update_doccheck=update_doccheck,
    )
    assert bfac
    return account, bfac


@grp.command()
@click.argument("account_key")
@click.option("-num_dossiers", default=1, type=click.INT)
def load_dossiers_pers(account_key: str, num_dossiers: int):
    """
    These dossiers are only visible for users with the BEKB/account_key/PERS role, e.g. <EMAIL>

    python manage.py load_bekb_data load-dossiers-pers bekbe -num_dossiers 3

    """
    do_load_dossiers_pers(account_key, num_dossiers)


def do_load_dossiers_pers(account_key: str, num_dossiers: int):
    account, bfac = get_account_and_bekb_factory(account_key)

    for i in range(0, num_dossiers):
        bekb_dossier = bfac.create_sample_dossier(pers=True)
        bekb_dossier.dossier.name = (
            f"{DOSSIER_NAME_PREFIX_TEST}PERS #{i + 1}: {bekb_dossier.dossier.name}"
        )
        bekb_dossier.dossier.save()


@grp.command()
@click.argument("account_key")
def load_dossiers_standard(account_key: str):
    """
    python manage.py load_bekb_data load-dossiers-standard bekbe

    """
    do_load_dossiers_standard(account_key)


def do_load_dossiers_standard(account_key: str):
    account, bfac = get_account_and_bekb_factory(account_key)
    d0 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}DOSSIER #00 (no businesscase)",
        num_business_cases=0,
        num_collaterals=0,
        collateral_type=bfac.attribute_collateral_grundpfand,
        add_semantic_documents=False,
    )
    add_some_fake_semantic_documents(
        d0.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d1x1 = bfac.create_sample_dossier_grundpfand()
    d = Dossier.objects.get(uuid=d1x1.dossier.uuid)
    d.name = f"{DOSSIER_NAME_PREFIX_TEST}{d.name}"
    d.save()
    add_some_fake_semantic_documents(
        d1x1.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d1x2 = bfac.create_sample_dossier_grundpfand(
        bfac.property_collateral_type_verwaltungs
    )
    d = Dossier.objects.get(uuid=d1x2.dossier.uuid)
    d.name = f"{DOSSIER_NAME_PREFIX_TEST}{d.name}"
    d.save()
    add_some_fake_semantic_documents(
        d1x2.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d1x3 = bfac.create_sample_dossier_grundpfand(bfac.property_collateral_type_neben)
    d = Dossier.objects.get(uuid=d1x3.dossier.uuid)
    d.name = f"{DOSSIER_NAME_PREFIX_TEST}{d.name}"
    d.save()
    add_some_fake_semantic_documents(
        d1x3.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d10 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}DOSSIER #10 (1 businesscase, 1 not-matching collateral)",
        num_business_cases=1,
        num_collaterals=1,
        collateral_type=bfac.attribute_collateral_personenversicherung,
        add_semantic_documents=False,
    )
    add_some_fake_semantic_documents(
        d10.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d11 = bfac.create_sample_dossier_personenversicherung()
    d = Dossier.objects.get(uuid=d11.dossier.uuid)
    d.name = f"{DOSSIER_NAME_PREFIX_TEST}{d.name}"
    d.save()
    add_some_fake_semantic_documents(
        d11.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["PENSION3A_INSURANCE_LETTER_REDEMPTION"],
    )

    d20 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}DOSSIER #20 (1 businesscase, 10 collaterals grundpfand)",
        num_business_cases=1,
        num_collaterals=10,
        collateral_type=bfac.attribute_collateral_grundpfand,
        add_semantic_documents=False,
    )
    add_some_fake_semantic_documents(
        d20.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d30 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}DOSSIER #30 (1 businesscase, 10 collaterals misc)",
        num_business_cases=1,
        num_collaterals=10,
        collateral_type=None,
        add_semantic_documents=False,
    )
    add_some_fake_semantic_documents(
        d30.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d40 = bfac.create_sample_dossier_two_hauptdeckung()
    add_some_fake_semantic_documents(
        d40.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )

    d90 = bfac.create_sample_dossier(
        pers=False,
        dossier_name=f"{DOSSIER_NAME_PREFIX_TEST}DOSSIER #90 (8 businesscase, 10 collaterals misc each, 10 docs)",
        num_business_cases=8,
        num_collaterals=10,
        collateral_type=None,
        add_semantic_documents=False,
    )
    add_some_fake_semantic_documents(
        d90.dossier,
        num_docs=10,
        allow_empty_docs=False,
        valid_document_category_keys=[
            "PASSPORT_CH",
            "RESIDENCE_PERMIT",
            "PENSION3A_INSURANCE_CONTRACT",
            "EXTRACT_FROM_LAND_REGISTER",
            "PROPERTY_INSURANCE",
        ],
    )


@grp.command()
@click.argument("account_key")
def load_dossiers_all(account_key: str):
    """
    Load all dossiers (standard, pers, ...)

    python manage.py load_bekb_data load-dossiers-all bekbe

    @param account_key:
    @return:
    """
    do_load_dossiers_misc(
        account_key,
        num_export_archives=2,
        num_dossiers_ready_for_export=2,
        num_dossiers_sample=2,
        add_statemgmt_dossiers=True,
    )
    do_load_dossiers_pers(account_key, 2)
    do_load_dossiers_standard(account_key)
    do_load_dossiers_group_merge_testing(account_key)
    do_load_dossiers_group_merge_testing_collateral(account_key)
