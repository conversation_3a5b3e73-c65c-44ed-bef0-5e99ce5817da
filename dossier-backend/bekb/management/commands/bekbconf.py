import djclick as click
import structlog

from bekb.bekb_instance_type import BekbInstanceType
from bekb.bekbconf import (
    add_bekbconf_ekd16,
    add_doccats_for_new_businesscases,
    optionally_create_doccat,
    add_bekbconf_ekd142,
)
from bekb.fakes import (
    PATH_CURRENT_BEKB_STATEMGMT_EXPORT,
    handle_update_ekd_ids_and_external_titles,
    handle_check_ekd_ids_and_external_titles,
    PATH_CURRENT_BEKB_BUSINESSCASE_TYPES,
    handle_check_bekb_config,
)
from doccheck import DOCCHECK_PATH
from doccheck.export import (
    import_doccheck,
    sync_businesscase_types_from_account,
    sync_doccat_names_from_account,
)
from dossier.models import Account, DocumentCategory
from dossier.services import (
    create_businesscase_type_import,
    add_case_to_dossiers_with_doccheck,
)
from statemgmt.export import update_state_machine

logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
@click.option("-dry_run", default=True, type=click.BOOL)
def add_231100_ekd16(account_key: str, dry_run: bool):
    """
    Add new document category. If it already exists, do nothing

    Example:
    python manage.py bekbconf add-231100-ekd16 default
    python manage.py bekbconf add-231100-ekd16 default -dry_run=False

    @param account_key: Identifies account
    @param dry_run: If True, nothing is updated. Default False
    @return: Nothing. Prints out feedback
    """
    add_bekbconf_ekd16(account_key, dry_run)


@grp.command()
@click.argument("account_key")
@click.option("-dry_run", default=True, type=click.BOOL)
def update_240407_account_for_new_businesscases(account_key: str, dry_run: bool):
    """
    Add a few new document categories needed for
    a) the new business cases and
    b) for the auto grouping feature.
    If it already exists, do nothing

    Example:
    - python manage.py bekbconf update-240407-account-for-new-businesscases default
    - python manage.py bekbconf update-240407-account-for-new-businesscases bekbe -dry_run=False

    @param account_key: Identifies account
    @param dry_run: If True, nothing is updated. Default False
    @return: Nothing. Prints out feedback
    """
    logger.info(
        f"add_240325_doccats_for_new_businesscases({account_key}, {dry_run})..."
    )
    add_doccats_for_new_businesscases(account_key, dry_run)

    # Add new businesscase types, e.g. "PROLONGATION"
    if not dry_run:
        account = Account.objects.get(key=account_key)
        account.show_document_category_external = True
        account.enable_rendering_plans_tab = True
        account.enable_dossier_assignment_to_someone_else = True
        account.enable_semantic_page_image_lazy_loading = True
        account.enable_bekb_automatic_collateral = True
        account.save()

        p = PATH_CURRENT_BEKB_BUSINESSCASE_TYPES
        create_businesscase_type_import(p, account_key)

        p = PATH_CURRENT_BEKB_STATEMGMT_EXPORT
        assert p.exists()
        state_machine = update_state_machine(
            p, account.active_work_status_state_machine.name
        )
        assert state_machine is not None

        account = Account.objects.get(key=account_key)
        if account.active_doc_check:
            doc_check_key = account.active_doc_check.key
        else:
            doc_check_key = f"{account_key}_doccheck"

        p = DOCCHECK_PATH / "configurations/bekb_doccheck_240507.json"
        assert p.exists()
        doc_check = import_doccheck(p, doc_check_key)

        if not account.active_doc_check:
            account.active_doc_check = doc_check
            account.save()

        sync_businesscase_types_from_account(account_key, doc_check_key)

        sync_doccat_names_from_account(
            account_key=account.key, doc_check_key=doc_check_key
        )

        add_case_to_dossiers_with_doccheck(account_key)


@grp.command()
@click.argument("account_key")
def update_240718_add_bill_property(account_key: str):
    """
    - Umbenennen der bestehenden Kategorie "920-EKD44 Rechnung" (BILL_MISC) zu
      "690-EKD44 Rechnung Liegenschaft" (BILL_PROPERTY)
    - Neue Kategorie hinzufügen "920-EKD131 Rechnung" (BILL_MISC)

    Damit werden alle bestehenden "920-EKD44" richtig in der Kategorie
    "EKD44 Renovationsarbeiten / Kostenvoranschlag" archiviert.

    Neu verarbeitete Rechnungen werden unterschieden nach Rechnungen zum Objekt
    (690) und Rechnungen zur Person (920). Letzteres sind z.B. Zahnarztrechnungen,
    Weiterbildungsrechnungen, ...

    To run on instance:
    # rename BILL_MISC to PROPERTY_BILL and set varia to exclude from recommendation
    python manage.py bekbconf update-240718-add-bill-property bekbe
    # Fetch all new document categories but do not change existing IDs
    python manage.py update_dossier_categories bekbe assets/document_category/default/DocumentCategory-2025-03-10.json -update_id=False -update_exclude_for_recommendation=False
    # Add EKD numbers and external titles DE, FR to new categories
    python manage.py bekbconf update-ekd-ids-and-external-titles bekbe -dry_run=False
    # Verify that config correct:
    python manage.py bekbconf check-ekd-ids-and-external-titles bekbp
    python manage.py bekbconf check-bekb-config bekbp

    # Helper: this resets the document categories that existed before the upgrade to the version of before the upgrade
    # Added doc cats remain unchanged
    python manage.py update_dossier_categories bekbp bekb/data/document_categories/240723_production_before_upgrade/FullDocumentCategory-2024-07-23.json

    """
    account = Account.objects.get(key=account_key)

    dc_varia = DocumentCategory.objects.get(name="MISC_CAT", account=account)
    if not dc_varia.exclude_for_recommendation:
        dc_varia.exclude_for_recommendation = True
        dc_varia.save()

    dc_reg = DocumentCategory.objects.get(
        name="REGISTRATION_LAND_REGISTER", account=account
    )
    if dc_reg.id == "782-EKD133":
        dc_reg.id = "694-EKD133"
        logger.info("change id", dc=dc_reg)
        dc_reg.save()

    dc_reg2 = DocumentCategory.objects.get(name="LAND_REGISTER_BILL", account=account)
    if dc_reg2.id == "955-EKD127":
        dc_reg2.id = "697-EKD127"
        logger.info("change id", dc=dc_reg2)
        dc_reg2.save()

    dc_reg3 = DocumentCategory.objects.get(name="LAND_REGISTER_MISC", account=account)
    if dc_reg3.id == "789-EKD133":
        dc_reg3.id = "698-EKD133"
        logger.info("change id", dc=dc_reg3)
        dc_reg3.save()

    dc_ali = DocumentCategory.objects.get(name="CONFIRMATION_ALIMONY", account=account)
    if dc_ali.id == "368-EKD51":
        dc_ali.id = "365-EKD51"
        logger.info("change id", dc=dc_ali)
        dc_ali.save()

    dc_old = DocumentCategory.objects.get(name="BILL_MISC", account=account)
    assert dc_old
    if dc_old.id == "920-EKD131":
        logger.info(
            "Category change for BILL_MISC -> BILL_PROPERTY and adding of new BILL_MISC has already happend"
        )
        return

    assert dc_old.id == "920-EKD44"

    dc_old.id = "690-EKD44"
    dc_old.name = "PROPERTY_BILL"
    dc_old.de = "Rechnung Liegenschaft"
    dc_old.en = "Invoice property"
    dc_old.fr = "Facture immeuble"
    dc_old.it = "Fattura di proprietà"
    dc_old.additional_search_terms_de = "Abrechnung"
    dc_old.description_de = "Rechnung für Kosten im Zusammenhang mit einer Liegenschaft (nicht Liegenschaftsabrechnung)"
    dc_old.save()

    dc_new = DocumentCategory(
        id="920-EKD131",
        name="BILL_MISC",
        account=account,
        de="Rechnung",
        en="Bill Misc",
        fr="Facture",
        it="Fattura",
        de_external="Personen Diverses",
        fr_external="Personnes divers",
    )

    optionally_create_doccat(account, dc_new, dry_run=False)


@grp.command()
@click.argument("account_key")
@click.option("-dry_run", default=True, type=click.BOOL)
@click.option("-override", default=False, type=click.BOOL)
def add_241022_ekd142_myky(account_key: str, dry_run: bool, override: bool):
    """
    Add new document category. If it already exists, do nothing

    Example:
    python manage.py bekbconf add-241022-ekd142-myky bekbe
    python manage.py bekbconf add-241022-ekd142-myky bekbe -dry_run=False -override=False

    @param account_key: Identifies account
    @param dry_run: If True, nothing is updated. Default False
    @return: Nothing. Prints out feedback
    """
    add_bekbconf_ekd142(account_key, dry_run, override)


@grp.command()
@click.argument("account_key")
@click.option("-dry_run", default=True, type=click.BOOL)
def update_ekd_ids_and_external_titles(account_key: str, dry_run: bool):
    """
    Example:
    python manage.py bekbconf update-ekd-ids-and-external-titles bekbp -dry_run=
    @param account_key:
    @return:
    """
    handle_update_ekd_ids_and_external_titles(
        account_key, BekbInstanceType.MORTGAGE, dry_run
    )


@grp.command()
@click.argument("account_key")
def check_ekd_ids_and_external_titles(account_key: str):
    """
    Example:
    python manage.py bekbconf check-ekd-ids-and-external-titles bekbp
    @param account_key:
    @return: Nothing. It logs which document categories do not have an EKD in the id and which are missing an
    external title in DE and FR
    """
    handle_check_ekd_ids_and_external_titles(account_key)


@grp.command()
@click.argument("account_key")
def check_bekb_config(account_key: str):
    """
    Check if document categories and the mapping for the export are consistent

    Example:
    python manage.py bekbconf check-bekb-config bekbp
    """
    # This will have a failed assert if the config is not valid
    doc_cat_map, mappings = handle_check_bekb_config(
        account_key, BekbInstanceType.MORTGAGE
    )
    print(
        f"Found {len(doc_cat_map.items())} entries in document categories (with an EKD mapping)"
    )
    print(f"Found {len(mappings.items())} EKD mappings")

    for dc_name in doc_cat_map.keys():
        dc = doc_cat_map[dc_name]
        dc_id = dc.id  # e.g. "210-EKD131"
        assert dc_id.endswith(mappings[dc_name])

    print("Configuration is valid.")
