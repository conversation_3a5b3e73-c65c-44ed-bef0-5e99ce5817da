from enum import Enum

from django.db import models
from django.db.models import CASCADE, SET_NULL

from core.behaviors import Timestampable
from dossier.models import Dossie<PERSON>, Account, DossierFile
from semantic_document.models import SemanticDocument

# Ideally models that describe attributed unique to BEKB


class Partner(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["account", "parkey"], name="unique_account_parkey"
            ),
        ]

    account = models.ForeignKey(Account, on_delete=CASCADE)

    # Parkey alone must not be unique as in 2 different accounts the same parkey could be used.
    # This happens if a client has e.g. a mortgage dossier and a fipla dossier
    # But Parkey is unique per account as defined in Meta -> UniqueConstraint
    parkey = models.Char<PERSON><PERSON>(max_length=20)

    name = models.CharField(max_length=255)
    firstname = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    pers = models.BooleanField(blank=True, null=True)

    def __str__(self):
        return f"{self.parkey}, {self.name}, {self.firstname}"


class Attribute(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["account", "entity", "key"], name="unique_account_key_entity"
            ),
        ]

    class Entity(Enum):
        BusinessStatus = "BusinessStatus"
        BusinessType = "BusinessType"
        CollateralType = "CollateralType"
        CollateralStatus = "CollateralStatus"
        PropertyType = "PropertyType"
        PropertyCollateralType = "PropertyCollateralType"
        RealEstatePropertyStatus = "RealEstatePropertyStatus"

    account = models.ForeignKey(Account, on_delete=CASCADE)
    entity = models.CharField(
        max_length=30, choices=[(tag.value, tag.value) for tag in Entity]
    )
    key = models.CharField(max_length=40)
    name_de = models.CharField(max_length=255)
    name_fr = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.key}, {self.name_de}, {self.name_fr}"


class RealEstateProperty(Timestampable):
    account = models.ForeignKey(Account, on_delete=CASCADE)
    property_partner = models.ForeignKey(Partner, on_delete=CASCADE)
    property_number = models.CharField(max_length=255)
    property_type = models.ForeignKey(
        Attribute,
        on_delete=CASCADE,
        limit_choices_to=dict(entity=Attribute.Entity.PropertyType.value),
        related_name="property_type_real_estate_properties",
    )

    address_street = models.CharField(max_length=255, blank=True, null=True)
    address_street_nr = models.CharField(max_length=20, blank=True, null=True)
    address_zip = models.CharField(max_length=30)
    address_city = models.CharField(max_length=255)

    land_register_municipality = models.CharField(max_length=255, blank=True, null=True)
    land_register_id = models.CharField(max_length=100, blank=True, null=True)

    status = models.ForeignKey(
        Attribute,
        on_delete=CASCADE,
        limit_choices_to=dict(entity=Attribute.Entity.RealEstatePropertyStatus.value),
        related_name="status_real_estate_properties",
        blank=True,
        null=True,
    )

    def __str__(self):
        street_info = []
        if self.address_street is not None:
            street_info.append(self.address_street)

        if self.address_street_nr is not None:
            street_info.append(self.address_street_nr)

        if len(street_info) > 0:
            street_info = "/" + " ".join(street_info)
        else:
            street_info = ""

        return f"{self.account}/{self.property_number}/{self.address_zip} {self.address_city}{street_info}"


class BusinessCase(Timestampable):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["account", "business_partner", "business_number"],
                name="unique_account_business_partner_business_number",
            ),
        ]
        ordering = ["business_partner", "business_number"]

    account = models.ForeignKey(Account, on_delete=CASCADE)
    business_partner = models.ForeignKey(Partner, on_delete=CASCADE)
    business_number = models.CharField(max_length=20)
    business_type = models.ForeignKey(
        Attribute,
        on_delete=CASCADE,
        limit_choices_to=dict(entity=Attribute.Entity.BusinessType.value),
        related_name="business_type_businesscases",
    )

    business_status = models.ForeignKey(
        Attribute,
        on_delete=CASCADE,
        limit_choices_to=dict(entity=Attribute.Entity.BusinessStatus.value),
        related_name="business_status_businesscases",
    )
    mutation_date = models.DateField()
    mutation_user = models.CharField(max_length=40)

    def __str__(self):
        return f"{self.account.key}/{self.business_partner_id}/{self.business_number}"


class Collateral(Timestampable):
    # Collaterals are unique to BEKB
    # Once a document is finished and goes to the archive, we can attach a collateral to it
    # The collateral is an asset that is used to secure the loan (e.g. a house)
    # There are rules which collateral can be used for which loan
    account = models.ForeignKey(Account, on_delete=CASCADE)
    business_partner = models.ForeignKey(
        Partner, on_delete=CASCADE, related_name="business_partner_collaterals"
    )
    businesscase = models.ForeignKey(BusinessCase, on_delete=CASCADE)

    collateral_number = models.CharField(max_length=20)
    collateral_partner = models.ForeignKey(
        Partner, on_delete=CASCADE, related_name="collateral_partner_collaterals"
    )

    document_parkey = models.CharField(max_length=20, blank=True, null=True)

    collateral_type = models.ForeignKey(
        Attribute,
        on_delete=CASCADE,
        limit_choices_to=dict(entity=Attribute.Entity.CollateralType.value),
        related_name="collateral_type_collaterals",
    )

    description = models.CharField(max_length=255, blank=True, null=True)

    collateral_status = models.ForeignKey(
        Attribute,
        on_delete=CASCADE,
        limit_choices_to=dict(entity=Attribute.Entity.CollateralStatus.value),
        related_name="collateral_status_collaterals",
    )

    policy_number = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return f"{self.account.key}/{self.collateral_number}"


class CollateralRealEstateProperty(Timestampable):
    account = models.ForeignKey(Account, on_delete=CASCADE)
    collateral = models.ForeignKey(Collateral, on_delete=CASCADE)
    realestate_property = models.ForeignKey(RealEstateProperty, on_delete=CASCADE)

    # Moved from PropertyRealEstate
    property_collateral_type = models.ForeignKey(
        Attribute,
        on_delete=CASCADE,
        limit_choices_to=dict(entity=Attribute.Entity.PropertyCollateralType.value),
        related_name="collateral_type_real_estate_properties",
    )


class BEKBDossierProperties(Timestampable):
    account = models.ForeignKey(Account, on_delete=CASCADE)
    dossier = models.OneToOneField(Dossier, on_delete=CASCADE)
    business_partner = models.ForeignKey(
        Partner, on_delete=CASCADE, related_name="business"
    )

    # "Oberster Partner" aka IUG-Partner (the root node in the partner/parkey hierarchy where the business_partner
    # is one of the nodes (can also be the same as partner_partner).
    # This field can technically be provided on create_dossier and it can be updated on show_dossier. But according
    # to Marc Schmied, 230803, it is not even available in DBP which creates the two respective links. So the only
    # way it is updated is the update_business_partner api call (which updates both business_partner and
    # partner_partner).
    partner_partner = models.ForeignKey(
        Partner, on_delete=CASCADE, related_name="partner", blank=True, null=True
    )

    business_case = models.ForeignKey(
        BusinessCase, blank=True, null=True, on_delete=SET_NULL
    )

    # Boolean flag. If this is set to true then this dossier is only accessible to users with the pers privilege
    pers = models.BooleanField(default=False, blank=True, null=True)


class CollateralAssignment(Timestampable):
    account = models.ForeignKey(Account, on_delete=CASCADE)
    semantic_document = models.OneToOneField(SemanticDocument, on_delete=CASCADE)
    collateral = models.ForeignKey(Collateral, on_delete=CASCADE)
    # CollateralRealEstateProperty not needed here because we only work on 'Hauptdeckung'
    # and because a property cannot be linked two times to a collateral
    property = models.ForeignKey(
        RealEstateProperty, blank=True, null=True, on_delete=CASCADE
    )


class BEKBDossierExport(Timestampable):
    account = models.ForeignKey(Account, on_delete=CASCADE)
    dossier = models.OneToOneField(
        Dossier, on_delete=CASCADE, related_name="bekbexport"
    )
    file = models.ForeignKey(DossierFile, on_delete=CASCADE, blank=True, null=True)


class ExportFeedback(Timestampable):
    account = models.ForeignKey(Account, on_delete=CASCADE)
    export = models.ForeignKey(BEKBDossierExport, on_delete=CASCADE)

    class Status(models.IntegerChoices):
        ERROR = 0
        SUCCESS = 1

    status = models.IntegerField(choices=Status.choices)

    message = models.TextField(max_length=500, blank=True, null=True)
