{"openapi": "3.1.0", "info": {"title": "Hypodossier - BEKB API", "version": "0.7.0", "description": ""}, "paths": {"/partner/bekb/api/0.7/{dossier_uuid}/token": {"get": {"operationId": "bekb_api_get_dossier_grant_token", "summary": "Get Dossier <PERSON>", "parameters": [{"in": "path", "name": "dossier_uuid", "schema": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "string"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/dossier": {"get": {"operationId": "bekb_api_show_dossier", "summary": "Show Dossier", "parameters": [{"in": "query", "name": "encoded_jwt", "schema": {"title": "Encoded Jwt", "type": "string"}, "required": true}], "responses": {"303": {"description": "See Other", "content": {"application/json": {"schema": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Response", "type": "string"}}}}}, "description": "Shows all the dossiers associated with the corresponding businesscase_parkey\nand creates a redirect to the bekb_dossier list view.\n\nfor the encoding of the encoded_jwt see @create_dossier\n\nArgs:\nrequest (HttpRequest): The HTTP request from the client.\nencoded_jwt (str): The encoded JWT.\n\nReturns:\n    HttpResponse: The HTTP response."}}, "/partner/bekb/api/0.7/list_dossiers/{account_name}/{business_parkey}": {"get": {"operationId": "bekb_api_list_dossiers", "summary": "List Dossiers", "parameters": [{"in": "path", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}, {"in": "path", "name": "business_parkey", "schema": {"maxLength": 20, "title": "Business Parkey", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DossierShow"}, "title": "Response", "type": "array"}}}}}, "description": "Lists all dossiers associated with the specified business partner.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/extra_types": {"get": {"operationId": "bekb_api_extra_types", "summary": "Extra Types", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "stub so the types are in the openapi scshema", "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/DossierCreateJWT"}, {"$ref": "#/components/schemas/DossierShowJWT"}], "title": "Extra Types"}}}, "required": true}}}, "/partner/bekb/api/0.7/dossier/create": {"get": {"operationId": "bekb_api_create_dossier", "summary": "Create Dossier", "parameters": [{"in": "query", "name": "encoded_jwt", "schema": {"title": "Encoded Jwt", "type": "string"}, "required": true}], "responses": {"302": {"description": "Found", "content": {"application/json": {"schema": {"title": "Response", "type": "string"}}}}}, "description": "Creates a new Dossier based on the provided parameters\n\nThis endpoint is called via URL integration in the DBP (Digitales Berater Portal - Create a new Dossier).\nAfter the creation of the dossier, the user is redirected to the detail view of the newly created dossier.\n\nencoded_jwt is a jwt which contains the following parameters:\n* ***expiration*** defines the expiration time of the token datetime.utcnow().replace(tzinfo=timezone.utc) + timedelta(minutes=60)\n* ***businesscase_parkey*** corresponds to the business case to which the dossier is associated.\n* ***pers*** sets the dossier property to pers, which indicates that only users with pers attributes are allow to open the dossier\n* ***fico*** sets the username (email address) of the responsible financial coach of the dossier\n\n* ***current_user*** is the username of the person trying to create the dossier.\n    if the pers attribute is set, it is assumed that the current_user has the permission to view dossiers with the pers attribut of the dossier set to true\n\n```python\nimport jwt\nshared_secret = \"a secret shared between bekb and hypodossier\"\nparameters = {'exp': expiration, 'businesscase_parkey': '1234566', 'pers': True, \"team\": \"team1\", \"fico\": \"<EMAIL>\", \"current_user\": \"<EMAIL>\" }\nencoded_jwt = jwt.encode(parameters, shared_secret, algorithm='HS256')\n```"}}, "/partner/bekb/api/0.7/dossier/properties": {"put": {"operationId": "bekb_api_update_dossier_properties", "summary": "Update Dossier Properties", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Updates the properties of a Dossier.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account where the Dossier belongs.\n    updates (List[DossierProperties]): The list of Dossier properties to update.\n\nReturns:\n    int: HTTP status code indicating the result of the operation.\n    str: Empty string.", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DossierProperties"}, "title": "Updates", "type": "array"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/users": {"get": {"operationId": "bekb_api_list_hypodossier_users", "summary": "List Hypodossier Users", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/User"}, "title": "Response", "type": "array"}}}}}, "description": "Lists all HypoDossier users for a specific account.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account.\n\nReturns:\n    List[schemas.User]: The list of HypoDossier users for the specified account.", "security": [{"BEKBJWTAuth": []}]}, "put": {"operationId": "bekb_api_update_user_attributes", "summary": "Update User Attributes", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"title": "Response", "type": "string"}}}}}, "description": "Updates the attributes of a user.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account where the user belongs.\n    updates (List[schemas.UserAttributes]): The list of user attributes to update.\n\nReturns:\n    int: HTTP status code indicating the result of the operation.\n    str: Empty string.", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserAttributes"}, "title": "Updates", "type": "array"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/business-parkeys": {"get": {"operationId": "bekb_api_list_business_parkeys", "summary": "List Business Parkeys", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"maxLength": 20, "type": "string"}, "title": "Response", "type": "array"}}}}}, "description": "Returns a list of all business parkeys (Geschäftsfall Parkey) actively used at HypoDossier.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account.\n\nReturns:\n    List[Parkey]: The list of business parkeys.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/business-partners": {"put": {"operationId": "bekb_api_update_business_partners", "summary": "Update Business Partners", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Updates the information of business partners.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account where the business partners belong.\n    updates (schemas.BusinessPartnerUpdates): The updates to be applied to the business partners.\n\nReturns:\n    int: HTTP status code indicating the result of the operation.\n    str: Empty string.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessPartnerUpdates"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/attributes": {"put": {"operationId": "bekb_api_create_or_update_attribute", "summary": "Create Or Update Attribute", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Creates or updates an attribute in the database.\nThe key of the attribute consists of the entity and the key\nof the attribute.\n\nTODO: löscht BEKB Attribute oder werden die einfach nicht mehr referenziert?\nen: does BEKB delete attributes or are they simply no longer referenced?\n\nArgs:\n    request: HTTP request object.\n    account_name: Account name.\n    attribute_updates: Updates to the attributes.\n\nReturns:\n    HTTP status code: 204 for successful update, 401 for unauthorized access.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttributeUpdates"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/businesscases": {"put": {"operationId": "bekb_api_update_list_of_businesscase", "summary": "Update List Of Businesscase", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Updates a list of business cases in the database.\n\nArgs:\n    request: HTTP request object.\n    account_name: Account name.\n    updates: Updates to the business cases.\n\nReturns:\n    HTTP status code: 204 for successful update, 401 for unauthorized access.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCaseUpdates"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/realestateproperties": {"put": {"operationId": "bekb_api_update_realestate_properties", "summary": "Update Realestate Properties", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Updates real estate properties in the database. The 'property_number' is assumed to be the\nunique key for each property.\n\nArgs:\n    request: HTTP request object.\n    account_name: Account name.\n    real_estate_properties: Real estate properties to be updated.\n\nReturns:\n    HTTP status code: 204 for successful update, 401 for unauthorized access.\n\nTODO:\n    Haben die Liegenschaften einen Status (falls die gelöscht/deaktiviert werden?) oder benötigen\n    wir ein Delete API Hypodossier geht davon aus, dass die property_number ein unique key ist\n    für das property\n\n    En: Do the properties have a status (in case they are deleted/deactivated?) or do we need a\n    Delete API Hypodossier assumes that the property_number is a unique key for the property", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RealEstateProperty"}, "title": "Real Estate Properties", "type": "array"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/businesscases/collateral": {"put": {"operationId": "bekb_api_update_collateral_for_business_key", "summary": "Update Collateral For Business Key", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Update the collateral information for a business case (Geschäftsfall Parkey).\n\nDesigned to be used in batches\n\nAdditional descriptive information might be required and has to be decided by bekb\n\nArgs:\n    request: The incoming request.\n    account_name: The name of the account to update.\n    collaterals: The list of collateral data to update.\n\nReturns:\n    HTTP status code 204 and an empty string.", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Collateral"}, "title": "Collaterals", "type": "array"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/export/dossier-ready-for-export": {"get": {"operationId": "bekb_api_show_dossier_ready_for_export", "summary": "Show Dossier Ready For Export", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DossierExport"}, "title": "Response", "type": "array"}}}}}, "description": "Returns a list of dossier exports that are ready for export.\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account whose exports are to be retrieved.\n\nReturns:\n    List[schemas.DossierExport]: A list of exports for the specified account.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/export/file": {"get": {"operationId": "bekb_api_download_file", "summary": "Download File", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}, {"in": "query", "name": "export_uuid", "schema": {"format": "uuid", "title": "Export U<PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"format": "binary", "title": "Response", "type": "string"}}}}}, "description": "Downloads a Dossier associated with a given account and export UUID.\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    export_uuid: The unique identifier of the export.\n\nReturns:\n    StreamingHttpResponse: The file to be downloaded.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/export/feedback": {"post": {"operationId": "bekb_api_add_dossier_export_feedback", "summary": "Add Dossier Export <PERSON>", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Adds feedback for a given export.\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    feedbacks: A list of DossierExportFeedback schema objects.\n\nReturns:\n    int, str: 204 status code and an empty string indicating successful operation.\n    HTTP 204 if update has been done successfully. HTTP 401 if not allowed.", "requestBody": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DossierExportFeedback"}, "title": "Feedbacks", "type": "array"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/test/dossier/create/ready-for-export": {"post": {"operationId": "bekb_api_add_some_sample_dossier_ready_for_export", "summary": "Add Some Sample Dossier Ready For Export", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}, {"in": "query", "name": "count", "schema": {"default": 1, "title": "Count", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK"}}, "description": "Create up to count (max 10, default 1) fake dossier in state ready for export\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    count: The number of fake dossiers to be created (default is 1).\n\nReturns:\n    int, Message: In case of a production account, returns a 403 status code and an error message.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/test/dossier/process_ready_for_export": {"post": {"operationId": "bekb_api_process_ready_for_export", "summary": "Process Ready For Export", "parameters": [{"in": "query", "name": "account_name", "schema": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "required": true}], "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Result_UUID_"}, "title": "Response", "type": "array"}}}}}, "description": "Checks whether a dossier is ready for export\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    processing_request: A ProcessReadyForExportRequest schema object containing dossier UUIDs\n                         and 'not_updated_since' parameter.\n\nReturns:\n    int, function: In case of a production account, returns a 403 status code and an error message.\n    Otherwise, processes the dossiers and returns a 201 status code.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessReadyForExportRequest"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}}, "components": {"schemas": {"Message": {"properties": {"detail": {"title": "Detail", "type": "string"}}, "required": ["detail"], "title": "Message", "type": "object"}, "AccountName": {"enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "title": "Account<PERSON><PERSON>", "type": "string"}, "BusinessPartner": {"properties": {"parkey": {"maxLength": 20, "title": "<PERSON><PERSON>", "type": "string"}, "name": {"maxLength": 255, "title": "Name", "type": "string"}, "firstname": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Firstname"}, "pers": {"title": "Pers", "type": "boolean"}}, "required": ["parkey", "name", "pers"], "title": "BusinessPartner", "type": "object"}, "DossierShow": {"properties": {"dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "business_partner": {"$ref": "#/components/schemas/BusinessPartner"}, "dossier_name": {"maxLength": 255, "title": "Dossier Name", "type": "string"}, "language": {"$ref": "#/components/schemas/Langugage"}, "access_mode": {"title": "Access Mode", "type": "string"}, "work_status_key": {"title": "Work Status Key", "type": "string"}, "work_status_name_de": {"title": "Work Status Name De", "type": "string"}, "work_status_name_fr": {"title": "Work Status Name Fr", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "expires_at": {"format": "date-time", "title": "Expires At", "type": "string"}}, "required": ["dossier_uuid", "business_partner", "dossier_name", "language", "access_mode", "work_status_key", "work_status_name_de", "work_status_name_fr", "created_at", "updated_at", "expires_at"], "title": "DossierShow", "type": "object"}, "Langugage": {"enum": ["de", "fr"], "title": "Langugage", "type": "string"}, "DossierCreateJWT": {"properties": {"exp": {"description": "Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular.", "title": "Exp", "type": "integer"}, "account_name": {"$ref": "#/components/schemas/AccountName"}, "business_partner": {"$ref": "#/components/schemas/BusinessPartner"}, "partner_partner": {"anyOf": [{"$ref": "#/components/schemas/Partner"}, {"type": "null"}]}, "dossier_name": {"maxLength": 255, "title": "Dossier Name", "type": "string"}, "language": {"$ref": "#/components/schemas/Langugage"}, "fico": {"$ref": "#/components/schemas/User", "description": "The responsible FICO for the dossier"}, "current_user": {"$ref": "#/components/schemas/User", "description": "The current user (from the DBP). Will be the owner of the dossier during creation."}}, "required": ["exp", "account_name", "business_partner", "dossier_name", "language", "fico", "current_user"], "title": "DossierCreateJWT", "type": "object"}, "DossierShowJWT": {"properties": {"exp": {"description": "Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular.", "title": "Exp", "type": "integer"}, "account_name": {"$ref": "#/components/schemas/AccountName"}, "business_partner": {"$ref": "#/components/schemas/BusinessPartner"}, "partner_partner": {"anyOf": [{"$ref": "#/components/schemas/Partner"}, {"type": "null"}]}, "language": {"$ref": "#/components/schemas/Langugage"}, "fico": {"$ref": "#/components/schemas/User", "description": "The responsible FICO for the dossier"}, "current_user": {"$ref": "#/components/schemas/User", "description": "The current user (from the DBP). Will be the owner of the dossier."}}, "required": ["exp", "account_name", "business_partner", "language", "fico", "current_user"], "title": "DossierShowJWT", "type": "object"}, "Partner": {"properties": {"parkey": {"maxLength": 20, "title": "<PERSON><PERSON>", "type": "string"}, "name": {"maxLength": 255, "title": "Name", "type": "string"}, "firstname": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Firstname"}}, "required": ["parkey", "name"], "title": "Partner", "type": "object"}, "User": {"properties": {"username": {"description": "should be the email address as username", "maxLength": 255, "title": "Username", "type": "string"}, "firstname": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Firstname"}, "name": {"maxLength": 255, "title": "Name", "type": "string"}, "pers": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Pers"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Active"}}, "required": ["username", "name"], "title": "User", "type": "object"}, "DossierProperties": {"properties": {"business_parkey": {"maxLength": 20, "title": "Business Parkey", "type": "string"}, "fico": {"anyOf": [{"$ref": "#/components/schemas/User"}, {"type": "null"}], "description": "the responsible financial coach"}}, "required": ["business_parkey"], "title": "DossierProperties", "type": "object"}, "UserAttributes": {"properties": {"username": {"description": "The username (should be an email address) of the user to update", "maxLength": 255, "title": "Username", "type": "string"}, "firstname": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Firstname"}, "name": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Name"}, "pers": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "set to true if the user has access to pers dossiers", "title": "Pers"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Is the user active (True) or inactive (False)", "title": "Active"}}, "required": ["username"], "title": "UserAttributes", "type": "object"}, "BusinessPartnerUpdate": {"properties": {"business_parkey": {"maxLength": 20, "title": "Business Parkey", "type": "string"}, "business_partner": {"anyOf": [{"$ref": "#/components/schemas/BusinessPartner"}, {"type": "null"}]}, "partner_partner": {"anyOf": [{"$ref": "#/components/schemas/Partner"}, {"type": "null"}]}}, "required": ["business_parkey"], "title": "BusinessPartnerUpdate", "type": "object"}, "BusinessPartnerUpdates": {"default": [], "items": {"$ref": "#/components/schemas/BusinessPartnerUpdate"}, "title": "BusinessPartnerUpdates", "type": "array"}, "Attribute": {"properties": {"entity": {"$ref": "#/components/schemas/Entity"}, "key": {"maxLength": 40, "title": "Key", "type": "string"}, "name_de": {"maxLength": 255, "title": "Name De", "type": "string"}, "name_fr": {"maxLength": 255, "title": "Name Fr", "type": "string"}}, "required": ["entity", "key", "name_de", "name_fr"], "title": "Attribute", "type": "object"}, "AttributeUpdates": {"items": {"$ref": "#/components/schemas/Attribute"}, "title": "AttributeUpdates", "type": "array"}, "Entity": {"enum": ["BusinessStatus", "BusinessType", "CollateralType", "CollateralStatus", "PropertyType", "PropertyCollateralType", "RealEstatePropertyStatus"], "title": "Entity", "type": "string"}, "BusinessCase": {"description": "Geschäftsfall", "properties": {"business_parkey": {"maxLength": 20, "title": "Business Parkey", "type": "string"}, "business_number": {"maxLength": 20, "title": "Business Number", "type": "string"}, "business_type": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Immobilienfinanzierung, ...)", "maxLength": 40, "title": "Business Type", "type": "string"}, "business_status": {"description": "Geschäftsstatus (pendent, definitiv, ab<PERSON><PERSON><PERSON>, aufgehoben, auszahlungsgeprüft, nicht zustande gekommen", "maxLength": 40, "title": "Business Status", "type": "string"}, "mutation_date": {"format": "date", "title": "Mutation Date", "type": "string"}, "mutation_user": {"maxLength": 50, "title": "Mutation User", "type": "string"}}, "required": ["business_parkey", "business_number", "business_type", "business_status", "mutation_date", "mutation_user"], "title": "BusinessCase", "type": "object"}, "BusinessCaseUpdates": {"items": {"$ref": "#/components/schemas/BusinessCase"}, "title": "BusinessCaseUpdates", "type": "array"}, "RealEstateProperty": {"properties": {"property_partner": {"$ref": "#/components/schemas/Partner", "description": "Partner der Liegenschaft"}, "property_number": {"description": "Liegenschaftsnummer", "maxLength": 20, "title": "Property Number", "type": "string"}, "property_type": {"description": "Liegenschaftsobjektart (e.g. Eigentumswohnung, Garage/Abstellplatz)", "maxLength": 40, "title": "Property Type", "type": "string"}, "address_street": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Address Street"}, "address_street_nr": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "title": "Address Street Nr"}, "address_zip": {"maxLength": 30, "title": "Address Zip", "type": "string"}, "address_city": {"maxLength": 255, "title": "Address City", "type": "string"}, "land_register_municipality": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "description": "Grundbuch Gemeinde", "title": "Land Register Municipality"}, "land_register_id": {"anyOf": [{"maxLength": 100, "type": "string"}, {"type": "null"}], "description": "Grundbuch Blatt-Nr.", "title": "Land Register Id"}, "status": {"description": "RealEstatePropertyStatus Attribute", "maxLength": 40, "title": "Status", "type": "string"}}, "required": ["property_partner", "property_number", "property_type", "address_zip", "address_city", "status"], "title": "RealEstateProperty", "type": "object"}, "Collateral": {"description": "additional fields? sollen values mitgeliefert werden für GUI Mapping? partner parkey kommt separat", "properties": {"business_parkey": {"description": "Geschäftsfall-Parkey of the collateral", "maxLength": 20, "title": "Business Parkey", "type": "string"}, "business_number": {"description": "Geschäfts-Nr.", "maxLength": 20, "title": "Business Number", "type": "string"}, "collateral_number": {"description": "Deckungs-Nr.", "maxLength": 20, "title": "Collateral Number", "type": "string"}, "collateral_partner": {"$ref": "#/components/schemas/Partner", "description": "Partner des Deckungsgebers"}, "collateral_type": {"description": "e.g. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> intern", "maxLength": 40, "title": "Collateral Type", "type": "string"}, "description": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "description": "Bemerkung", "title": "Description"}, "collateral_status": {"description": "Deckungsstatus Todo: ta<PERSON><PERSON><PERSON><PERSON> gebra<PERSON>t", "maxLength": 40, "title": "Collateral Status", "type": "string"}, "policy_number": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "description": "Policennummer (im Fall der Deckungsart 'Personenversicherungen')", "title": "Policy Number"}, "real_estate_properties": {"anyOf": [{"items": {"$ref": "#/components/schemas/CollateralRealEstateProperty"}, "type": "array"}, {"type": "null"}], "title": "Real Estate Properties"}}, "required": ["business_parkey", "business_number", "collateral_number", "collateral_partner", "collateral_type", "description", "collateral_status", "policy_number"], "title": "Collateral", "type": "object"}, "CollateralRealEstateProperty": {"properties": {"property_number": {"description": "Liegenschaftsnummer", "maxLength": 20, "title": "Property Number", "type": "string"}, "property_collateral_type": {"description": "Liegenschaftsdeckungstyp: Hauptdeckung | Nebendeckung | Verwaltungsgeschäft", "maxLength": 40, "title": "Property Collateral Type", "type": "string"}}, "required": ["property_number", "property_collateral_type"], "title": "CollateralRealEstateProperty", "type": "object"}, "DossierExport": {"properties": {"dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "export_uuid": {"format": "uuid", "title": "Export U<PERSON>", "type": "string"}}, "required": ["dossier_uuid", "export_uuid"], "title": "DossierExport", "type": "object"}, "DossierExportFeedback": {"properties": {"export_uuid": {"format": "uuid", "title": "Export U<PERSON>", "type": "string"}, "status": {"$ref": "#/components/schemas/ExportStatus"}, "message": {"anyOf": [{"maxLength": 500, "type": "string"}, {"type": "null"}], "title": "Message"}}, "required": ["export_uuid", "status"], "title": "DossierExportFeedback", "type": "object"}, "ExportStatus": {"enum": ["success", "error"], "title": "ExportStatus", "type": "string"}, "Error": {"properties": {"code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}}, "required": ["code", "message"], "title": "Error", "type": "object"}, "Result_UUID_": {"properties": {"data": {"anyOf": [{"format": "uuid", "type": "string"}, {"type": "null"}], "title": "Data"}, "error": {"anyOf": [{"$ref": "#/components/schemas/Error"}, {"type": "null"}]}}, "title": "Result[UUID]", "type": "object"}, "ProcessReadyForExportRequest": {"properties": {"dossier_uuids": {"anyOf": [{"items": {"format": "uuid", "type": "string"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "not_updated_since": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Not Updated Since"}}, "title": "ProcessReadyForExportRequest", "type": "object"}}, "securitySchemes": {"BEKBJWTAuth": {"type": "http", "scheme": "bearer"}}}, "servers": []}