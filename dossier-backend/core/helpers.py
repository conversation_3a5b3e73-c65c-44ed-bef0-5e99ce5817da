import json
import re
import asyncio
from typing import List
import platform

import structlog
from django.contrib.auth import get_user_model
from django.db.models import Field
import requests
from zipfile import ZipFile

import aio_pika
from aio_pika.patterns.rpc import CallbackType, RPC
from django.contrib.admin.utils import NestedObjects
from django.db import DEFAULT_DB_ALIAS
from django.db.models import Model
from django.db.models.utils import resolve_callables

from core.schema import ModelNestedObjectsStatistics, ModelStats
from dossier.exceptions import HttpError
from projectconfig.settings import RABBIT_URL

logger = structlog.get_logger()


async def fabric_start_server(
    rpc_callback: CallbackType,
    rpc_method_name: str,
    loop: asyncio.AbstractEventLoop = None,
    client_properties=None,
    auto_delete: bool = False,
    name: str = "",
):
    if client_properties is None:
        client_properties = {}

    connection = await aio_pika.connect_robust(
        f"{RABBIT_URL}?name=fabric start server {name} {platform.node()}",
        client_properties=client_properties,
        loop=loop,
    )

    channel = await connection.channel()
    rpc = await RPC.create(channel)

    await rpc.register(rpc_method_name, rpc_callback, auto_delete=auto_delete)

    return connection


def remove_invalid_chars(name: str, symbol_to_replace=""):
    return re.sub(
        r"\s\s+", " ", re.sub(r"""[&\/\\#+$%`:*?<>{}!|="]""", symbol_to_replace, name)
    ).strip()


def validate_size_of_zip_archive(filepath: str):
    zip_file = ZipFile(filepath)
    return len(zip_file.namelist()) > 0


def upload_data_to_minio(put_upload_url: str, fp):
    response = requests.put(put_upload_url, data=fp.read())
    response.raise_for_status()


def open_and_validate_and_upload_data_to_minio(
    filepath: str, put_url: str, text_error="FILES_NOT_FOUND"
):
    with open(filepath, "rb") as fp:
        if not validate_size_of_zip_archive(filepath):
            raise HttpError(400, text_error)
        upload_data_to_minio(put_url, fp)


def get_model_stats(db_models: List[Model]) -> ModelStats:
    collector = NestedObjects(using=DEFAULT_DB_ALIAS)
    collector.collect(db_models)

    model_stats = []
    for model, instances in collector.model_objs.items():
        if isinstance(model, type):
            model_data = ModelNestedObjectsStatistics(
                model_type=str(model), count=len(instances)
            )
            model_stats.append(model_data)
    return ModelStats(__root__=model_stats)


def is_iso8601_date(value):
    # Regular expression to match ISO 8601 date format
    iso8601_pattern = re.compile(r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z")
    return isinstance(value, str) and iso8601_pattern.fullmatch(value)


def compare_datastructures(
    data_1,
    data_2,
    list_ignored_keys: List[str] = None,
    list_ignored_values: List[str] = None,
):
    # Compare data loaded from json files
    assert type(data_1) == type(
        data_2
    ), f"Type mismatch: {type(data_1)} != {type(data_2)}"

    if isinstance(data_1, dict):
        data_1_keys = data_1.keys()
        data_2_keys = data_2.keys()
        if list_ignored_keys:
            data_1_keys = [
                item for item in data_1.keys() if item not in list_ignored_keys
            ]
            data_1_keys_ignored = [
                item for item in data_1.keys() if item in list_ignored_keys
            ]
            data_2_keys = [
                item for item in data_2.keys() if item not in list_ignored_keys
            ]
            data_2_keys_ignored = [
                item for item in data_2.keys() if item in list_ignored_keys
            ]
            if data_1_keys_ignored or data_2_keys_ignored:
                logger.info(
                    "skip comparison of some keys",
                    keys_left_ignored=data_1_keys_ignored,
                    keys_right_ignored=data_2_keys_ignored,
                )
        assert (
            data_1_keys == data_2_keys
        ), f"Key mismatch: {data_1_keys} != {data_2_keys}"
        for key in data_1_keys:
            if list_ignored_values and key in list_ignored_values:
                # do nothing, this should not be compared
                pass
                same = data_1[key] == data_2[key]
                if not same:
                    logger.info(
                        "skip comparison of different values for key",
                        key=key,
                        value_1=data_1[key],
                        value_2=data_2[key],
                    )
            else:
                compare_datastructures(
                    data_1[key], data_2[key], list_ignored_keys, list_ignored_values
                )

    elif isinstance(data_1, list):
        assert len(data_1) == len(
            data_2
        ), f"List length mismatch: {len(data_1)} != {len(data_2)}"

        if all(isinstance(item, (dict, list)) for item in data_1):
            data_1_sorted = sorted(data_1, key=lambda x: json.dumps(x, sort_keys=True))
            data_2_sorted = sorted(data_2, key=lambda x: json.dumps(x, sort_keys=True))
            for item1, item2 in zip(data_1_sorted, data_2_sorted):
                compare_datastructures(
                    item1, item2, list_ignored_keys, list_ignored_values
                )
        else:
            for item1, item2 in zip(sorted(data_1), sorted(data_2)):
                compare_datastructures(
                    item1, item2, list_ignored_keys, list_ignored_values
                )

    elif not is_iso8601_date(data_1):
        assert data_1 == data_2, f"Value mismatch: {data_1} != {data_2}"


def optionally_update_user_model(
    user_obj: get_user_model(),
    defaults=None,
    using=None,
):
    """
    Conditionally updates the user model instance with the given defaults,
    only updating fields that have changed.
    """
    defaults = defaults or {}
    model = get_user_model()
    changed_fields = set()

    for k, v in resolve_callables(defaults):
        if getattr(user_obj, k) != v:
            setattr(user_obj, k, v)
            changed_fields.add(k)

    if not changed_fields:
        return user_obj, False

    concrete_field_names = model._meta._non_pk_concrete_field_names
    # update_fields does not support non-concrete fields.
    if concrete_field_names.issuperset(changed_fields):
        # Add fields which are set on pre_save(), e.g. auto_now fields.
        # This is to maintain backward compatibility as these fields
        # are not updated unless explicitly specified in the
        # update_fields list.
        for field in model._meta.local_concrete_fields:
            if not (field.primary_key or field.__class__.pre_save is Field.pre_save):
                if field.name in changed_fields:
                    if field.name != field.attname:
                        changed_fields.add(field.attname)

        if changed_fields:
            user_obj.save(using=using, update_fields=changed_fields)
            return user_obj, True
    return user_obj, False
