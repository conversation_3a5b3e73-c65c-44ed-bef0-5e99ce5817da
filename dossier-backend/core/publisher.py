import pika
from django.conf import settings


def publish(
    message: bytes,
    routing_key: str,
    exchange: str = "",
    properties: pika.spec.BasicProperties = None,
):
    """improve and reuse connections"""
    connection = pika.BlockingConnection(pika.URLParameters(settings.RABBIT_URL))
    channel = connection.channel()
    channel.basic_publish(
        exchange=exchange, routing_key=routing_key, body=message, properties=properties
    )
    channel.close()
    connection.close()
