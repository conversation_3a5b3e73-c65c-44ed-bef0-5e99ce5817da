from django.conf import settings
from django.test.client import Client
import jwt
from jwcrypto import jwk


class AuthenticatedClient(Client):
    def __init__(
        self, token, enforce_csrf_checks=False, raise_request_exception=True, **defaults
    ):
        self.token = token
        super().__init__(enforce_csrf_checks, raise_request_exception, **defaults)

    def request(self, **request):
        request.update({"HTTP_AUTHORIZATION": f"Bearer {self.token}"})
        return super().request(**request)


def create_token(
    given_name: str,
    family_name: str,
    email: str,
    roles: list = (),
    account_key: str | None = None,
    extra_fields: dict[str, any] | None = None,
    signed_with_keycloak_key: bool = False,
) -> str:
    payload = {
        "aud": "account",
        "email_verified": True,
        "name": f"{given_name} {family_name}",
        "preferred_username": email,
        "user_roles": roles,
        "given_name": given_name,
        "family_name": family_name,
        "email": email,
    }

    if account_key is not None:
        payload["account_key"] = account_key

    if extra_fields is not None:
        payload.update(extra_fields)

    if signed_with_keycloak_key:
        private_key_obj = jwk.JWK.from_pem(settings.KEYCLOAK_PRIVATE_KEY.encode())
    else:
        private_key_obj = jwk.JWK.from_json(settings.INSTANCE_SIGNING_KEY)

    token = jwt.encode(
        payload,
        private_key_obj.export_to_pem(private_key=True, password=None).decode(),
        algorithm="RS256",
    )

    return token


def create_token_from_payload(payload: dict) -> str:
    """
    Creates a JWT token from a given payload dictionary.

    Args:
        payload (dict): The payload to encode into the token

    Returns:
        str: The encoded JWT token
    """

    payload.update({"aud": "account", "email_verified": True})

    private_key_obj = jwk.JWK.from_json(settings.INSTANCE_SIGNING_KEY)

    token = jwt.encode(
        payload,
        private_key_obj.export_to_pem(private_key=True, password=None).decode(),
        algorithm="RS256",
    )

    return token
