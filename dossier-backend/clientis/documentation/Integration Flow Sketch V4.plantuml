@startuml Transfer documents to eDossier
autonumber
box CLIENTIS
    actor "Standard User" as clientisuser
    participant "eDossier" as mfiles


end box

box Hypodossier
    participant "Hypodossier API" as hypoapi
    participant "https://clientis.hypodossier.ch" as hypo
end box


title Transfer document to eDossier

== Trigger Transfer ==
alt in hypodossier
    group Access Hypodossier
    end
    clientisuser -> hypo: <PERSON>ton Click "Transfer Document to eDossier"
    activate clientisuser
    activate hypo
    hypo -> hypo: set read only
    hypo --> clientisuser
    deactivate clientisuser
    hypo -> hypo: Creates PDF + Ready for download
    deactivate hypo


== Download Files (every 5min) ==

mfiles -> hypoapi: get documents ready for download **GET** /export/all-semantic-documents-available\
activate mfiles
activate hypoapi
hypoapi --> mfiles: list of documents ready for download, with urls
deactivate hypoapi

loop for each file
    ' Extract dossier_id, semantic_document_uuid, download_url from list item '

    mfiles -> hypoapi: **POST** /export/{dossier_id}/semantic-documents/{semantic_document_uuid}/set-export-in-progress
    activate hypoapi
    hypoapi --> mfiles: 200 OK (Confirms status changed internally)
    deactivate hypoapi

    mfiles -> hypoapi: Download ZIP via\nsemantic_document_url (provided in polling response)
    activate hypoapi
    hypoapi --> mfiles: Pdf + Metadata (ZIP)
    deactivate hypoapi

    mfiles -> mfiles: Process ZIP (Extract PDF, XML), Archive PDF\nusing XML Metadata

    alt Archiving Successful
        mfiles -> hypoapi: **POST** /export/{dossier_id}/semantic-documents/{semantic_document_uuid}/set-done
        activate hypoapi
        hypoapi --> mfiles: 200 OK (Confirms export completion internally)
        deactivate hypoapi
    else Archiving Failed
        mfiles -> hypoapi: **POST** /export/{dossier_id}/semantic-documents/{semantic_document_uuid}/set-error\n(Optional: body with error message)
        activate hypoapi
        hypoapi --> mfiles: 200 OK (Confirms export error internally)
        deactivate hypoapi
    end
deactivate mfiles
@enduml
