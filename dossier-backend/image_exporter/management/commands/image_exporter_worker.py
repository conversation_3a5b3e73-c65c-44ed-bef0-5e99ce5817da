"""
exports IMAGE page objects as a zip file, saves it in the s3 store and return a link valid for 24h
"""

import asyncio
import io
import logging
import structlog
import os
import platform
import sys
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Dict
from uuid import UUID
from zipfile import ZipFile

import djclick as click
import requests
from PIL import Image
from channels.db import database_sync_to_async

from core.helpers import (
    fabric_start_server,
    remove_invalid_chars,
    validate_size_of_zip_archive,
    upload_data_to_minio,
)
from dossier.models import Dossier
from image_exporter.helpers import (
    get_page_objects,
    image_to_byte,
    get_file_extension,
    generate_name_of_page_object,
    crop_image,
)
from image_exporter.photo_album_docx import (
    get_photo_album_docx_template_path,
    add_photo_album_to_zip,
    create_photo_album_from_image_streams,
)
from image_exporter.schemas import (
    ImageExportResponseV1,
    ImageExportResponsePayload,
    ExportPageImageRequestBody,
)
from projectconfig.settings import ASYNC_IMAGE_EXPORTER_WORKER_V1_QUEUE_NAME

logger = structlog.get_logger()

ERROR_IMAGES_NOT_FOUND = "IMAGES_NOT_FOUND"


def filter_page_object_model(simple_page_object, page_object):
    return (
        simple_page_object.page_object_uuid == page_object["uuid"]
        and UUID(simple_page_object.semantic_page_uuid)
        == page_object["semantic_page_uuid"]
        and UUID(simple_page_object.semantic_document_uuid)
        == page_object["semantic_document_uuid"]
    )


def write_page_objects_to_file(
    page_object_filters: ExportPageImageRequestBody.dict,
    page_objects_data,
    filepath: str,
    photo_album_path: Path,
):
    """
    photo_album_path: Path of template. By default, filename = get_photo_album_docx_template_path('photo-album-docx-template-default-v01.docx')
    """
    count_page_objects = 0

    with ZipFile(filepath, "w") as zip_file:
        image_streams = []
        for page_index in range(len(page_objects_data)):
            page_object = page_objects_data[page_index]

            file_extension = get_file_extension(page_object["original_filename"])

            bytes_image = get_image_bytes(page_object, file_extension)

            if page_object_filters["skip_small_images"] and not is_valid_size_of_image(
                bytes_image
            ):
                continue

            file_name = (
                remove_invalid_chars(
                    generate_name_of_page_object(page_object, page_index + 1),
                    symbol_to_replace="_",
                )
                + file_extension
            )

            zip_file.writestr(
                file_name,
                bytes_image.getvalue(),
            )

            image_streams.append(bytes_image)
            count_page_objects += 1

        photo_album_doc = create_photo_album_from_image_streams(
            photo_album_path, image_streams
        )
        if photo_album_doc:
            add_photo_album_to_zip(photo_album_doc, zip_file)

    has_photo_album = photo_album_doc is not None
    return count_page_objects, has_photo_album


def get_image_bytes(page_object, file_extension: str) -> io.BytesIO:
    image = Image.open(io.BytesIO(requests.get(page_object["url"]).content))

    page_object_image = create_page_object_image(
        image, page_object["bbox"], page_object["rotation"]
    )

    return image_to_byte(page_object_image, file_extension)


def create_page_object_image(image, bbox, rotation_angle):
    image = crop_image(image, bbox)

    return rotate_image(image, rotation_angle)


def get_pillow_angle(rotation_angle):
    rotation_data = {
        90: Image.ROTATE_270,
        180: Image.ROTATE_180,
        270: Image.ROTATE_90,
    }

    pillow_angle = rotation_data.get(rotation_angle)

    if not pillow_angle:
        raise Exception("Unknown rotation angle")

    return pillow_angle


def rotate_image(image, rotation_angle):
    if rotation_angle != 0:
        pillow_angle = get_pillow_angle(rotation_angle)
        return image.transpose(pillow_angle)

    return image


def bytes_to_kb(file_bytes):
    return file_bytes / 1000


def is_valid_size_of_image(bytes_image):
    max_weight_of_file_in_kb = 5

    return bytes_to_kb(sys.getsizeof(bytes_image)) > max_weight_of_file_in_kb


def validate_page_object_filters(page_object_filters: ExportPageImageRequestBody.dict):
    keys_to_delete = ["skip_small_images"]

    page_object_filters_to_dict = {
        key: page_object_filters[key]
        for key in page_object_filters
        if key not in keys_to_delete
    }

    return any(page_object_filters_to_dict.values())


def get_sorting_key_for_page_object_data(page_object_data_dict: Dict[str, str]):
    title = page_object_data_dict.get("formatted_title", "zzzzzzzzzzzzzzzzzzzzz")
    page_no = page_object_data_dict.get("semantic_page_number", "999999")
    page_pos = str(page_object_data_dict.get("bbox"))
    return (title, page_no, page_pos)


@database_sync_to_async
def on_message(
    dossier_uuid: str,
    put_upload_url: str,
    name_of_archive: str,
    page_object_filters: ExportPageImageRequestBody.dict,
    add_photo_album_docx: bool,
    *args,
    **kw,
):
    error_text = None
    logger.info(f"image exporter got dossier: {dossier_uuid}")

    if not validate_page_object_filters(page_object_filters):
        logger.error(
            f"A user didn't selected any filters. Dossier: {dossier_uuid}; Filters: {page_object_filters}"
        )
        return ImageExportResponseV1(
            http_status_code=400, message=ERROR_IMAGES_NOT_FOUND
        ).model_dump_json()

    page_objects_data = get_page_objects(UUID(dossier_uuid), page_object_filters)

    # Sort images by doc formatted title of the corresponding page
    page_objects_data = sorted(
        page_objects_data,
        key=lambda x: get_sorting_key_for_page_object_data(x),
        reverse=False,
    )

    dossier = Dossier.objects.get(uuid=dossier_uuid)
    account = dossier.account

    photo_album_path = get_photo_album_docx_template_path(
        account.photo_album_docx_template, add_photo_album_docx
    )

    if len(page_objects_data) == 0:
        logger.error(
            f"No page objects {page_objects_data} found in the dossier {dossier_uuid}"
        )
        error_text = ERROR_IMAGES_NOT_FOUND

    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        filepath = os.path.join(temp_path, name_of_archive)

        count_page_objects, has_photo_album = write_page_objects_to_file(
            page_object_filters, page_objects_data, filepath, photo_album_path
        )

        with open(filepath, "rb") as fp:
            if not validate_size_of_zip_archive(filepath):
                logger.error(
                    f"When exporting dossier {dossier_uuid}, there was an archive without page object images"
                )
                return ImageExportResponseV1(
                    http_status_code=400, message=ERROR_IMAGES_NOT_FOUND
                ).model_dump_json()

            upload_data_to_minio(put_upload_url, fp)

    if error_text:
        logger.error(
            f"The error of page object images export: {error_text}; Dossier: {dossier_uuid}"
        )
        return ImageExportResponseV1(
            http_status_code=400, message=error_text
        ).model_dump_json()

    return ImageExportResponseV1(
        http_status_code=200,
        message="OK",
        payload=ImageExportResponsePayload(count_page_objects=count_page_objects),
    ).model_dump_json()


async def start_server(loop):
    return await fabric_start_server(
        on_message,
        ASYNC_IMAGE_EXPORTER_WORKER_V1_QUEUE_NAME,
        client_properties={"connection_name": f"image_exporter_{platform.node()}"},
        loop=loop,
        auto_delete=False,
        name="image exporter",
    )


@click.command()
def start():
    logging.basicConfig(level=logging.INFO)

    loop = asyncio.get_event_loop()
    connection = loop.run_until_complete(start_server(loop))

    logger.info("consumer started")

    try:
        loop.run_forever()
    finally:
        loop.run_until_complete(connection.close())
