[project]
name = "dossier-backend"
version = "0.1.0"
description = "Dossier Backend with APIs for listing, reading and editing Dossier"
authors = [
    {name = "Hypodossier AG"}
]
requires-python = ">=3.10,<4.0"
dependencies = [
    "python-dotenv>=0.20.0,<0.21.0",
    "django (==4.2.21)",
    "django-ninja>=1.3.0,<2.0.0",
    "pydantic==2.10.5",
    "django-click>=2.2.0,<3.0.0",
    "django-json-widget>=1.1.1,<2.0.0",
    "click-pathlib>=2020.3.13,<2021.0.0",
    "whitenoise>=6.8.1,<7.0.0",
    "django-cors-headers>=3.14.0,<4.0.0",
    "PyJWT>=2.10.1,<3.0.0",
    "minio>=7.2.12,<8.0.0",
    "pika>=1.2.0,<2.0.0",
    "aio-pika>=9.4.1,<10.0.0",
    "pandas>=1.3.5,<2.0.0",
    "numpy==1.26.4",
    "XlsxWriter>=3.0.2,<4.0.0",
    "psycopg2-binary>=2.9.3,<3.0.0",
    "Pillow>=10.0.1,<11.0.0",
    "requests-oauthlib>=1.3.1,<2.0.0",
    "actorizer>=3.1.1,<4.0.0",
    "python-docx>=0.8.11,<0.9.0",
    "oauthlib==3.2.2",
    "django-admin-sortable>=2.3,<3.0",
    "django-colorfield>=0.13.0,<0.14.0",
    "plantuml>=0.3.0,<0.4.0",
    "pypdf2>=2.12.1,<3.0.0",
    "faker>=37.1.0,<38.0.0",
    "ratelimit>=2.2.1,<3.0.0",
    "certifi>=2025.0.0,<2026.0.0",
    "httpx[http2,socks]>=0.28.0,<0.29.0",
    "schedule>=1.1.0,<2.0.0",
    "deprecation>=2.1.0,<3.0.0",
    "backoff>=2.2.1,<3.0.0",
    "structlog>=25.2.0,<26.0.0",
    "jwcrypto>=1.5.6,<2.0.0",
    "sanic>=24.6.0,<25.0.0",
    "miniopy-async>=1.16,<2.0",
    "django-import-export>=3.3.1,<4.0.0",
    "django-structlog>=8.1.0,<9.0.0",
    "rich>=13.6.0,<14.0.0",
    "django-admin-rangefilter>=0.11.2,<0.12.0",
    "cryptography==44.0.1",
    "django-silk>=5.0.4,<6.0.0",
    "django-allauth>=0.60.1,<0.61.0",
    "factory-boy>=3.3.0,<4.0.0",
    "channels>=4.1.0,<5.0.0",
    "channels-rabbitmq>=4.0.1,<5.0.0",
    "uvicorn>=0.30.1,<0.31.0",
    "pytest-retry>=1.6.3,<2.0.0",
    "setuptools>=80.7.1,<81.0.0",
    "urllib3>=2.2.2,<3.0.0",
    "nanoid>=2.0.0,<3.0.0",
    "tenacity>=9.0.0,<10.0.0",
    "django-admin-sortable2>=2.2.4,<3.0.0",
    "rapidfuzz>=3.12.1,<4.0.0",
    "django-nested-admin>=4.1.1,<5.0.0",
    "requests (>=2.13.0,<3.0.0)",
    "reportlab (>=4.3.1,<5.0.0)",
    "pydantic-extra-types (>=2.10.3,<3.0.0)",
    "pika-stubs (>=0.1.3,<0.2.0)",
    "pydantic-extra-types (>=2.10.3,<3.0.0)",
    "httpcore (==1.0.9)",
    "django-tailwind (>=4.0.1,<5.0.0)"
]

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "hypodossier"
url = "https://gitlab.com/api/v4/groups/52803895/-/packages/pypi/simple"
priority = "primary"

[tool.poetry]
package-mode = false
requires-poetry = ">=2.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
pytest-django = "^4.9.0"
pytest-mock = "^3.6.1"
pytest-cov = "^3.0.0"
pip-audit = "^2.7.3"
pytest-asyncio = "^0.18.3"
requests-mock = "^1.9.3"
freezegun = "^1.2.2"

# Stuff that is not directly used but needs to be upgraded to comply with security-audit goes here and can be removed
# when not needed anymore
wheel = "^0.45.0" # added here to force update from 0.37 to 0.41, no direct dependency in code
twisted = "24.11.0" # added here to force update from 24.3.0 that has a security issue, can be removed afterwards

pytest-profiling = "^1.7.0"
django-extensions = "^3.2.3"
werkzeug = "^3.1.0" # is used for runserver_plus
black = "^24"
ruff = "^0.0.285"
pre-commit = "^3.3.3"
pytest-httpx = "^0.35.0"
pylama = "^8.4.1"
pytest-xdist = "^3.5.0"
pyparsing = "^3.1.2"
pydot = "^2.0.0"
deepdiff = "^7.0.1"
reportlab = "^4.3.0"
pdf2image = "^1.17.0"
watchgod = "^0.8.2"

[build-system]
requires = ["poetry-core>=2.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
#Disabled because of upgrade from black 23 to black 24
#multi_line_output = 3
#include_trailing_comma = true
#force_grid_wrap = 0
#use_parentheses = true
#ensure_newline_before_comments = true
line_length = 88
exclude = '''
/(
  | migrations
  | \.venv
)/
'''

[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "**/node_modules/**",
    "venv",
    ".venv",
    "**/migrations/**"
]
# F811 is "Redefinition of unused"
ignore = ["E501", "E722", "F811"]

[tool.pylama]
max_line_length = 3000
# C901 is "too complex"
# E722 is "do not use bare except"
# E501 is "line too long"
ignore = "E501"
#,W605,E722,W291,W391,E203,W293,C901,W191,E101"



[tool.coverage.run]
omit = [
  "**/__init__.py",
  "**/tests/**",
  "**/tests.py",
  "**/migrations/**",
  "**/fixtures/**",
  "**/admin/**",
  "**/manage.py",
  "**/settings/**",
  "**/settings.py",
  "**/asgi.py",
  "**/wsgi.py",
  ".venv/**",
]

