import djclick as click
import structlog
from django.utils import timezone

from dossier.models import Account
from e2e.account_types import account_types

logger = structlog.getLogger(__name__)


@click.command()
@click.argument("account_key", nargs=1)
@click.argument("account_type", nargs=1)
@click.option("--delete", is_flag=True)
def create_account(account_key: str, account_type: str, delete: bool = False):
    start = timezone.now()
    try:
        if delete:
            start_delete = timezone.now()
            try:
                logger.info("Delete flag set, deleting account", key=account_key)
                account = Account.objects.filter(key=account_key).first()
                if account:
                    account.delete()
            finally:
                logger.info(
                    "Delete Account duration",
                    duration=(timezone.now() - start_delete).total_seconds(),
                )
        account_types[account_type](account_key)
    finally:
        logger.info(
            "Create Account duration", duration=(timezone.now() - start).total_seconds()
        )
