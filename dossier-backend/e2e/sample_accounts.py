from pathlib import Path
from typing import List

import factory
import structlog
from pydantic import TypeAdapter


from assets import ASSETS_PATH
from dossier import schemas as dossier_schema
from dossier.doc_cat_helpers import load_document_categories_from_path
from dossier.factories import (
    AccountFactory,
    JWKFactory,
    DossierUserFactory,
    RandomUserFactory,
    DossierFactory,
    SemanticDocumentFactory,
    SemanticPageFactory,
)
from dossier.models import DocumentCategory, Account

logger = structlog.getLogger(__name__)


def create_basic_account_with_dossiers(
    key: str,
    dossier_number: int = 4,
    semantic_document_number: int = 4,
    semantic_page_number: int = 1,
):
    account = AccountFactory(key=key)
    load_document_categories_from_path(account, info_logging=False)
    JWKFactory(account=account)

    testuser1_account_user = DossierUserFactory(
        user__username="<EMAIL>", account=account
    )
    test_user1 = testuser1_account_user.user

    dossiers = []

    for i in range(0, dossier_number):
        logger.info(f"Creating dossier {i}")
        dossier = DossierFactory(account=account, owner=test_user1, name=f"Dossier {i}")
        for document_number in range(0, semantic_document_number):
            semantic_document = SemanticDocumentFactory(
                dossier=dossier, title_suffix=f"Document {document_number}"
            )

            for page_number in range(0, semantic_page_number):
                SemanticPageFactory(
                    dossier=dossier,
                    semantic_document=semantic_document,
                    page_number=page_number,
                )

        dossiers.append(dossier)
    return account


def create_basic_account(key: str):
    account = AccountFactory(key=key)
    load_document_categories_from_path(account, info_logging=False)
    JWKFactory(account=account)

    testuser1_account_user = DossierUserFactory(
        user__username="<EMAIL>", account=account
    )
    test_user1 = testuser1_account_user.user

    dossier_users = []
    for i in range(1, 500):
        user = RandomUserFactory()
        dossier_users.append(DossierUserFactory(user=user, account=account))

    dossiers = []

    dossier = DossierFactory(
        account=account, owner=test_user1, name="Dossier with Tax Declaration"
    )

    tax_declarlation = DocumentCategory.objects.get(
        account=account, id="310", name="TAX_DECLARATION"
    )

    for i in range(1, 5):
        semantic_document = SemanticDocumentFactory(
            dossier=dossier,
            document_category=tax_declarlation,
            title_suffix="Steuererklärung 2024",
        )

        SemanticPageFactory(
            dossier=dossier,
            semantic_document=semantic_document,
            page_number=0,
        )

    for i in range(1, 5):
        logger.info(f"Creating dossier {i}")
        dossier = DossierFactory(account=account, owner=test_user1, name=f"Dossier {i}")
        semantic_document = SemanticDocumentFactory(dossier=dossier)

        for j in range(0, 4):
            SemanticPageFactory(
                dossier=dossier,
                semantic_document=semantic_document,
                page_number=j,
            )

        dossiers.append(dossier)
    return account


def account_with_logs_of_dossier_for_filtering(key: str):
    account = AccountFactory(key=key)

    JWKFactory(account=account)

    load_document_categories_from_path(account, info_logging=False)

    DossierUserFactory(user__username="<EMAIL>", account=account)

    dossier_user_factory = DossierUserFactory.create_batch(50, account=account)
    users = [dossier_user.user for dossier_user in dossier_user_factory]

    dossier_users = []
    for i in range(1, 500):
        user = RandomUserFactory()
        dossier_users.append(DossierUserFactory(user=user, account=account))

    dossiers = DossierFactory.create_batch(
        1000, account=account, owner=factory.Iterator(users)
    )
    logger.info(f"Created {len(dossiers)} dossiers")


def create_empty_basic_account(key: str):
    account = AccountFactory(key=key)
    JWKFactory(account=account)
    return account


def create_an_account_with_a_dossiers(key: str):

    account = AccountFactory(key=key)
    # load_document_categories_from_path(account, info_logging=False)
    document_categories_json_path = (
        ASSETS_PATH / "document_category/default/DocumentCategory-2025-03-10.json"
    )
    text = Path(document_categories_json_path).read_text()

    # TODO change the names of these fields in classifier for consistency
    text = text.replace('"docid"', '"id"')
    text = text.replace('"desc_de"', '"description_de"')
    text = text.replace('"desc_en"', '"description_en"')
    text = text.replace('"desc_fr"', '"description_fr"')
    text = text.replace('"desc_it"', '"description_it"')

    document_categories: list[dossier_schema.DocumentCategoryLoad] = TypeAdapter(
        List[dossier_schema.DocumentCategoryLoad]
    ).validate_json(text)

    doc_catgores = [
        DocumentCategory(**category.model_dump(), account=account)
        for category in document_categories
    ]
    DocumentCategory.objects.bulk_create(doc_catgores)

    JWKFactory(account=account)

    dossier = DossierFactory(account=account, name="Downloadable Dossier")

    for i in range(1, 3):
        semantic_document = SemanticDocumentFactory(
            dossier=dossier, title_suffix=f"Document {i}"
        )
        for page_number in range(1, 4):
            SemanticPageFactory(
                dossier=dossier,
                semantic_document=semantic_document,
                page_number=page_number,
            )
    return account


def download_deactivated(key: str):
    account: Account = create_an_account_with_a_dossiers(key)
    account.enable_download_dossier = False
    account.save()
    return account
