import djclick as click

from django.db import transaction
import logging


from dossier.models import Account
from semantic_document.helpers import (
    get_semantic_documents_with_incorrect_page_numbering,
    fix_document_page_numbering,
)

logger = logging.getLogger(__name__)


@click.command()
@click.argument("account_key")
@click.option(
    "--batch-size",
    "-b",
    default=100,
    help="Number of documents to process in each batch",
    type=int,
)
@click.option(
    "--dry-run",
    "-d",
    default=True,
    help="Show what would be done without making actual changes",
)
@click.option(
    "--problem-type",
    "-p",
    default="non_zero_start",
    help="Value is either 'non_zero_start', or 'non_consecutive', or 'all'  to fix either type of problem. Can only solve one type of problem per run.",
)
def fix_semantic_document_page_numbering_batch(
    account_key: str, batch_size: int, dry_run: bool, problem_type: str
):
    """
    Fix page numbering for semantic documents in the specified account.

    Only fixes documents where pages are sequential but don't start at zero.

    Example usage:

    # Dry run to see what would change, default is dry run mode
    python manage.py fix_semantic_document_page_numbering_batch default

    # Fix documents in batches of 50
    python manage.py fix_semantic_document_page_numbering_batch default --batch-size 10 --dry-run False

    python manage.py fix_semantic_document_page_numbering_batch default --batch-size 10 --dry-run False -p non_zero_start
    python manage.py fix_semantic_document_page_numbering_batch default --batch-size 10 --dry-run False -p non_consecutive
    python manage.py fix_semantic_document_page_numbering_batch default --batch-size 10 --dry-run False -p all

    """
    try:
        account = Account.objects.get(key=account_key)
    except Account.DoesNotExist:
        logger.error(f"Account '{account_key}' not found")
        return

    logger.info(
        f"Starting page number fix for account '{account_key}' "
        f"(batch_size={batch_size}, dry_run={dry_run}, problem_type={problem_type})"
    )

    # Get documents that need fixing
    documents = get_semantic_documents_with_incorrect_page_numbering(account)
    if problem_type == "non_zero_start":
        fixable_docs = documents["start_at_one"] | documents["start_at_two_or_more"]
    elif problem_type == "non_consecutive":
        fixable_docs = documents["non_consecutive"]
    elif problem_type == "all":
        fixable_docs = (
            documents["start_at_one"]
            | documents["start_at_two_or_more"]
            | documents["non_consecutive"]
        )
    else:
        raise ValueError(
            f"Unknown problem type: {problem_type}. Valid options are 'non_zero_start', or 'non_consecutive', or 'all'"
        )

    if not fixable_docs or not fixable_docs.exists():
        logger.info("No documents found that need fixing")
        return

    total_docs = fixable_docs.count()
    logger.info(f"Found {total_docs} documents to process")

    processed_count = 0
    fixed_count = 0
    error_count = 0

    batch = fixable_docs[0:batch_size]

    for doc in batch:
        try:
            with transaction.atomic():
                # Get current page numbers for logging
                pages = doc.semantic_pages.order_by("number")
                original_numbers = list(pages.values_list("number", flat=True))

                if dry_run:
                    logger.info(
                        f"Would fix document {doc.uuid}: "
                        f"pages {original_numbers} -> "
                        f"starting from 0"
                    )
                    fixed_count += 1
                else:
                    fix_non_sequential_pages = problem_type in [
                        "non_consecutive",
                        "all",
                    ]
                    if fix_document_page_numbering(doc, fix_non_sequential_pages):
                        fixed_count += 1
                        logger.info(
                            f"Fixed document {doc.uuid}: "
                            f"pages {original_numbers} -> "
                            f"starting from 0"
                        )
                    else:
                        logger.warning(
                            f"Skipped document {doc.uuid}: " "not suitable for fixing"
                        )

        except Exception as e:
            error_count += 1
            logger.error(
                f"Error processing document {doc.uuid}: {str(e)}", exc_info=True
            )

        processed_count += 1

        if processed_count % batch_size == 0:
            logger.info(
                f"Progress: {processed_count}/{total_docs} "
                f"(fixed: {fixed_count}, errors: {error_count})"
            )

    logger.info(
        f"Processing complete: "
        f"processed {processed_count} documents, "
        f"fixed {fixed_count}, "
        f"errors {error_count}"
    )

    if dry_run:
        logger.info(
            "This was a dry run - no changes were made. Run with '--dry-run=False' to really apply changes"
        )
