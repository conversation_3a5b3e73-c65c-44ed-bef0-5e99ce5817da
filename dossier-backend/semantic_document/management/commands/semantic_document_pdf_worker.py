# import asyncio
# import json
# from time import time
# import platform
#
# import aio_pika
# import djclick as click
# import structlog
# from actorizer.broker import actor, set_broker, Broker
# from django.conf import settings
# from pydantic import BaseModel
# from structlog.contextvars import bound_contextvars
#
# from projectconfig.settings import (
#     DOSSIER_EVENT_CONSUMER_PREFETCH,
#     DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD,
#     DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE,
#     ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_V2_ROUTING_KEY,
# )
# from semantic_document.actors import process_semantic_document_pdf_tempfile_storage
#
# logger = structlog.get_logger()
#
# broker = Broker(f"{settings.RABBIT_URL}?name=pdf worker {platform.node()}")
# set_broker(broker)
#
#
# class Message(BaseModel):
#     body: str
#
#
# @actor(
#     queue_name=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_V2_ROUTING_KEY,
# )
# async def run(message: aio_pika.IncomingMessage):
#     async with message.process(reject_on_redelivered=True, requeue=True):
#         start = time()
#         with bound_contextvars(message_type=message.type):
#             # await logger.ainfo("semantic_document_pdf_worker", message=message.type)
#             # await logger.ainfo("semantic_document_pdf_worker", body=message.body)
#             await broker.publish(
#                 routing_key="cpu_bound",
#                 message=message,
#             )
#             end = time()
#             await logger.ainfo("message processed", duration=end - start)
#
#
# @actor(
#     queue_name="cpu_bound",
# )
# async def cpu_bound(message: aio_pika.IncomingMessage):
#     async with message.process(reject_on_redelivered=True, requeue=True):
#         start = time()
#         with bound_contextvars(message_type=message.type):
#             # await logger.ainfo("cpu_bound_func_foo", message=message)
#             # await logger.ainfo("cpu_bound_func_foo", body=message.body)
#             await process_semantic_document_pdf_tempfile_storage(
#                 semantic_document_pdf_request=json.loads(message.body)[
#                     "semantic_document_pdf_request"
#                 ]
#             )
#             end = time()
#             await logger.ainfo("message processed", duration=end - start)
#
#
# @actor(
#     queue_name="gpu_bound",
# )
# async def gpu_bound(message: aio_pika.IncomingMessage):
#     async with message.process(reject_on_redelivered=True, requeue=True):
#         start = time()
#         with bound_contextvars(message_type=message.type):
#             await logger.ainfo("gpu_bound_func_foo", message=message)
#             await logger.ainfo("gpu_bound_func_foo", body=message.body)
#             end = time()
#             await logger.ainfo("message processed", duration=end - start)
#
#
# # def event_consumer():
# @click.command()
# def start():
#     nice = settings.PRETTY_PRINT_LOG
#     shared_processors = [
#         # Processors that have nothing to do with output,
#         # e.g., add timestamps or log level names.
#         structlog.contextvars.merge_contextvars,
#         structlog.processors.add_log_level,
#         structlog.processors.StackInfoRenderer(),
#         structlog.dev.set_exc_info,
#     ]
#     if nice:
#         # Pretty printing when we run in a terminal session.
#         # Automatically prints pretty tracebacks when "rich" is installed
#         processors = shared_processors + [
#             structlog.processors.TimeStamper(fmt="iso", utc=False),
#             structlog.dev.ConsoleRenderer(),
#         ]
#     else:
#         # Print JSON when we run, e.g., in a Docker container.
#         # Also print structured tracebacks.
#         processors = shared_processors + [
#             structlog.processors.dict_tracebacks,
#             structlog.processors.JSONRenderer(),
#         ]
#     structlog.configure(processors)
#
#     logger.info(
#         "Start up SemanticDocumentEventConsumer",
#         THREAD_POOL_FILE_UPLOAD=DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD,
#         PREFETCH=DOSSIER_EVENT_CONSUMER_PREFETCH,
#         DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE=DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE,
#     )
#     loop = asyncio.get_event_loop()
#     connection = loop.run_until_complete(broker.run(loop))
#
#     # Use existing event loop to monitor processed files https://gitlab.com/hypodossier/document-universe/-/issues/314
#
#     try:
#         loop.run_forever()
#     finally:
#         loop.run_until_complete(connection.close())
#
#
# # @click.command()
# # def start():
# #     run_with_reloader(event_consumer)
