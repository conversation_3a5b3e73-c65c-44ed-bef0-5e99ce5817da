import copy
from collections import Counter
from datetime import datetime
from typing import List, Dict, Union, Type, Tuple
from uuid import UUID

import structlog
from django.contrib.auth import get_user_model
from django.db import models, transaction
from django.db.models import Q, QuerySet
from django.forms import model_to_dict
from django.shortcuts import get_object_or_404
from ninja.errors import HttpError

from bekb.models import CollateralAssignment
from core.behaviors import SoftDeletionModel
from core.generics import T_Model
from dossier import schemas as dossier_schemas
from dossier.helpers_access_check import (
    get_writable_dossier_from_request_with_access_check,
)
from dossier.models import Dossier, DocumentCategory, ConfidenceLevel, Account
from events.services import publish
from processed_file.models import PageObject
from semantic_document import schemas
from semantic_document.models import (
    SemanticPage,
    SemanticDocument,
    SemanticDocumentPageObject,
    SemanticPagePageObject,
)
from semantic_document.schemas import MoveSemanticPageSchema

logger = structlog.get_logger()


def model_to_dict_wfk(modelobj):
    """ "Converts the django model into dict with keeping links to the FK fields"""
    opts = modelobj._meta.fields
    modeldict = model_to_dict(modelobj)
    for m in opts:
        if m.is_relation:
            foreignkey = getattr(modelobj, m.name)
            if foreignkey:
                try:
                    modeldict[m.name] = foreignkey
                except:
                    pass
    return modeldict


def model_to_dict_with_fk_and_uuid(modelobj):
    """Converts a Django model instance into a dictionary, including ForeignKey fields and the UUID."""
    model_dict = model_to_dict_wfk(modelobj)
    model_dict["uuid"] = modelobj.uuid
    return model_dict


def delete_models_using_soft_deletion_or_not(
    qs_to_delete: QuerySet[SoftDeletionModel], hard_delete=False
):
    if hard_delete:
        qs_to_delete.hard_delete()
    else:
        qs_to_delete.delete()


# Also only used in one place
def fill_deleted_semantic_pages(
    dossier: Dossier,
    client_provided_state: List[dossier_schemas.RequestBodyResultDND],
    hard_delete=False,
):
    """ "Populates a list of deleted_semantic_pages with serialized dictionaries from the SemanticPage model
    which are removed from the database during the Drag and Drop"""
    deleted_semantic_pages = []
    semantic_prefetch = [
        "processed_page",
        "document_category",
        "page_category",
        "dossier",
        "semantic_document",
        "processed_page__processed_file__extracted_file",
    ]

    source_semantic_pages = []

    for client_state_semantic_document in client_provided_state:
        # Iterate through state that's expected to be set by client

        client_state_semantic_document_dict = (
            client_state_semantic_document.model_dump()
        )

        # Fetch the semantic_document described in client_state_semantic_document_dict
        semantic_document_object_model = dossier.semantic_documents.prefetch_related(
            "semantic_pages"
        ).get(uuid=client_state_semantic_document_dict["semantic_document_uuid"])

        # If the client has provided use with data for the semantic page
        if client_state_semantic_document_dict["data"] is not None:
            q_objects = Q()
            for client_state_semantic_page_dict in client_state_semantic_document_dict[
                "data"
            ]["semantic_pages"]:
                q_objects |= Q(
                    uuid=client_state_semantic_page_dict["uuid"],
                    # processed_page__processed_file is a little ambigous, but should not matter
                    # as we expect uuid to be unique
                    processed_page__processed_file__uuid=client_state_semantic_page_dict[
                        "source_file_uuid"
                    ],
                    processed_page__number=client_state_semantic_page_dict[
                        "source_page_number"
                    ],
                )

            # Now we get all semantic pages for a semantic document
            # but exclude semantic pages described in client_state_semantic_document_dict

            semantic_pages_qs_excluding_client_state_pages = (
                semantic_document_object_model.semantic_pages.select_related(
                    *semantic_prefetch
                )
                .all()
                .exclude(q_objects)
            )

            for source_semantic_page in semantic_pages_qs_excluding_client_state_pages:
                # gather data for the pages to be deleted
                source_semantic_pages.append(
                    create_semantic_page_moved_detail(source_semantic_page)
                )

            # delete page object from aggregated page objects by moved semantic page
            semantic_document_object_model.aggregated_page_objects.filter(
                page_object__uuid__in=semantic_pages_qs_excluding_client_state_pages.values_list(
                    "semantic_page_page_objects__page_object__uuid"
                )
            ).delete()

            deleted_semantic_pages.extend(
                delete_duplicates(semantic_pages_qs_excluding_client_state_pages)
            )
        else:
            # Else: Semantic Document is empty - remove all semantic pages
            for_cycle = list(
                semantic_document_object_model.semantic_pages.select_related(
                    *semantic_prefetch
                ).all()
            )
            for semantic_page in for_cycle:
                # gather data for the pages to be deleted
                source_semantic_pages.append(
                    create_semantic_page_moved_detail(semantic_page)
                )

                deleted_semantic_pages.append(
                    model_to_dict_with_fk_and_uuid(semantic_page)
                )
                semantic_page.hard_delete()

    # Loop through client_provided_state and delete any semantic documents that have data = None
    semantic_documents_to_delete = filter(
        lambda semantic_document_from_request: semantic_document_from_request.data
        is None,
        client_provided_state,
    )

    semantic_documents_uuid_to_delete = list(
        map(
            lambda semantic_document_from_request: semantic_document_from_request.semantic_document_uuid,
            semantic_documents_to_delete,
        )
    )

    delete_models_using_soft_deletion_or_not(
        SemanticDocument.objects.filter(uuid__in=semantic_documents_uuid_to_delete),
        hard_delete,
    )

    return source_semantic_pages, deleted_semantic_pages


def create_semantic_page_moved_detail(source_semantic_page: SemanticPage):
    source_semantic_document = source_semantic_page.semantic_document
    return schemas.SemanticPageMovedDetail(
        semantic_page_uuid=source_semantic_page.uuid,
        processed_page_uuid=source_semantic_page.processed_page.uuid,
        semantic_document=schemas.SemanticDocumentMoved(
            uuid=source_semantic_document.uuid,
            document_category_name=source_semantic_document.document_category.name,
            title=source_semantic_document.title,
        ),
        page_number=source_semantic_page.number,
    )


def delete_duplicates(qs):
    deleted_semantic_pages = []
    for object_model in qs:
        deleted_semantic_pages.append(model_to_dict_with_fk_and_uuid(object_model))

    qs.hard_delete()

    return deleted_semantic_pages


def created_list_and_update_by_result_dnd(
    created: List[SemanticPage],
    updated_index: int,
    semantic_document_object_model: SemanticDocument,
    client_state_semantic_page_dict: Dict,
    deleted_semantic_pages: List,
):

    result_after_filter = []

    for deleted_page in deleted_semantic_pages:
        if str(deleted_page["uuid"]) == client_state_semantic_page_dict["uuid"]:
            copied_deleted_page = copy.deepcopy(deleted_page)
            del copied_deleted_page["uuid"]
            result_after_filter.append(copied_deleted_page)

    semantic_page_page_object_data: List[SemanticPagePageObject] = []

    if len(result_after_filter):

        semantic_page = result_after_filter[0]
        semantic_page.update({"semantic_document": semantic_document_object_model})
        semantic_page["number"] = updated_index
        semantic_page_object_model = SemanticPage(**semantic_page)
        created.append(semantic_page_object_model)

        for page_object in client_state_semantic_page_dict["page_objects"]:
            page_object = PageObject.objects.filter(uuid=page_object["uuid"]).first()
            if page_object:
                semantic_page_page_object_data.append(
                    SemanticPagePageObject(
                        page_object=page_object,
                        semantic_page=semantic_page_object_model,
                    )
                )

    return created, semantic_page_page_object_data


# Only used in one place - move_pages_api
def save_result_dnd_to_db(
    client_provided_state: List[dossier_schemas.RequestBodyResultDND],
    dossier: Dossier,
    deleted_semantic_pages: List,
):
    """ "
    Saves DND results of document pages by updating the number field in the SemanticPage table. (Saves the ordering)
    """
    updated_numbers = []
    created = []
    page_object_data = []
    for client_state_semantic_document in client_provided_state:
        client_state_semantic_document_dict = (
            client_state_semantic_document.model_dump()
        )

        try:
            semantic_document_object_model = dossier.semantic_documents.get(
                uuid=client_state_semantic_document_dict["semantic_document_uuid"]
            )

        except SemanticDocument.DoesNotExist:
            continue

        if client_state_semantic_document_dict["data"] is None:
            continue

        for index in range(
            len(client_state_semantic_document_dict["data"]["semantic_pages"])
        ):
            client_state_semantic_page_dict = client_state_semantic_document_dict[
                "data"
            ]["semantic_pages"][index]

            try:
                semantic_page = semantic_document_object_model.semantic_pages.get(
                    uuid=client_state_semantic_page_dict["uuid"],
                    processed_page__processed_file__uuid=client_state_semantic_page_dict[
                        "source_file_uuid"
                    ],
                    processed_page__number=client_state_semantic_page_dict[
                        "source_page_number"
                    ],
                )

                semantic_page.number = index
                updated_numbers.append(semantic_page)

            except SemanticPage.DoesNotExist:
                (
                    created,
                    semantic_page_page_object_data,
                ) = created_list_and_update_by_result_dnd(
                    created=created,
                    updated_index=index,
                    semantic_document_object_model=semantic_document_object_model,
                    client_state_semantic_page_dict=client_state_semantic_page_dict,
                    deleted_semantic_pages=deleted_semantic_pages,
                )

                page_object_data.extend(semantic_page_page_object_data)

        # fix_soft_deleted_position(semantic_document_object_model)

    # log event
    # created contains also "intermediary results" of the DND (but not for move per menu).
    # E.g. if you move two pages, one of a 310-doc and one of a 245-doc together to a 210-doc
    # (see test_logging_move_semantic_pages_dnd), the created list will contain:
    # - 310-doc-pages with destination 245-doc (intermediary)
    # - 245-doc-page with destination of 210-doc (final)
    # - 310-doc-page with destination 210-doc (final)
    # I don't know how to filter out the intermediary result page, as I don't see any attribute that would distinguish it.
    # If intermediary results are present, the respective processed_page will occur multiple times in created.
    # After some testing, it seems that the original DND request body always has the destination doc as last list entry.
    # The implementation of DND seems to be buggy anyway (see ticket hypodossier/document-universe#245).
    # After this is fixed, the logging should be set up in a better way (not relying on the assumption above).

    destination_semantic_document_uuid = client_provided_state[
        -1
    ].semantic_document_uuid

    processed_pages_uuid = [created_page.processed_page_id for created_page in created]
    dest_semantic_pages = [
        created_page
        for created_page in created
        if not (
            processed_pages_uuid.count(created_page.processed_page_id) > 1
            and created_page.semantic_document.uuid
            != destination_semantic_document_uuid
        )
    ]

    SemanticPage.objects.bulk_create(created)
    SemanticPage.objects.bulk_update(updated_numbers, ["number"])
    SemanticPagePageObject.objects.bulk_create(page_object_data)
    return dest_semantic_pages


def semantic_restore_or_delete(
    dossier: Dossier,
    model_in_db: Union[QuerySet, Type[T_Model]],
    model_uuid: UUID,
    to_delete: bool,
):
    object_model = get_object_or_404(model_in_db, dossier=dossier, uuid=model_uuid)

    if to_delete:
        object_model.delete()
    else:
        object_model.recover()

    return object_model


def semantic_restore_or_delete_with_access_check(
    dossier_uuid: UUID,
    request,
    model_in_db,
    model_uuid: UUID,
    to_delete: bool,
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    return semantic_restore_or_delete(dossier, model_in_db, model_uuid, to_delete)


def get_start_work_status_from_dossier_account(dossier: Dossier):
    """
    If the account has a state machine, return the start status
    used to set the initial status of a semantic document when calling
    SemanticDocument.objects.create
    """
    sm = dossier.account.active_semantic_document_work_status_state_machine
    if sm:
        return sm.start_status
    return None


def create_semantic_document(*args, **kwargs):
    # Wrapper function which sets default work_status if not provided
    if "work_status" not in kwargs:
        dossier = kwargs.get("dossier")
        if dossier is None:
            raise ValueError(
                "The 'dossier' parameter is required when 'work_status' is not provided."
            )
        kwargs["work_status"] = get_start_work_status_from_dossier_account(
            dossier=dossier
        )
    return SemanticDocument.objects.create(*args, **kwargs)


def create_unnamed_semantic_document(dossier: Dossier, request_dict):
    """
    Handle workaround for this scenario and remove this code soon:

    combine pages api for photos delivers
    - correct id 615-EKD128
    - wrong name PLAN_FLOOR

    combine pages api for plans delivers
    - wrong id 604 (should be 604-EKD35)
    - correct name PLAN_FLOOR

    combine tax api delivers
    - correct id 310-EKD53
    - wrong name PLAN_FLOOR

    @param dossier:
    @param request_dict:
    @return:
    """
    default_doc_cat_name = "UNKNOWN"

    id = request_dict.get("document_category_id")
    name = None
    if id:
        if id == "604":
            # Use name because id 604 can be wrong if it is e.g. '604-EKD1234'
            name = "PLAN_FLOOR"
        else:
            # Use id, name might be wrong for legacy combine
            pass
    else:
        # Use name because no id available
        name = default_doc_cat_name

    if name:
        document_category = DocumentCategory.objects.get(
            name=name, account=dossier.account
        )
    else:
        document_category = DocumentCategory.objects.get(id=id, account=dossier.account)

    suffix = request_dict.get("suffix")

    if document_category.name == default_doc_cat_name:
        title_custom = "___ DOC Unnamed Document"  # ___ DOK Unbenanntes Dokument
    else:
        title_custom = None

    return SemanticDocument.objects.create(
        dossier=dossier,
        title_custom=title_custom,
        confidence_level=ConfidenceLevel.HIGH,
        confidence_formatted="100%",
        confidence_value=100,
        title_suffix=suffix,
        document_category=document_category,
        work_status=get_start_work_status_from_dossier_account(dossier=dossier),
    )


def delete_semantic_pages(list_of_semantic_page_uuid: List[str], hard_delete=False):
    deleted = []
    semantic_documents_to_validate = []

    deleted_semantic_pages = list(
        SemanticPage.objects.select_related(
            "dossier",
            "page_category",
            "semantic_document",
            "document_category",
            "processed_page",
        ).filter(uuid__in=list_of_semantic_page_uuid)
    )

    for index_semantic_page_uuid in range(len(list_of_semantic_page_uuid)):
        # Complicated logic to fetch the semantic pages in the same order as their uuids appear in list_of_semantic_page_uuid
        semantic_page_to_delete = next(
            filter(
                lambda x: x.uuid
                == UUID(list_of_semantic_page_uuid[index_semantic_page_uuid]),
                deleted_semantic_pages,
            )
        )

        semantic_page = model_to_dict_wfk(semantic_page_to_delete)

        deleted.append(semantic_page)

        # Delete aggregated page object of the page that is going to be deleted
        semantic_page_to_delete.semantic_document.aggregated_page_objects.filter(
            page_object__uuid__in=semantic_page_to_delete.semantic_page_page_objects.values_list(
                "page_object__uuid"
            )
        ).delete()

        semantic_documents_to_validate.append(semantic_page["semantic_document"])

    # Now delete semantic pages (hard or soft)
    delete_models_using_soft_deletion_or_not(
        SemanticPage.objects.filter(uuid__in=list_of_semantic_page_uuid), hard_delete
    )

    # Now delete the semantic documents that have no pages left that reference them.
    semantic_documents_to_hard_delete, semantic_documents_to_soft_delete = (
        get_empty_semantic_documents(semantic_documents_to_validate)
    )
    delete_models_using_soft_deletion_or_not(
        SemanticDocument.objects.filter(uuid__in=semantic_documents_to_hard_delete),
        hard_delete=True,
    )
    delete_models_using_soft_deletion_or_not(
        SemanticDocument.objects.filter(uuid__in=semantic_documents_to_soft_delete),
        hard_delete=False,
    )

    return deleted


def get_empty_semantic_documents(
    list_of_semantic_document,
) -> Tuple[List[UUID], List[UUID]]:
    """
    Find semantic documents that have no pages left. There are 2 categories:
     - documents that have no pages at all (neither active nor soft-deleted) -> should be hard deleted
     - documents that have only soft-deleted pages -> should be soft deleted

    Args:
        list_of_semantic_document: List of semantic documents to check

    Returns:
        List of UUIDs of semantic documents that should be hard deleted
        List of UUIDs of semantic documents that should be soft deleted
    """
    semantic_documents_to_hard_delete = []
    semantic_documents_to_soft_delete = []

    for semantic_document in list_of_semantic_document:
        # Check if the document has any pages at all (including soft-deleted ones)
        has_any_pages_at_all = SemanticPage.all_objects.filter(
            semantic_document=semantic_document
        ).exists()
        if not has_any_pages_at_all:
            semantic_documents_to_hard_delete.append(semantic_document.uuid)
        else:
            has_any_alive_pages = SemanticPage.objects.filter(
                semantic_document=semantic_document
            ).exists()
            if not has_any_alive_pages:
                semantic_documents_to_soft_delete.append(semantic_document.uuid)

    return semantic_documents_to_hard_delete, semantic_documents_to_soft_delete


def create_page_objects(page_objects_json_list, semantic_page_object_model):
    semantic_page_page_object_data = []
    for page_object in page_objects_json_list:
        page_object = PageObject.objects.filter(uuid=page_object.uuid).first()

        if page_object:
            semantic_page_page_object_data.append(
                SemanticPagePageObject(
                    page_object=page_object, semantic_page=semantic_page_object_model
                )
            )
    return semantic_page_page_object_data


def save_deleted_semantic_page(
    deleted_data, semantic_document: SemanticDocument
) -> List[SemanticPage]:
    created_semantic_pages = []
    for semantic_page_index in range(len(deleted_data)):
        semantic_page = deleted_data[semantic_page_index]

        semantic_page.update({"semantic_document": semantic_document})
        semantic_page.update({"number": semantic_page_index})
        semantic_page_object_model = SemanticPage(**semantic_page)

        created_semantic_pages.append(semantic_page_object_model)

    return SemanticPage.objects.bulk_create(created_semantic_pages)


def create_page_object_to_moved_semantic_page(
    semantic_pages_schema: List[MoveSemanticPageSchema],
    created_semantic_pages: List[SemanticPage],
) -> List[SemanticPagePageObject]:
    """
    semantic_pages_schema should have one list entry for each list entry in
    created_semantic_pages

    @param semantic_pages_schema:
    @param created_semantic_pages:
    @return: List of all the created page objects for all pages
    """
    page_objects = []

    for new_semantic_page in created_semantic_pages:
        source_file_uuid: UUID = new_semantic_page.processed_page.processed_file.uuid
        page_number: int = new_semantic_page.processed_page.number

        # Step 1: find the schema for the current semantic page
        # based on uuid of processed file and page number in processed file
        matching_page_schema = None
        for schema in semantic_pages_schema:
            if (
                source_file_uuid == UUID(schema.source_file_uuid)
                and page_number == schema.number
            ):
                matching_page_schema = schema
                break

        # Step 2: copy all page objects to the new semantic page
        if matching_page_schema:
            page_objects.extend(
                create_page_objects(
                    matching_page_schema.page_objects, new_semantic_page
                )
            )

    return SemanticPagePageObject.objects.bulk_create(page_objects)


def create_aggregated_page_objects(
    semantic_document: SemanticDocument, page_objects: List[SemanticPagePageObject]
):
    list_page_objects = list(map(lambda x: x.page_object, page_objects))
    list_aggregated = list(
        map(
            lambda x: SemanticDocumentPageObject(
                page_object=x, semantic_document=semantic_document
            ),
            list_page_objects,
        )
    )

    SemanticDocumentPageObject.objects.bulk_create(list_aggregated)


def create_duplicates_semantic_page(
    semantic_pages: List[schemas.MoveSemanticPageSchema],
    list_semantic_pages: List[SemanticPage],
    semantic_document: SemanticDocument,
):
    duplicate = []
    for semantic_page_index in range(len(semantic_pages)):
        semantic_page = next(
            filter(
                lambda semantic_page_from_qs: semantic_page_from_qs.uuid
                == UUID(semantic_pages[semantic_page_index].uuid),
                list_semantic_pages,
            )
        )

        semantic_page_dict = model_to_dict_wfk(semantic_page)
        semantic_page_dict.update({"semantic_document": semantic_document})
        semantic_page_dict.update({"number": semantic_page_index + 1})

        duplicate.append(SemanticPage(**semantic_page_dict))

    return SemanticPage.objects.bulk_create(duplicate)


def recover_aggregated_page_objects(semantic_page: SemanticPage):
    aggregated_page_objects = map(
        lambda semantic_page_page_objects: SemanticDocumentPageObject(
            page_object=semantic_page_page_objects.page_object,
            semantic_document=semantic_page.semantic_document,
        ),
        semantic_page.semantic_page_page_objects.all(),
    )

    SemanticDocumentPageObject.objects.bulk_create(aggregated_page_objects)


def delete_aggregated_page_objects(deleted_semantic_page: SemanticPage):
    SemanticDocumentPageObject.objects.filter(
        semantic_document=deleted_semantic_page.semantic_document,
        page_object__semantic_page_page_objects__semantic_page=deleted_semantic_page,
    ).delete()


def validate_length_of_semantic_document_title(title: str):
    if len(title) < 3:
        raise HttpError(400, "Minimum length for document title is 3 characters")


def update_semantic_document_title(
    account: Account,
    user: get_user_model(),
    request_data,
    semantic_document: SemanticDocument,
):
    document_category = get_object_or_404(
        DocumentCategory,
        account=account,
        id=request_data["id"],
        name=request_data["name"],
    )
    document_category_before = semantic_document.document_category
    title_before = semantic_document.title

    title = (
        f"{request_data['id']} {request_data['name']} {request_data['suffix']}".strip()
    )
    collateral = CollateralAssignment.objects.filter(
        semantic_document_id=request_data["uuid"]
    ).first()
    if collateral:
        collateral.delete()

    validate_length_of_semantic_document_title(title)

    if semantic_document.title_custom:
        semantic_document.title_custom = None

    semantic_document.title_suffix = request_data["suffix"]
    semantic_document.document_category = document_category

    publish(
        schemas.SemanticDocumentRenamedEvent(
            username=user.username,
            source=schemas.SemanticDocumentMoved(
                uuid=semantic_document.uuid,
                document_category_name=document_category_before.name,
                title=title_before,
            ),
            destination=schemas.SemanticDocumentMoved(
                uuid=semantic_document.uuid,
                document_category_name=document_category.name,
                title=title,
            ),
        )
    )


def get_document_type_recommendations(semantic_pages):
    c = Counter(
        [
            page.document_category
            for page in semantic_pages
            if not page.document_category.exclude_for_recommendation
            and page.document_category != page.semantic_document.document_category
        ]
    )

    return [category for category, count in c.most_common(3)]


def document_cat_recommendations_for_pages(
    pages: List[SemanticPage], doc_cat_of_document: DocumentCategory or None
):
    """Get document category recommendations based on page categories.

    The recommendations are determined by:
    1. Counting occurrences of each category in the pages
    2. Excluding categories marked with exclude_for_recommendation=True
    3. Excluding the document's own category if provided
    4. Sorting by count (descending) and category ID (ascending)
    5. Taking the top 3 categories

    Args:
        pages: List of SemanticPage objects to analyze
        doc_cat_of_document: Optional category to exclude from recommendations

    Returns:
        List of up to 3 DocumentCategory objects, ordered by count desc, ID asc
    """
    logger.debug(f"Getting recommendations for {len(pages)} pages")
    if doc_cat_of_document:
        logger.debug(
            f"Excluding document category: {doc_cat_of_document.name} ({doc_cat_of_document.id})"
        )

    # Count document categories
    c = Counter(
        [
            page.document_category
            for page in pages
            if not page.document_category.exclude_for_recommendation
            and page.document_category != doc_cat_of_document
        ]
    )

    logger.debug(f"Category counts: {[(cat.name, count) for cat, count in c.items()]}")

    # Get all categories with their counts
    categories_with_counts = [(cat, count) for cat, count in c.items()]

    # Sort by:
    # 1. Count (descending)
    # 2. Category ID (ascending) - this makes the order deterministic when counts are equal
    sorted_categories = sorted(
        categories_with_counts,
        key=lambda x: (
            -x[1],
            x[0].id,
        ),  # -x[1] for descending count, x[0].id for ascending ID
    )

    logger.debug(
        f"Sorted categories: {[(cat.name, count) for cat, count in sorted_categories]}"
    )

    # Take top 3 categories
    result = [category for category, _ in sorted_categories[:3]]
    logger.debug(f"Final recommendations: {[cat.name for cat in result]}")

    return result


def create_copy_file_name(filename, exist_original_file_names, rename=False):
    split_name = filename.split(".")

    file_format = split_name[-1]
    file_format_len = len(file_format)

    file_name = filename[: -file_format_len - 1]

    if filename not in exist_original_file_names:
        return filename
    else:
        if rename:
            new_name = f"{file_name[:-1]}{int(filename[-len(file_format) - 2]) + 1}.{file_format}"
            return create_copy_file_name(
                new_name, exist_original_file_names, rename=True
            )
        else:
            new_name = f"{file_name}_copy_1.{file_format}"
            return create_copy_file_name(
                new_name, exist_original_file_names, rename=True
            )


def compute_semantic_document_date(
    semantic_document: SemanticDocument,
) -> datetime.date:
    """
    If a date is set in semantic_document.custom_semantic_document_date then this date is used.
    Else we use the first page in the document, look up the upload date of the related original file
    and use that. If the document has no pages we use today's date.

    @param semantic_document:
    @return:
    """
    if semantic_document.custom_semantic_document_date:
        return semantic_document.custom_semantic_document_date

    default_date: datetime.date = compute_default_semantic_document_date(
        semantic_document
    )
    return default_date


def compute_default_semantic_document_date(
    semantic_document: SemanticDocument,
) -> datetime.date:
    """
    We use the first page in the document, look up the upload date of the related original file
    and use that. If the document has no pages we use today's date.

    @param semantic_document:
    @return:
    """
    first_semantic_page = semantic_document.semantic_pages.first()
    if first_semantic_page is None:
        return datetime.today().date()

    processed_page = first_semantic_page.processed_page
    document_date = (
        processed_page.processed_file.extracted_file.original_file.created_at
    )
    return document_date.date() if document_date else datetime.today().date()


def get_semantic_documents_with_incorrect_page_numbering(account: Account) -> dict:
    """
    Get all semantic documents with incorrect page numbering.
    A semantic document has incorrect page numbering if:
    - The page numbers are not starting correctly (min_page_number > 0)
    - The page numbers are not consecutive (has gaps in the sequence)

    Returns:
        dict with two keys:
        - 'non_zero_start': QuerySet of documents that don't start at page 0
        - 'non_consecutive': QuerySet of documents with gaps in page sequence
    """
    base_queryset = (
        SemanticDocument.objects.annotate(
            max_page_number=models.Max("semantic_pages__number"),
            min_page_number=models.Min("semantic_pages__number"),
            page_count=models.Count("semantic_pages__number"),
        )
        .filter(dossier__account=account)
        .order_by("-created_at")
    )

    return {
        "start_at_one": base_queryset.filter(min_page_number=1),
        "start_at_two_or_more": base_queryset.filter(min_page_number__gt=1),
        "non_consecutive": base_queryset.filter(
            ~Q(
                page_count=models.F("max_page_number") - models.F("min_page_number") + 1
            ),
            min_page_number__lte=0,  # Exclude documents that are already in start_at_one or start_at_two_or_more
        ),
    }


def fix_document_page_numbering(
    document: SemanticDocument,
    fix_non_sequential_pages: bool = True,
    include_soft_deleted_pages: bool = True,
) -> bool:
    """
    Fix page numbering for a semantic document to start from zero.
    Handles both non-sequential pages and duplicate page numbers.

    Args:
        document: SemanticDocument instance to fix
        fix_non_sequential_pages: Boolean to control whether to fix non-sequential pages
        include_soft_deleted_pages: whether to include soft-deleted pages when counting and ordering in the fix.
          For all real use cases we want this to be True. So every page_number is unique per document - including
          deleted pages. This is needed to ensure that undeletion of deleted pages re-inserts them in the correct position.

    Returns:
        bool: True if fixed successfully, False if document couldn't be fixed or document has no pages or there is nothing to fix.
    """
    with transaction.atomic():

        if include_soft_deleted_pages:
            pages: QuerySet[SemanticPage] = (
                SemanticPage.all_objects.filter(semantic_document=document)
                .order_by("number", "deleted_at", "-updated_at")
                .all()
            )
        else:
            # Get pages ordered by number and then by updated_at to maintain relative order
            # Technically, we don't need .filter(deleted_at=None) as its in the default manager
            pages: QuerySet[SemanticPage] = (
                SemanticPage.objects.filter(semantic_document=document)
                .order_by("number", "deleted_at", "-updated_at")
                .filter(deleted_at=None)
                .all()
            )

        if not pages:
            logger.info(
                f"Document {document.uuid} has no pages, so no page numbers to fix"
            )
            return False

        # Check current state
        total_pages = pages.count()
        distinct_page_numbers = (
            document.semantic_pages.values("number").distinct().count()
        )
        min_page = pages[0].number
        max_page = pages.last().number

        # This check alone is not enough, because the we could have [0,0] for a 2 page document. But in combination
        # with is_non_sequential it is enough as that would catch it.
        has_duplicates = distinct_page_numbers != total_pages

        is_non_sequential = (max_page - min_page + 1) != total_pages

        # Early return if no fix needed
        if min_page == 0 and not has_duplicates and not is_non_sequential:
            return False

        if not fix_non_sequential_pages and is_non_sequential:
            logger.info(f"Document {document.uuid} has non-sequential pages")
            return False

        if has_duplicates:
            logger.info(
                f"Document {document.uuid} has duplicate page numbers: "
                f"{total_pages} total pages but {distinct_page_numbers} distinct numbers"
            )

        # Update pages one by one to avoid conflicts
        for new_number, page in enumerate(pages):
            page.number = new_number
            page.save(update_fields=["number"])

        logger.info(
            f"Fixed page numbering for document {document.uuid}. "
            f"Original range: {min_page}-{max_page}, "
            f"Had duplicates: {has_duplicates}, "
            f"Was non-sequential: {is_non_sequential}"
        )
        return True


def fix_soft_deleted_position_block_approach(
    semantic_document: SemanticDocument, initial_pages: List[SemanticPage]
):
    # We might have soft deleted this document in an earlier function
    if semantic_document is None or semantic_document.semantic_pages.count() == 0:
        return

    # When we move pages to a new document, we don't take into account soft-deleted pages.
    # these have duplicate numbers, so we need to fix them, and make sure they align correctly

    current_order_semantic_pages = SemanticPage.all_objects.filter(
        semantic_document=semantic_document
    ).order_by("number", "deleted_at", "-updated_at")

    # Check to see if we have duplicate page numbers
    # Check current state
    total_pages = current_order_semantic_pages.count()
    distinct_page_numbers = current_order_semantic_pages.distinct("number").count()
    min_page = current_order_semantic_pages[0].number
    max_page = current_order_semantic_pages.last().number
    has_duplicates = distinct_page_numbers != total_pages
    is_non_sequential = (max_page - min_page + 1) != total_pages

    if not has_duplicates and not is_non_sequential and min_page == 0:
        return

    if total_pages == 0:
        return

    if has_duplicates and current_order_semantic_pages.exists():

        logger.info(
            "Initial pages",
            initial_pages=[(page.number, page) for page in initial_pages],
        )
        logger.info(
            "current_order_semantic_pages",
            current_order_semantic_pages=[
                (page.number, page) for page in current_order_semantic_pages
            ],
        )
        logger.info(
            "Soft Deleted Pages",
            current_order_semantic_pages=[
                (page.number, page)
                for page in current_order_semantic_pages
                if page.deleted_at is not None
            ],
        )
        # Identify soft-deleted blocks with boundaries
        # Build new order using active page objects from the API.
        new_order = list(current_order_semantic_pages.filter(deleted_at=None).all())

        # Identify soft-deleted blocks and their boundaries.
        block = []

        # Each entry: (active_page_before, active_page_after, block)
        block_boundaries = []

        last_active = None
        for page in initial_pages:
            if page.deleted_at is None:  # Active page
                if block:
                    # End of a soft-deleted block.
                    block_boundaries.append((last_active, page, block))
                    block = []
                last_active = page
            else:
                block.append(page)
        # Handle a soft-deleted block at the end of the document.
        if block:
            block_boundaries.append((last_active, None, block))

        # Reinsert soft-deleted blocks based on boundaries.
        for before_page, after_page, block in block_boundaries:
            # Determine the insertion index based on the active page boundaries.
            if after_page in new_order:
                insert_index = new_order.index(after_page)
            elif before_page in new_order:
                insert_index = new_order.index(before_page) + 1
            else:
                # If both boundaries aren't found, append at the end.
                insert_index = len(new_order)
            new_order[insert_index:insert_index] = block

        # Update the page numbers sequentially.
        for new_number, page in enumerate(new_order):
            page.number = new_number
            page.save()

    fix_document_page_numbering(
        document=semantic_document,
        fix_non_sequential_pages=True,
        include_soft_deleted_pages=True,
    )
