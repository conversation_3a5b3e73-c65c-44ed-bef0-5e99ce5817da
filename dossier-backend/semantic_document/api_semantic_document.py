from datetime import datetime, timezone
from typing import List, Optional
from uuid import UUID

from django.shortcuts import get_object_or_404
from ninja import Router
from ninja.errors import HttpError

from dossier import schemas as dossier_schemas
from dossier.helpers_access_check import (
    get_dossier_from_request_with_access_check,
    get_writable_dossier_from_request_with_access_check,
)
from semantic_document.helpers import compute_semantic_document_date
from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.services import (
    export_aggregate_page_objects_from_semantic_document,
    get_ordered_semantic_page_objects,
    api_set_semantic_document_ready_for_export,
    assert_semantic_document_is_writable,
)
from dossier import helpers as dossier_helpers
from semantic_document.schemas import (
    SemanticPage as SemanticPageSchema,
    SemanticDocumentDateSchema,
)

semantic_document_router = Router()


@semantic_document_router.get(
    "/{semantic_document_uuid}/all-page-objects",
    response=List[dossier_schemas.PageObjectApiDataWithUUID],
    url_name="semantic-document-all-page-objects",
    description="Get all page objects for a specific semantic document",
)
def get_semantic_document_page_objects(request, semantic_document_uuid: UUID):

    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)

    # Security access check
    get_dossier_from_request_with_access_check(request, semantic_document.dossier.uuid)

    semantic_document_page_objects = get_ordered_semantic_page_objects(
        semantic_document
    )

    return [
        dossier_helpers.prepare_page_object_v2(page_object)
        for page_object in semantic_document_page_objects
    ]


@semantic_document_router.get(
    "/{semantic_document_uuid}/aggregate-page-objects",
    response=List[dossier_schemas.PageObjectApiDataWithUUID],
    url_name="semantic-document-aggregate-page-objects",
    description="Get aggregate page objects for a specific semantic document",
)
def get_semantic_document_aggregate_page_objects(request, semantic_document_uuid: UUID):

    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)

    # Security access check
    get_dossier_from_request_with_access_check(request, semantic_document.dossier.uuid)

    return export_aggregate_page_objects_from_semantic_document(
        semantic_document=semantic_document
    )


@semantic_document_router.post(
    "/{semantic_document_uuid}/set-state-ready-for-export",
    url_name="semantic-document-set-state-ready-for-export",
    description="Set the state of a Semantic document, and dispatch export process. "
    "Will only process a document that has SemanticDocument.work_state=IN_FRONT_OFFICE",
    response=Optional[UUID],
)
def set_dossier_ready_for_export(request, semantic_document_uuid: UUID):
    semantic_document = get_object_or_404(
        SemanticDocument,
        uuid=semantic_document_uuid,
    )
    assert_semantic_document_is_writable(semantic_document)

    # Security access check
    dossier = get_writable_dossier_from_request_with_access_check(
        request, semantic_document.dossier.uuid
    )

    return api_set_semantic_document_ready_for_export(
        dossier=dossier, semantic_document_uuid=semantic_document_uuid
    )


@semantic_document_router.get(
    "/{semantic_document_uuid}/semantic-pages",
    response=List[SemanticPageSchema],
    url_name="get-semantic-pages",
    description="Get all semantic pages in a specific semantic document",
)
def get_semantic_pages(
    request, semantic_document_uuid: UUID, show_soft_deleted_pages: bool = False
):
    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)
    get_dossier_from_request_with_access_check(request, semantic_document.dossier.uuid)
    manager = (
        SemanticPage.all_objects if show_soft_deleted_pages else SemanticPage.objects
    )

    return [
        SemanticPageSchema(
            uuid=semantic_page.uuid,
            semantic_document_uuid=semantic_page.semantic_document.uuid,
            deleted=show_soft_deleted_pages,
            page_number=semantic_page.number,
            image_url=str(semantic_page.processed_page.image.fast_url),
            rotation_angle=semantic_page.rotation_angle,
            document_category=semantic_page.document_category.name,
            page_category=semantic_page.page_category.name,
            created_at=semantic_page.created_at,
            updated_at=semantic_page.updated_at,
        )
        for semantic_page in manager.filter(
            semantic_document=semantic_document
        ).order_by("number")
    ]


@semantic_document_router.get(
    "/{semantic_document_uuid}/date",
    response=SemanticDocumentDateSchema,
    url_name="get-semantic-document-date",
    description="Get or initialize date for semantic document",
)
def get_semantic_document_date(request, semantic_document_uuid: UUID):
    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)
    dossier = get_dossier_from_request_with_access_check(
        request, semantic_document.dossier.uuid
    )
    if not dossier.account.enable_custom_semantic_document_date:
        raise HttpError(401, "You do not have permission to perform this action.")
    # Client really wants date with a Z timezone, hence casting to UTC
    return SemanticDocumentDateSchema(
        semantic_document_date=datetime.combine(
            compute_semantic_document_date(semantic_document),
            datetime.min.time(),
            tzinfo=timezone.utc,
        )
    )
