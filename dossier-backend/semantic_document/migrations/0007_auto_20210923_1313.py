# Generated by Django 3.2.6 on 2021-09-23 11:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('processed_file', '0002_pageobject_visible'),
        ('semantic_document', '0006_remove_semanticdocument_filename'),
    ]

    operations = [
        migrations.AddField(
            model_name='semanticpage',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='semanticpagepageobject',
            name='page_object',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='semantic_page_page_objects', to='processed_file.pageobject'),
        ),
    ]
