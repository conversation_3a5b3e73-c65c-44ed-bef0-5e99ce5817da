from uuid import UUID

from django.http import Http404

from dossier.services_external import get_dossier_with_access_check_api
from semantic_document.models import SemanticPageUserAnnotations, SemanticPage
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
    create_context_for_semantic_document_state_transition,
)
from statemgmt.models import StateMachine, Status
from statemgmt.services import validate_state_transition
from workers.models import SemanticDocumentExport
from semantic_document.constants import (
    DEFAULT_HIGHLIGHT_COLOR,
    EXAMPLE_HIGHLIGHT1_GROUP_UUID,
    EXAMPLE_HIGHLIGHT2_GROUP_UUID,
    EXAMPLE_COMMENT_GROUP_UUID,
    EXAMPLE_HIGHLIGHT1_BOXES,
    EXAMPLE_HIGHLIGHT2_BOXES,
    EXAMPLE_COMMENT_DATA,
)


def update_semantic_document_export_status(
    request,
    external_dossier_id: str,
    semantic_document_uuid: UUID,
    target_state: SemanticDocumentState,
    error_message: str = None,
):
    if target_state not in [
        SemanticDocumentState.EXPORT_DONE,
        SemanticDocumentState.EXPORT_ERROR,
        SemanticDocumentState.EXPORT_IN_PROGRESS,
    ]:
        raise Exception("Invalid argument")

    dossier = get_dossier_with_access_check_api(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document = dossier.semantic_documents.get(
        uuid=semantic_document_uuid, dossier=dossier
    )

    semdoc_exports = list(
        SemanticDocumentExport.objects.filter(
            semantic_document__dossier=dossier,
            done__isnull=False,
            semantic_document__uuid=semantic_document_uuid,
            semantic_document__work_status__key__in=[
                SemanticDocumentState.EXPORT_AVAILABLE.value,
                SemanticDocumentState.EXPORT_IN_PROGRESS.value,
                SemanticDocumentState.EXPORT_ERROR.value,
            ],
        ).all()
    )

    if len(semdoc_exports) == 0:
        raise Http404("No pending export found for this semantic document")
    affected_semdoc_uuids = {
        semantic_document.semantic_document_id for semantic_document in semdoc_exports
    }

    if (
        len(affected_semdoc_uuids) != 1
        or next(iter(affected_semdoc_uuids)) != semantic_document_uuid
    ):
        raise Http404("Could not identify unique semantic document")

    state_machine: StateMachine = (
        dossier.account.active_semantic_document_work_status_state_machine
    )
    completed_state = Status.objects.get(
        key=target_state.value,
        state_machine=state_machine,
    )

    validate_state_transition(
        context=create_context_for_semantic_document_state_transition(),
        current_status=semantic_document.work_status,
        next_status=completed_state,
    )

    for export in semdoc_exports:
        if error_message:
            export.error_message = error_message
            export.save()
        semantic_document = export.semantic_document
        semantic_document.work_status = completed_state
        semantic_document.save()

    affected_export_uuids = [
        semantic_document.uuid for semantic_document in semdoc_exports
    ]
    return affected_export_uuids


def create_highlight_annotations(
    semantic_page, highlight_group_uuid, boxes, hexcolor="#FFFF00"
):
    """Create highlight annotations for a semantic page

    Args:
        semantic_page: The semantic page to add annotations to
        highlight_group_uuid: UUID for grouping related highlights
        boxes: List of dictionaries containing bbox coordinates
        hexcolor: Hex color code for the highlight (default: yellow)
    """
    for box in boxes:
        SemanticPageUserAnnotations.objects.create(
            semantic_page=semantic_page,
            annotation_group_uuid=highlight_group_uuid,
            annotation_type="highlight",
            text=None,
            bbox_top=box["bbox_top"],
            bbox_left=box["bbox_left"],
            bbox_width=box["bbox_width"],
            bbox_height=box["bbox_height"],
            hexcolor=hexcolor,
        )


def create_comment_annotation(
    semantic_page,
    comment_group_uuid,
    text,
    bbox_top,
    bbox_left,
    bbox_width,
    bbox_height,
    hexcolor="#FFFF00",
):
    """Create a comment annotation for a semantic page

    Args:
        semantic_page: The semantic page to add the comment to
        comment_group_uuid: UUID for the comment
        text: The comment text
        bbox_top: Top position (0-1)
        bbox_left: Left position (0-1)
        bbox_width: Width (0-1)
        bbox_height: Height (0-1)
        hexcolor: Hex color code for the comment box (default: yellow)
    """
    return SemanticPageUserAnnotations.objects.create(
        semantic_page=semantic_page,
        annotation_group_uuid=comment_group_uuid,
        annotation_type="comment",
        text=text,
        bbox_top=bbox_top,
        bbox_left=bbox_left,
        bbox_width=bbox_width,
        bbox_height=bbox_height,
        hexcolor=hexcolor,
    )


def create_example_annotations_simply_law(semantic_page: SemanticPage) -> None:
    """Create example annotations for the simply law document."""
    # Create first highlight annotation
    create_highlight_annotations(
        semantic_page=semantic_page,
        highlight_group_uuid=EXAMPLE_HIGHLIGHT1_GROUP_UUID,
        boxes=EXAMPLE_HIGHLIGHT1_BOXES,
        hexcolor=DEFAULT_HIGHLIGHT_COLOR,
    )

    # Create second highlight annotation
    create_highlight_annotations(
        semantic_page=semantic_page,
        highlight_group_uuid=EXAMPLE_HIGHLIGHT2_GROUP_UUID,
        boxes=EXAMPLE_HIGHLIGHT2_BOXES,
        hexcolor=DEFAULT_HIGHLIGHT_COLOR,
    )

    # Create comment annotation
    create_comment_annotation(
        semantic_page=semantic_page,
        comment_group_uuid=EXAMPLE_COMMENT_GROUP_UUID,
        **EXAMPLE_COMMENT_DATA,
        hexcolor=DEFAULT_HIGHLIGHT_COLOR,
    )
