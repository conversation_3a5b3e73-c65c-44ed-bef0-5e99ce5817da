from collections import Counter
from datetime import datetime
from http import HTT<PERSON>tatus
from typing import List
from uuid import UUID

import structlog
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Case, When, <PERSON><PERSON><PERSON><PERSON>
from django.db import transaction
from django.db.models import F, Prefetch
from django.shortcuts import get_object_or_404
from ninja import Router
from ninja.errors import HttpError


from bekb.collaterals import find_correct_real_estate_properties_for_collateral
from bekb.models import CollateralAssignment, Collateral, CollateralRealEstateProperty
from core import schema as core_schema
from doccheck.models import CompletenessRule
from dossier import helpers_v2
from dossier import models as dossier_models
from dossier import schemas as dossier_schemas
from dossier import helpers as dossier_helpers
from dossier.doccheck_api import router as doccheck_router, check_case_access
from dossier.helpers import is_document_topic_property
from dossier.helpers_access_check import (
    get_dossier_from_request_with_access_check,
    get_writable_dossier_from_request_with_access_check,
)
from dossier.models import DocumentCategory, Dossier
from events.services import publish
from processed_file.models import ConfidenceLevel
from semantic_document import helpers as semantic_document_helpers, models
from semantic_document import schemas
from semantic_document.events import create_semantic_pages_moved_event
from semantic_document.helpers import (
    update_semantic_document_title,
    compute_default_semantic_document_date,
    fix_soft_deleted_position_block_approach,
)
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPagePageObject,
)
from semantic_document.schemas import (
    MoveSemanticPageSchema,
    COMBINE_PAGES_UNASSIGNED,
)
from semantic_document.services import (
    assign_semantic_documents_to_doccheck_rules_automatically,
    assign_semantic_documents_to_doccheck_rule,
    calculate_combine_pages_real_estate_properties,
    copy_or_move_semantic_pages_to_new_document,
    assert_semantic_document_is_writable,
    assert_semantic_documents_are_writable,
)
from semantic_document.services_doccheck import get_doccheck_document_assignments

semantic_documents_router = Router()

logger = structlog.get_logger(__name__)


# TODO: not sure we need this
@semantic_documents_router.get(
    "/realestate-properties/{semantic_document_uuid}",
    response=List[schemas.RealEstatePropertyEntity],
    url_name="get-real-estate-properties",
    description="Get all RealEstateProperty associated to dossier from a semantic document UUID."
    "This includes properties with a FK to dossier but have not been assigned to a semantic document.",
)
def get_realestate_properties(request, semantic_document_uuid: UUID):
    # Get all RealEstateProperty assigned to dossier associated with semantic document
    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)

    # We use this function to check if the user has access semantic document via dossier
    dossier = get_dossier_from_request_with_access_check(
        request, semantic_document.dossier.uuid
    )
    return [
        schemas.RealEstatePropertyEntity.model_validate(realestateproperty)
        for realestateproperty in dossier_models.RealestateProperty.objects.filter(
            dossier=dossier
        )
        .distinct("key")
        .all()
    ]


@semantic_documents_router.get(
    "/entity_mapping/dossier/{dossier_uuid}",
    response=schemas.SemanticDocumentEntityMappings,
    url_name="get-entity-mappings-dossier",
    description="Get entity mappings for semantic dossiers associated with a dossier UUID."
    "returned as a K:V mapping of semantic_document_uuid : List[entity_type]",
)
def get_category_mappings_for_dossier(request, dossier_uuid: UUID):
    """We need to be able to assign entity mappings to a dossier, so that we are allowed to assign certain
    properties e.g. realestate_property to a semantic document.

    """
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_documents = SemanticDocument.objects.filter(dossier=dossier).all()

    mappings = {}

    for semantic_document in semantic_documents:
        if is_document_topic_property(semantic_document.document_category):
            mappings[str(semantic_document.uuid)] = [
                schemas.EntityTypes.REAL_ESTATE_PROPERTY
            ]

    return schemas.SemanticDocumentEntityMappings(type_mappings=mappings)


@semantic_documents_router.get(
    "/realestate-properties/dossier/{dossier_uuid}",
    response=List[schemas.RealEstatePropertyEntity],
    url_name="get-real-estate-properties-dossier",
    description="Get all RealEstateProperty assigned to a dossier via a dossier UUID. This includes any "
    "that are not assigned to a semantic document. This is used to populate the dropdown in the UI.",
)
def get_realestate_properties_for_dossier(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    return (
        dossier_models.RealestateProperty.objects.filter(dossier=dossier)
        .distinct("key")
        .all()
    )


@semantic_documents_router.get(
    "/realestate-properties/dossier/{dossier_uuid}/assigned",
    response=List[schemas.RealEstatePropertyAssignment],
    url_name="get-real-estate-properties-dossier-assigned",
    description="Get all RealEstateProperty assigned to a dossier and assigned to a semantic document "
    "via a dossier UUID. We use this in the frontend, to do a single batch call to find all semantic_documents with"
    "a realestate_property assignment and then cache the result using dossier uuid as the key",
)
def get_assigned_realestate_properties_for_dossier(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    return (
        dossier_models.RealestateProperty.objects.filter(
            assignedrealestatepropertysemanticdocument__semantic_document__dossier=dossier
        )
        .annotate(
            semantic_document_uuid=F(
                "assignedrealestatepropertysemanticdocument__semantic_document__uuid"
            )
        )
        .all()
    )


# Currently not used as we use bulk get via get-real-estate-properties-dossier-assigned
@semantic_documents_router.get(
    "/realestate-properties/{semantic_document_uuid}/assigned",
    response=schemas.RealEstatePropertyEntity,
    url_name="get-real-estate-properties-assigned",
    description="Get the RealEstateProperty assigned to a semantic document.",
)
def get_assigned_realestate_properties_for_semantic_document(
    request, semantic_document_uuid: UUID
):
    # Get all RealEstateProperty assigned to this semantic document
    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)

    # We use this function to check if the user has access semantic document via dossier
    get_dossier_from_request_with_access_check(request, semantic_document.dossier.uuid)
    return dossier_models.RealestateProperty.objects.filter(
        assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
    ).first()


@semantic_documents_router.post(
    "/realestate-properties/{semantic_document_uuid}/assign",
    response={201: schemas.RealEstatePropertyEntity, 400: core_schema.Message},
    url_name="assign-real-estate-property",
)
def assign_realestate_property_to_semantic_document(
    request, semantic_document_uuid: UUID, payload: schemas.RealEstatePropertyKey
):
    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_document_uuid)
    assert_semantic_document_is_writable(semantic_document)

    # TODO: when we delete or move an entity, we need to update the semantic documents
    # last_entity_change_date

    # Assign a RealEstateProperty to a semantic document, not sure if this should be get_or_create,
    # rather than get_object_or_404 for realestate_property
    # i.e. whether we should create a new realestate_property if it doesn't exist
    # This would require changing the schema to allow to pass in the realestate_property data

    if not is_document_topic_property(semantic_document.document_category):
        return HTTPStatus.BAD_REQUEST, {
            "detail": "Document category is not a property type"
        }

    # We use this function to check if the user has access semantic document via dossier
    dossier = get_writable_dossier_from_request_with_access_check(
        request, semantic_document.dossier.uuid
    )

    realestate_property = get_object_or_404(
        dossier_models.RealestateProperty,
        dossier=dossier,
        key=payload.real_estate_property_key,
    )

    with transaction.atomic():
        # There should only be one AssignedRealEstatePropertySemanticDocument per semantic document
        AssignedRealEstatePropertySemanticDocument.objects.filter(
            semantic_document=semantic_document
        ).all().delete()

        AssignedRealEstatePropertySemanticDocument.objects.create(
            semantic_document=semantic_document, realestate_property=realestate_property
        )

    return 201, realestate_property


@semantic_documents_router.patch("/{dossier_uuid}/assign_collateral")
def assign_collateral(
    request, dossier_uuid: UUID, request_body: schemas.assignAutoCollateralRequestBody
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    with transaction.atomic():
        for doc in request_body.document:
            semantic_document = get_object_or_404(
                SemanticDocument, uuid=doc.semantic_document_uuid
            )

            assert_semantic_document_is_writable(semantic_document)

            collateral_number = doc.collateral_number
            if collateral_number:
                collateral = get_object_or_404(
                    Collateral,
                    collateral_number=collateral_number,
                    businesscase__uuid=dossier.bekbdossierproperties.business_case.uuid,
                    account__uuid=dossier.account.uuid,
                    business_partner__uuid=dossier.bekbdossierproperties.business_partner.uuid,
                )

                collateral_assignment: CollateralAssignment = (
                    CollateralAssignment.objects.filter(
                        account=dossier.account, semantic_document=semantic_document
                    ).first()
                )

                # Now find best-effort a matching realestate_property. In case the collateral is not of type
                # "Grundpfand" there will not be one. Else
                realestate_property = None
                creps: List[CollateralRealEstateProperty] = (
                    find_correct_real_estate_properties_for_collateral(
                        account=dossier.account, collateral=collateral
                    )
                )
                if len(creps) > 0:
                    crep = creps[0]
                    realestate_property = crep.realestate_property

                if collateral_assignment:
                    if (
                        collateral_assignment.collateral != collateral
                        or collateral_assignment.property != realestate_property
                    ):
                        collateral_assignment.collateral = collateral
                        collateral_assignment.property = realestate_property
                        collateral_assignment.save()
                else:
                    CollateralAssignment.objects.create(
                        account=dossier.account,
                        semantic_document=semantic_document,
                        collateral=collateral,
                        property=realestate_property,
                    )
            else:
                CollateralAssignment.objects.filter(
                    account=dossier.account, semantic_document=semantic_document
                ).delete()

    return {"message": "Collateral assignment has been updated successfully."}


@semantic_documents_router.get(
    "/{dossier_uuid}", response=List[dossier_schemas.SemanticDocumentFullApiData]
)
def get_semantic_documents(
    request, dossier_uuid: UUID, show_soft_deleted: bool = False
):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    return helpers_v2.prepare_semantic_documents_for_api_v2(
        dossier=dossier, show_soft_deleted=show_soft_deleted, include_annotations=False
    )


@semantic_documents_router.post(
    "/{dossier_uuid}",
    response={HTTPStatus.CREATED: schemas.CreatedSemanticDocument},
    url_name="create-semantic-document",
)
def create_semantic_documents(
    request, dossier_uuid: UUID, request_body: schemas.CreatedSemanticDocumentBody
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    request_dict = request_body.model_dump()
    semantic_document = semantic_document_helpers.create_unnamed_semantic_document(
        dossier, request_dict
    )

    return HTTPStatus.CREATED, {"uuid": semantic_document.uuid}


@semantic_documents_router.post(
    "/{dossier_uuid}/move_semantic_pages",
    response={HTTPStatus.OK: schemas.SavingResultWithMessage},
    url_name="move_semantic_pages",
)
def move_semantic_pages(
    request,
    dossier_uuid: UUID,
    request_body: List[dossier_schemas.RequestBodyResultDND],
):

    # Check if any target documents are read-only
    target_semantic_documents = SemanticDocument.objects.filter(
        uuid__in=[item.semantic_document_uuid for item in request_body]
    )
    for semantic_document in target_semantic_documents:
        assert_semantic_document_is_writable(semantic_document=semantic_document)

    # TODO: when we delete or move a semantic page, we need to update the semantic documents
    # last_page_change_date
    with transaction.atomic():
        dossier = get_writable_dossier_from_request_with_access_check(
            request, dossier_uuid
        )

        semantic_document_uuids = [x.semantic_document_uuid for x in request_body]

        semantic_document_map_semantic_pages = {}

        for semantic_document_uuid in semantic_document_uuids:
            semantic_document_map_semantic_pages[semantic_document_uuid] = list(
                SemanticPage.all_objects.filter(
                    semantic_document__uuid=semantic_document_uuid,
                    dossier=dossier,
                )
                .order_by("number", "deleted_at", "-updated_at")
                .all()
            )

        # As far as I can tell, source_semantic_pages and deleted_semantic_pages contain the
        # same information, just that source_semantic_pages has the information in a pydantic schema
        (
            source_semantic_pages,
            deleted_semantic_pages,
        ) = semantic_document_helpers.fill_deleted_semantic_pages(
            dossier=dossier, client_provided_state=request_body, hard_delete=True
        )

        dest_semantic_pages = semantic_document_helpers.save_result_dnd_to_db(
            client_provided_state=request_body,
            dossier=dossier,
            deleted_semantic_pages=deleted_semantic_pages,
        )

        for semantic_document_uuid in semantic_document_uuids:
            fix_soft_deleted_position_block_approach(
                semantic_document=SemanticDocument.objects.filter(
                    uuid=semantic_document_uuid
                ).first(),
                initial_pages=semantic_document_map_semantic_pages[
                    semantic_document_uuid
                ],
            )

        publish(
            create_semantic_pages_moved_event(
                request.auth.user, source_semantic_pages, dest_semantic_pages
            )
        )

        return HTTPStatus.OK, {"message": "Saved"}


@semantic_documents_router.post(
    "/{dossier_uuid}/update_title", response=schemas.SavingResultWithMessageAndUUID
)
def update_title(
    request, dossier_uuid: UUID, request_body: schemas.UpdateSemanticTitleSchema
):
    semantic_document = get_object_or_404(SemanticDocument, uuid=request_body.uuid)
    assert_semantic_document_is_writable(semantic_document)

    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    account = dossier.account
    user = request.auth.user

    request_data = request_body.model_dump()

    update_semantic_document_title(account, user, request_data, semantic_document)

    semantic_document.confidence_level = ConfidenceLevel.HIGH
    semantic_document.confidence_formatted = "100%"
    semantic_document.confidence_value = 100

    if (
        account.enable_custom_semantic_document_date
        and request_body.semantic_document_date
    ):
        default_date: datetime.date = compute_default_semantic_document_date(
            semantic_document
        )

        # We only store the custom date if it is different from the default date. Else we re-calculate it when needed.
        if default_date != request_body.semantic_document_date:
            semantic_document.custom_semantic_document_date = (
                request_body.semantic_document_date
            )
            logger.info(
                "save custom document date",
                semantic_document_uuid=semantic_document.uuid,
                custom_date=semantic_document.custom_semantic_document_date,
            )

    semantic_document.save()

    assigned_real_estate_properties = (
        AssignedRealEstatePropertySemanticDocument.objects.filter(
            semantic_document=semantic_document
        ).all()
    )

    # If we have changed the semantic document category
    # we need to check whether the assigned real estate properties are still valid
    # i.e. document category must be of a property type, to allow for assignment of
    # real estate properties
    if assigned_real_estate_properties and not is_document_topic_property(
        semantic_document.document_category
    ):
        assigned_real_estate_properties.delete()

    if not assigned_real_estate_properties and is_document_topic_property(
        semantic_document.document_category
    ):
        # Check to see if dossier has any real estate properties
        # if there is one (and only one), assign it to the semantic document
        valid_real_estate_properties = dossier_models.RealestateProperty.objects.filter(
            dossier=dossier
        ).all()

        if valid_real_estate_properties.count() == 1:
            AssignedRealEstatePropertySemanticDocument.objects.create(
                semantic_document=semantic_document,
                realestate_property=valid_real_estate_properties.first(),
            )

    return {
        "message": "saved",
        "uuid": semantic_document.uuid,
        "title": semantic_document.title,
    }


@semantic_documents_router.post(
    "/category/is_document_topic_property",
    response=bool,
    url_name="is_document_topic_property",
)
def check_is_document_topic_property(request, request_body: schemas.CategoryGet):
    request_data = request_body.model_dump()

    document_category = get_object_or_404(
        DocumentCategory,
        name=request_data.get("name"),
        id=request_data.get("id"),
        account=request.auth.account,
    )

    return is_document_topic_property(document_category)


@semantic_documents_router.post(
    "/{dossier_uuid}/undelete", response=schemas.SavingResultWithMessage
)
def undelete_semantic_document(
    request, dossier_uuid: UUID, body: schemas.UndeleteSemanticDocumentSchema
):
    semantic_document_helpers.semantic_restore_or_delete_with_access_check(
        dossier_uuid,
        request,
        SemanticDocument.all_objects,
        body.semantic_document_uuid,
        False,
    )

    return schemas.SavingResultWithMessage(message="undeleted")


@semantic_documents_router.post(
    "/{dossier_uuid}/move_semantic_pages_to_new_document",
    response=schemas.MoveSemanticPagesToNewDocumentResponseSchema,
    url_name="move-semantic-pages-to-new-document",
)
def move_semantic_pages_to_new_document(
    request, dossier_uuid: UUID, body: schemas.MoveSemanticPagesToNewDocumentSchema
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    source_semantic_pages_qs = SemanticPage.objects.filter(
        uuid__in=[semantic_page.uuid for semantic_page in body.semantic_pages]
    )

    assert_semantic_documents_are_writable(
        SemanticDocument.objects.filter(
            semantic_pages__in=source_semantic_pages_qs
        ).all()
    )

    return copy_or_move_semantic_pages_to_new_document(
        dossier=dossier, user=request.auth.user, body=body, move=True
    )


@semantic_documents_router.post(
    "/{dossier_uuid}/copy_semantic_pages_to_new_document",
    response=schemas.MoveSemanticPagesToNewDocumentResponseSchema,
    url_name="copy-semantic-pages-to-new-document",
)
def copy_semantic_pages_to_new_document(
    request, dossier_uuid: UUID, body: schemas.MoveSemanticPagesToNewDocumentSchema
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    return copy_or_move_semantic_pages_to_new_document(
        dossier=dossier, user=request.auth.user, body=body, move=False
    )


@semantic_documents_router.delete(
    "/{dossier_uuid}/{semantic_uuid}", response=schemas.SavingResultWithMessage
)
def delete_semantic_document(request, dossier_uuid: UUID, semantic_uuid: UUID):
    get_writable_dossier_from_request_with_access_check(request, dossier_uuid)
    semantic_document = get_object_or_404(SemanticDocument, uuid=semantic_uuid)
    assert_semantic_document_is_writable(semantic_document)

    semantic_document_helpers.semantic_restore_or_delete_with_access_check(
        dossier_uuid, request, SemanticDocument, semantic_uuid, True
    )
    return schemas.SavingResultWithMessage(message="deleted")


@semantic_documents_router.delete(
    "/{dossier_uuid}/semantic_page/{semantic_page_uuid}",
    url_name="delete-semantic-page",
    response=schemas.SavingResultWithMessage,
)
def delete_semantic_page(request, dossier_uuid: UUID, semantic_page_uuid: UUID):

    semantic_page = get_object_or_404(SemanticPage, uuid=semantic_page_uuid)

    assert_semantic_document_is_writable(semantic_page.semantic_document)

    semantic_document_helpers.semantic_restore_or_delete_with_access_check(
        dossier_uuid, request, SemanticPage, semantic_page_uuid, True
    )

    return schemas.SavingResultWithMessage(message="deleted")


@semantic_documents_router.patch(
    "/{dossier_uuid}/semantic_page/{semantic_page_uuid}/undelete",
    url_name="undelete-semantic-page",
    response=schemas.SavingResultWithMessage,
)
def undelete_semantic_page(request, dossier_uuid: UUID, semantic_page_uuid: UUID):
    semantic_document_helpers.semantic_restore_or_delete_with_access_check(
        dossier_uuid, request, SemanticPage.all_objects, semantic_page_uuid, False
    )

    return schemas.SavingResultWithMessage(message="undeleted")


@semantic_documents_router.patch(
    "/rotate/{dossier_uuid}/{semantic_page_uuid}",
    response=schemas.SavingResultWithMessage,
)
def rotate_file(request, dossier_uuid: UUID, semantic_page_uuid: UUID):
    dossier: Dossier = get_writable_dossier_from_request_with_access_check(
        request, dossier_uuid
    )
    semantic_page: SemanticPage = get_object_or_404(
        SemanticPage, uuid=semantic_page_uuid
    )
    assert_semantic_document_is_writable(semantic_page.semantic_document)

    if semantic_page.dossier != dossier:
        raise HttpError(
            403, "Semantic page does not belong to the dossier in the request"
        )

    semantic_page.rotation_angle = (
        semantic_page.get_new_value_for_rotation_angle_field()
    )
    semantic_page.save()

    return schemas.SavingResultWithMessage(message="rotated")


@semantic_documents_router.post(
    "/{dossier_uuid}/semantic-pages/title-suffix-recommendations", response=List[str]
)
def title_suffix_recommendations_from_pages(
    request,
    dossier_uuid: UUID,
    request_body: schemas.CategoryRecommendationsRequestBody,
):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_pages = SemanticPage.objects.select_related(
        "processed_page",
        "processed_page__processed_file",
        "processed_page__processed_file__extracted_file",
        "processed_page__processed_file__extracted_file__file",
    ).filter(dossier=dossier, uuid__in=request_body.semantic_pages_uuid)

    suffix_recommendation = []

    for page in semantic_pages:
        suffix_recommendation.append(
            page.processed_page.processed_file.extracted_file.file.name
        )

    most_commonon_original_file_names = Counter(suffix_recommendation).most_common(3)

    return [value for value, count in most_commonon_original_file_names]


@semantic_documents_router.post(
    "/{dossier_uuid}/semantic-pages/document-category-recommendations",
    response=List[dossier_schemas.DocumentCategoryTranslated],
)
def get_document_type_recommendations_from_pages(
    request,
    dossier_uuid: UUID,
    request_body: schemas.CategoryRecommendationsRequestBody,
):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_pages = SemanticPage.objects.select_related(
        "document_category", "semantic_document", "semantic_document__document_category"
    ).filter(dossier=dossier, uuid__in=request_body.semantic_pages_uuid)

    return [
        dossier_schemas.DocumentCategoryTranslated(
            name=document_category.name,
            id=document_category.id,
            translation=document_category.translated(dossier.lang),
        )
        for document_category in semantic_document_helpers.document_cat_recommendations_for_pages(
            semantic_pages, None
        )
    ]


@semantic_documents_router.post(
    "/{dossier_uuid}/semantic-pages/combine",
    response={HTTPStatus.CREATED: schemas.CreatedSemanticDocument},
    url_name="combine-semantic-pages",
    description="Combine semantic pages to the new semantic document, ignores real estate property assignment",
)
def combine_pages(
    request,
    dossier_uuid: UUID,
    request_body: schemas.CombinePagesRequestBody,
    photos_sorting: bool = False,
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    list_of_semantic_page_uuid = list(
        map(
            lambda semantic_page_from_request: semantic_page_from_request.uuid,
            request_body.applicable_pages,
        )
    )

    # Optimize by fetching semantic pages and their documents in a single query
    semantic_pages = (
        SemanticPage.objects.select_related("semantic_document")
        .filter(uuid__in=list_of_semantic_page_uuid)
        .prefetch_related("semantic_document__semantic_pages")
    )

    # Get unique semantic documents and check writability
    semantic_documents = {page.semantic_document for page in semantic_pages}
    assert_semantic_documents_are_writable(semantic_documents)

    with transaction.atomic():
        semantic_document = semantic_document_helpers.create_unnamed_semantic_document(
            dossier, request_body.model_dump()
        )

        # Soft delete semantic pages on semantic documents
        deleted = semantic_document_helpers.delete_semantic_pages(
            list_of_semantic_page_uuid, True
        )

        # Create new semantic pages
        created_semantic_pages = semantic_document_helpers.save_deleted_semantic_page(
            deleted, semantic_document
        )

        created_page_objects = (
            semantic_document_helpers.create_page_object_to_moved_semantic_page(
                request_body.applicable_pages, created_semantic_pages
            )
        )

        semantic_document_helpers.create_aggregated_page_objects(
            semantic_document, created_page_objects
        )

    dossier.photos_sorting = photos_sorting
    dossier.save()

    return HTTPStatus.CREATED, {"uuid": semantic_document.uuid}


@semantic_documents_router.post(
    "/{dossier_uuid}/semantic-pages/combine-pages-realestate-property",
    response={
        HTTPStatus.CREATED: List[schemas.CreatedSemanticDocument],
        HTTPStatus.OK: List[schemas.CreatedSemanticDocument],
    },
    url_name="combine-semantic-pages-real-estate-property",
    description="Combine semantic pages taking into account real estate property to the new semantic document",
)
def combine_pages_real_estate_property(
    request,
    dossier_uuid: UUID,
    request_body: schemas.CombinePagesRealEstateRequestBody,
):
    """
    Combine semantic pages from various semantic documents taking into account real estate property to the new semantic
    document.

    We first group the semantic documents by real estate property (if it exists), then sort using a sorting
    string (combination of semantic_document uuid, original filename and photo number).
    If photos_sorting is true, we sort the pages by photo type, then finally we combine pages within each group.

    @param request:
    @param dossier_uuid:
    @param request_body: CombinePagesRealEstateRequestBody
    @return: (201, List[schemas.CreatedSemanticDocument]) if documents were combined, else (200, List[])
    """
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    document_category = get_object_or_404(
        DocumentCategory,
        name=request_body.document_category_key,
        account=request.user.account,
    )

    COMBINE_PAGES_VALID_DOCUMENT_CATEGORIES: List[str] = [
        "TAX_DECLARATION",
        "TAX_CALCULATION",
        "TAX_LIST_FINANCIAL_ASSETS",
        "TAX_DEBT_INVENTORY",
        "TAX_BUDGET",
        "PROPERTY_PHOTOS",
        "PLAN_FLOOR",
    ]
    if document_category.name not in COMBINE_PAGES_VALID_DOCUMENT_CATEGORIES:
        raise HttpError(400, "Invalid document category for 'combine pages'")

    semantic_documents_grouped_by_real_estate_property: (
        schemas.PermittedCombinePagesCombinations
    ) = calculate_combine_pages_real_estate_properties(dossier, document_category)

    # Optimize by fetching all semantic documents in a single query
    semantic_document_uuids = set()
    for (
        combination_list
    ) in semantic_documents_grouped_by_real_estate_property.combinations.values():
        semantic_document_uuids.update(
            c.semantic_document_uuid for c in combination_list
        )

    semantic_documents = SemanticDocument.objects.filter(
        uuid__in=semantic_document_uuids
    ).select_related("document_category")

    # Check writability for all documents
    assert_semantic_documents_are_writable(semantic_documents)

    created_semantic_documents = []

    for (
        combination_key,
        combination_list,
    ) in semantic_documents_grouped_by_real_estate_property.combinations.items():
        semantic_document_uuids = [x.semantic_document_uuid for x in combination_list]

        semantic_pages_qs = SemanticPage.objects.filter(
            semantic_document__uuid__in=semantic_document_uuids
        ).prefetch_related(
            Prefetch(
                "semantic_page_page_objects",
                queryset=SemanticPagePageObject.objects.select_related("page_object"),
            ),
            "processed_page__processed_file",
        )

        if request_body.photos_sorting:
            semantic_pages_qs = semantic_pages_qs.annotate(
                keys=ArrayAgg(
                    Case(
                        When(
                            semantic_page_page_objects__page_object__key__key__in=[
                                "photo_building_exterior",
                                "photo_outdoor",
                                "photo_building_visualisation",
                                "photo_building_interior",
                            ],
                            then="semantic_page_page_objects__page_object__key__key",
                        ),
                        output_field=CharField(),
                    ),
                    distinct=True,  # Shorten the list by excluding duplicates
                    default=None,
                )
            )

            key_order = [
                "photo_building_exterior",
                "photo_outdoor",
                "photo_building_visualisation",
                "photo_building_interior",
            ]

            # Fetch all pages and initially sort them by the sorting string which is a combination of
            # semantic_document uuid, original filename and photo number
            semantic_pages = sorted(
                list(semantic_pages_qs), key=lambda page: page.sorting_string
            )

            custom_order = {c: i for i, c in enumerate(key_order)}

            def custom_sorted(keys: list):
                return sorted(
                    keys, key=lambda c: custom_order.get(c, len(custom_order))
                )

            def sort_key(item):
                return (
                    key_order.index(custom_sorted(item.keys)[0])
                    if item.keys and item.keys[0] in key_order
                    else float("inf")
                )

            # Perform the sort, sorting in place did not seem to work correctly
            # sorted should be a stable sort, so the initial order (sort by sorting string) is preserved
            semantic_pages = sorted(semantic_pages, key=sort_key)

        else:
            semantic_pages = sorted(
                list(semantic_pages_qs), key=lambda page: page.sorting_string
            )

        list_of_semantic_page_uuid = [str(x.uuid) for x in semantic_pages]

        semantic_pages_data: List[MoveSemanticPageSchema] = [
            MoveSemanticPageSchema(
                uuid=str(semantic_page.uuid),
                source_file_uuid=str(semantic_page.processed_page.processed_file.uuid),
                number=semantic_page.processed_page.number,
                page_objects=[
                    dossier_helpers.prepare_page_object_v2(page_obj)
                    for page_obj in semantic_page.semantic_page_page_objects.all()
                ],
            )
            for semantic_page in semantic_pages
        ]

        with transaction.atomic():
            new_semantic_document = (
                semantic_document_helpers.create_unnamed_semantic_document(
                    dossier, {"document_category_id": document_category.id}
                )
            )

            new_semantic_document.document_category = document_category
            new_semantic_document.save()

            if combination_key != COMBINE_PAGES_UNASSIGNED:
                real_estate_property = dossier_models.RealestateProperty.objects.filter(
                    dossier=dossier,
                    key=combination_key,
                )

                if real_estate_property.exists():
                    AssignedRealEstatePropertySemanticDocument.objects.create(
                        semantic_document=new_semantic_document,
                        realestate_property=real_estate_property.first(),
                    )

            deleted = semantic_document_helpers.delete_semantic_pages(
                list_of_semantic_page_uuid, True
            )
            created_semantic_pages = (
                semantic_document_helpers.save_deleted_semantic_page(
                    deleted, new_semantic_document
                )
            )

            created_page_objects = (
                semantic_document_helpers.create_page_object_to_moved_semantic_page(
                    semantic_pages_data, created_semantic_pages
                )
            )

            semantic_document_helpers.create_aggregated_page_objects(
                new_semantic_document, created_page_objects
            )

            created_semantic_documents.append({"uuid": new_semantic_document.uuid})

    dossier.photos_sorting = request_body.photos_sorting
    dossier.save()

    if len(created_semantic_documents) > 0:
        return HTTPStatus.CREATED, created_semantic_documents
    else:
        return HTTPStatus.OK, []


@semantic_documents_router.get(
    "/{dossier_uuid}/semantic-pages/check-permitted-combine-pages-real-estate-property/{document_category_key}",
    response={HTTPStatus.CREATED: schemas.PermittedCombinePagesCombinations},
    url_name="check-permitted-combine-semantic-pages-real-estate-property",
    description="Checks which property semantic pages can be combined into new semantic documents",
)
def check_permitted_combine_pages_real_estate_property(
    request,
    dossier_uuid: UUID,
    document_category_key: str,
):
    document_category = get_object_or_404(
        DocumentCategory,
        name=document_category_key,
        account=request.user.account,
    )

    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    return calculate_combine_pages_real_estate_properties(dossier, document_category)


@doccheck_router.get(
    "/case/{case_uuid}/doccheck_assignment",
    response={200: List[schemas.DocCheckAssignmentOut], 404: core_schema.Message},
    url_name="doccheck_assignment",
)
def get_doccheck_assignment(request, case_uuid: UUID):
    """Gets all document requirements and assigned documents according to the doccheck rules associated with the case.
    The returned requirements have a list of recommended document options to fulfill the requirement. But all the
    documents in the dossier the case is based on can be mapped to the requirements.
    """
    case = check_case_access(request, case_uuid)
    return 200, get_doccheck_document_assignments(case=case)


@doccheck_router.post(
    "/case/{case_uuid}/doccheck_assignment",
    response={201: schemas.CreatedDocCheckAssignment, 404: core_schema.Message},
    url_name="doccheck_assignment",
)
def add_doccheck_assignment(
    request, case_uuid: UUID, doccheck_assignment: schemas.DocCheckAssignmentCreate
):
    """Adds or updates a record to DocCheckAssignment, linking a document requirement to a list of semantic documents
    or a comment to bypass the assignment
    """
    case = check_case_access(request, case_uuid)
    completeness_rule = get_object_or_404(
        CompletenessRule,
        doc_check=case.doc_check,
        key=doccheck_assignment.completeness_rule_key,
    )
    fulfillment_type = (
        models.FulfillmentType.DOC_ASSIGNMENT
        if doccheck_assignment.fulfillment_type
        == schemas.FulfillmentType.DOC_ASSIGNMENT
        else models.FulfillmentType.BYPASSING
    )
    semantic_documents = list(
        SemanticDocument.objects.filter(
            uuid__in=doccheck_assignment.assigned_document_uuids
        )
    )

    added_doccheck_assignment = assign_semantic_documents_to_doccheck_rule(
        case=case,
        context_model_uuid=doccheck_assignment.context_model_uuid,
        completeness_rule=completeness_rule,
        fulfillment_type=fulfillment_type,
        semantic_documents=semantic_documents,
        comment=doccheck_assignment.comment,
    )

    return 201, added_doccheck_assignment


@doccheck_router.get(
    "/case/{case_uuid}/doccheck_assignment_automatically",
    response={200: List[schemas.DocCheckAssignmentOut], 404: core_schema.Message},
    url_name="doccheck_assignment_automatically",
)
def show_doccheck_assignment_automatically(request, case_uuid: UUID):
    """Shows the possible automatic changes in the DocCheckAssignment without executing them (only shows assignments
    for requirements that are not fulfilled so far and that have exactly one matching document per document option).
    """
    case = check_case_access(request, case_uuid)
    assignments = assign_semantic_documents_to_doccheck_rules_automatically(
        case=case, make_assignment=False
    )
    return 200, assignments


@doccheck_router.post(
    "/case/{case_uuid}/doccheck_assignment_automatically",
    response={201: List[schemas.DocCheckAssignmentOut], 404: core_schema.Message},
    url_name="doccheck_assignment_automatically",
)
def do_doccheck_assigment_automatically(request, case_uuid: UUID):
    """Executes the automatic changes in the DocCheckAssignment (only changes assignments for requirements that are
    not fulfilled so far and that have exactly one matching document per document option).
    """
    case = check_case_access(request, case_uuid)
    assignments = assign_semantic_documents_to_doccheck_rules_automatically(
        case=case, make_assignment=True
    )
    return 201, assignments
