from typing import List

from doccheck.models import Case
from doccheck.services import check_document_requirements
from dossier.models import <PERSON><PERSON><PERSON>
from semantic_document import schemas, models
from semantic_document.models import DocCheckAssignment, FulfillmentType
from semantic_document.schemas import RequirementStatus


def get_doccheck_document_assignments(
    case: Case,
) -> List[schemas.DocCheckAssignmentOut]:
    requirements = check_document_requirements(case=case)
    assignments = list(DocCheckAssignment.objects.filter(case=case).all())

    result = []
    for requirement in requirements:
        status = RequirementStatus.NOT_FULFILLED
        if requirement.rule_strictness == "optional":
            status = RequirementStatus.NOT_FULFILLED_OPTIONAL
        assignment: DocCheckAssignment = next(
            (
                assignment
                for assignment in assignments
                if assignment.context_model_uuid == requirement.context_model_uuid
                and assignment.completeness_rule.key == requirement.rule_key
            ),
            None,
        )

        # Now test if the status can be changed because the requirement is fulfilled
        if assignment:
            if assignment.fulfillment_type == FulfillmentType.DOC_ASSIGNMENT:
                if list(assignment.assigned_documents.all()):
                    status = RequirementStatus.FULFILLED_ASSIGNMENT
                # Caution: if type is DOC_ASSIGNMENT and only a comment is set, the requirement
                # is not fulfilled
            elif assignment.fulfillment_type == FulfillmentType.BYPASSING:
                if assignment.comment:
                    status = RequirementStatus.FULFILLED_COMMENT

        # Make DOC_ASSIGNMENT the default type
        fulfillment_type = (
            schemas.FulfillmentType.BYPASSING
            if assignment
            and assignment.fulfillment_type == models.FulfillmentType.BYPASSING
            else schemas.FulfillmentType.DOC_ASSIGNMENT
        )

        result.append(
            schemas.DocCheckAssignmentOut(
                case_uuid=case.uuid,
                context_model_uuid=requirement.context_model_uuid,
                rule_key=requirement.rule_key,
                rule_title_de=requirement.rule_title_de,
                rule_title_en=requirement.rule_title_en,
                rule_title_fr=requirement.rule_title_fr,
                rule_title_it=requirement.rule_title_it,
                rule_desc_de=requirement.rule_desc_de,
                rule_desc_en=requirement.rule_desc_en,
                rule_desc_fr=requirement.rule_desc_fr,
                rule_desc_it=requirement.rule_desc_it,
                rule_strictness=requirement.rule_strictness,
                rule_order=requirement.rule_order,
                document_options=[
                    schemas.DocumentOption(
                        uuid=option.uuid,
                        document_category=option.document_category,
                        max_doc_age_in_months=option.max_doc_age_in_months,
                    )
                    for option in requirement.document_options
                ],
                assigned_document_titles=(
                    sorted([doc.title for doc in assignment.assigned_documents.all()])
                    if assignment
                    else []
                ),
                fulfillment_type=fulfillment_type,
                comment=assignment.comment if assignment else None,
                requirement_status=status,
            )
        )
    return result


def is_doccheck_fulfilled(dossier: Dossier) -> bool:
    if dossier.doccheck_case:
        assignments = get_doccheck_document_assignments(dossier.doccheck_case)
        if next(
            (
                assignment
                for assignment in assignments
                if assignment.requirement_status == RequirementStatus.NOT_FULFILLED
            ),
            None,
        ):
            return False

    return True
