import uuid

from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import <PERSON><PERSON><PERSON>, Account, DocumentCategory
from semantic_document.models import SemanticDocument, SemanticPagePageObject
from semantic_document.services import (
    export_aggregate_page_objects_from_semantic_document,
)


def test_generate_aggregate_page_objects(db):
    # Honestly not a great test, as we are not testing internals of generated data
    # however this tests that we are able to correctly populate List[PageObjectFullApiData] without raising an error
    external_dossier_id = str(uuid.uuid4())
    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=Account.objects.get(key="default")
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=2,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    semantic_document: SemanticDocument = semantic_documents[0]

    aggregate_page_objects = export_aggregate_page_objects_from_semantic_document(
        semantic_document
    )

    assert len(aggregate_page_objects) == 10

    assert (
        len(aggregate_page_objects)
        == SemanticPagePageObject.objects.filter(
            semantic_page__semantic_document=semantic_document
        ).count()
    )


def test_generate_aggregate_page_objects_filter_key(db):
    # Test that if we have two page objects for the same semantic document with the same key, only
    # return one of them
    external_dossier_id = str(uuid.uuid4())
    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=Account.objects.get(key="default")
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=2,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    semantic_document: SemanticDocument = semantic_documents[0]

    semantic_page_page_object = SemanticPagePageObject.objects.filter(
        semantic_page__semantic_document=semantic_document
    ).all()

    # Set two page objects to have the same key
    page_object_1 = semantic_page_page_object[0].page_object

    page_object_2 = semantic_page_page_object[1].page_object

    page_object_2.key = page_object_1.key
    page_object_2.save()

    aggregate_page_objects = export_aggregate_page_objects_from_semantic_document(
        semantic_document
    )

    assert len(aggregate_page_objects) == 9

    assert (
        len(aggregate_page_objects)
        == SemanticPagePageObject.objects.filter(
            semantic_page__semantic_document=semantic_document
        ).count()
        - 1
    )
