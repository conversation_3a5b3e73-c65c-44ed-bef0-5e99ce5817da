from typing import List

import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.urls import reverse

import dossier.schemas as dossier_schemas
from dossier.models import Dossier, DocumentCategory
from core.authentication import AuthenticatedClient
from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.schemas import (
    CreatedSemanticDocumentBody,
    MoveSemanticPagesToNewDocumentSchema,
    MoveSemanticPageSchema,
    MoveSemanticPagesToNewDocumentResponseSchema,
)

pytestmark = pytest.mark.django_db

User: AbstractUser = get_user_model()


def get_semantic_document_schema_and_object_from_list_schema(
    semantic_documents: List[dossier_schemas.SemanticDocumentFullApiData],
    document_category_id: str,
):
    doc_schema: dossier_schemas.SemanticDocumentFullApiData = [
        x for x in semantic_documents if x.document_category.id == document_category_id
    ][0]

    doc_object = SemanticDocument.objects.get(uuid=doc_schema.uuid)

    return doc_schema, doc_object


@pytest.mark.django_db
def test_move_semantic_pages_move_single_page_240_into_210(
    testuser1_client: AuthenticatedClient,
):
    """
    Test moving a single page from a single page doc into another single page doc

    Note: we are passing data into both objects i.e.

    [
    { semantic_document_uuid = 123, data = {... 2 semantic pages}
    { semantic_document_uuid = 245, data = {.. 0 semantic pages}
    ]

    normally in prod, we sould be passing data = null, instead for empty docs

    This is just to test, undefined behaviour

    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_documents = dossier_data.semantic_documents

    doc_210_SemanticDocumentFullApiData, doc_210_object = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents, "210"
        )
    )

    assert doc_210_object.semantic_pages.count() == 1

    doc_210_object_sem_page = doc_210_object.semantic_pages.first()

    doc_240_SemanticDocumentFullApiData, doc_240_object = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents, "240"
        )
    )

    assert doc_240_object.semantic_pages.count() == 1

    doc_240_object_sem_page = doc_240_object.semantic_pages.first()

    # remove the semantic page from 240
    doc_240_sem_page_SemanticPageFullApiData: (
        dossier_schemas.SemanticPageFullApiData
    ) = doc_240_SemanticDocumentFullApiData.semantic_pages[0]

    doc_240_SemanticDocumentFullApiData.semantic_pages = []

    # append it to 210
    doc_210_SemanticDocumentFullApiData.semantic_pages.append(
        doc_240_sem_page_SemanticPageFullApiData
    )
    # And update the page numbering
    doc_210_SemanticDocumentFullApiData.semantic_pages[1].number = 1

    # Build new datastructure
    new_post_data = [
        dossier_schemas.RequestBodyResultDND(
            data=doc_210_SemanticDocumentFullApiData,
            semantic_document_uuid=doc_210_SemanticDocumentFullApiData.uuid,
        ),
        dossier_schemas.RequestBodyResultDND(
            data=doc_240_SemanticDocumentFullApiData,
            semantic_document_uuid=doc_240_SemanticDocumentFullApiData.uuid,
        ),
    ]

    # Doc 210 should have 2 pages, numbered zero and one
    assert new_post_data[0].data.semantic_pages[0].number == 0
    assert new_post_data[0].data.semantic_pages[1].number == 1

    #  doc 240 should have 0
    assert new_post_data[1].data.semantic_pages == []

    # Eh - this was annoying to figure out how to correctly serialize
    obj2 = dossier_schemas.RequestBodyResultDNDList.model_validate(new_post_data)

    result = testuser1_client.post(
        reverse("api:move_semantic_pages", kwargs={"dossier_uuid": dossier.uuid}),
        data=obj2.model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200

    doc_210_object.refresh_from_db()
    doc_240_object.refresh_from_db()

    assert doc_210_object.semantic_pages.count() == 2

    # Pages should be ordered from zero index
    # Test asserts fix for https://gitlab.com/hypodossier/document-universe/-/issues/677
    assert doc_210_object.semantic_pages.all().order_by("number")[0].number == 0
    assert doc_210_object.semantic_pages.all().order_by("number")[1].number == 1

    doc_210_object.semantic_pages.filter(
        uuid__in=[doc_240_object_sem_page.uuid, doc_210_object_sem_page.uuid]
    )

    assert doc_240_object.semantic_pages.count() == 0

    assert doc_240_object.deleted_at is None


@pytest.mark.django_db
def test_move_semantic_pages_move_single_page_240_into_210_null(
    testuser1_client: AuthenticatedClient,
):
    """
    # Same as above, but second field has data = null, which should soft delete the doc
    Test moving a single page from a single page doc into another single page doc

    Note: we are passing data into both objects i.e.

    [
    { semantic_document_uuid = 123, data = {... 2 semantic pages}
    { semantic_document_uuid = 245, data = null
    ]

    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_documents = dossier_data.semantic_documents

    doc_210_SemanticDocumentFullApiData, doc_210_object = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents, "210"
        )
    )

    assert doc_210_object.semantic_pages.count() == 1

    doc_210_object_sem_page = doc_210_object.semantic_pages.first()

    doc_240_SemanticDocumentFullApiData, doc_240_object = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents, "240"
        )
    )

    assert doc_240_object.semantic_pages.count() == 1

    doc_240_object_sem_page = doc_240_object.semantic_pages.first()

    # remove the semantic page from 240
    doc_240_sem_page_SemanticPageFullApiData: (
        dossier_schemas.SemanticPageFullApiData
    ) = doc_240_SemanticDocumentFullApiData.semantic_pages[0]

    doc_240_SemanticDocumentFullApiData.semantic_pages = []

    # append it to 210
    doc_210_SemanticDocumentFullApiData.semantic_pages.append(
        doc_240_sem_page_SemanticPageFullApiData
    )

    # Build new datastructure
    new_post_data = [
        dossier_schemas.RequestBodyResultDND(
            data=doc_210_SemanticDocumentFullApiData,
            semantic_document_uuid=doc_210_SemanticDocumentFullApiData.uuid,
        ),
        dossier_schemas.RequestBodyResultDND(
            data=None,
            semantic_document_uuid=doc_240_SemanticDocumentFullApiData.uuid,
        ),
    ]

    # Eh - this was annoying to figure out how to correctly serialize
    obj2 = dossier_schemas.RequestBodyResultDNDList.model_validate(new_post_data)

    result = testuser1_client.post(
        reverse("api:move_semantic_pages", kwargs={"dossier_uuid": dossier.uuid}),
        data=obj2.model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200

    doc_210_object.refresh_from_db()

    assert doc_210_object.semantic_pages.count() == 2

    doc_210_object.semantic_pages.filter(
        uuid__in=[doc_240_object_sem_page.uuid, doc_210_object_sem_page.uuid]
    )

    # Should be soft deleted
    with pytest.raises(SemanticDocument.DoesNotExist) as excinfo:
        doc_240_object.refresh_from_db()

    assert str(excinfo.value) == "SemanticDocument matching query does not exist."


@pytest.mark.django_db
def test_move_semantic_pages_move_pages_240_into_empty_doc(
    testuser1_client: AuthenticatedClient,
):
    """
    Move 2 pages from an existing document into a newly created empty one
    Ensure that link to processed page and processed file still is correct.

    Initially this test was planned as an example of a problem reproduction but it actually proves that the move
    works correctly.

    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    # Create an empty semantic document to copy pages into
    testuser1_client.post(
        reverse("api:create-semantic-document", kwargs={"dossier_uuid": dossier.uuid}),
        data=CreatedSemanticDocumentBody(
            document_category_id="311", document_category_name="TAX_CALCULATION"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert SemanticDocument.objects.get(dossier=dossier, document_category__id="311")

    response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_documents = dossier_data.semantic_documents

    doc_310_SemanticDocumentFullApiData, doc_310_object = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents, "310"
        )
    )

    assert doc_310_object.semantic_pages.count() == 13

    doc_311_SemanticDocumentFullApiData, doc_311_object = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents, "311"
        )
    )

    assert doc_311_object.semantic_pages.count() == 0

    # append first two pages from 310 to 311
    doc_311_SemanticDocumentFullApiData.semantic_pages = (
        doc_310_SemanticDocumentFullApiData.semantic_pages[:2]
    )

    # Update 310, so it has two less pages
    doc_310_SemanticDocumentFullApiData.semantic_pages = (
        doc_310_SemanticDocumentFullApiData.semantic_pages[2:]
    )

    # Note, we don't update page numbering - which is also what the API doesn't do (i.e. it leaves it alone)

    # Build new datastructure
    new_post_data = [
        dossier_schemas.RequestBodyResultDND(
            data=doc_310_SemanticDocumentFullApiData,
            semantic_document_uuid=doc_310_SemanticDocumentFullApiData.uuid,
        ),
        dossier_schemas.RequestBodyResultDND(
            data=doc_311_SemanticDocumentFullApiData,
            semantic_document_uuid=doc_311_SemanticDocumentFullApiData.uuid,
        ),
    ]

    obj2 = dossier_schemas.RequestBodyResultDNDList.model_validate(new_post_data)

    result = testuser1_client.post(
        reverse("api:move_semantic_pages", kwargs={"dossier_uuid": dossier.uuid}),
        data=obj2.model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200

    doc_310_object.refresh_from_db()
    doc_311_object.refresh_from_db()

    assert doc_310_object.semantic_pages.count() == 11
    assert doc_311_object.semantic_pages.count() == 2

    # After the move the old 3rd page is the new first page because the first 2 had been moved
    doc_310_new_first_page = doc_310_object.semantic_pages.get(number=0)
    doc_310_new_first_page.number = 0
    doc_310_new_first_page.processed_page.number = (
        2  # because first 2 pages have been moved away into 311
    )

    doc_311_object.semantic_pages.filter(
        uuid__in=[x.uuid for x in doc_311_SemanticDocumentFullApiData.semantic_pages]
    )

    # Now get the pages
    doc_311_page_1: SemanticPage = doc_311_object.semantic_pages.get(number=0)
    doc_311_page_2: SemanticPage = doc_311_object.semantic_pages.get(number=1)

    # validate the page numbers of the pages
    assert doc_311_page_1.number == 0
    assert doc_311_page_2.number == 1
    assert doc_311_page_1.processed_page.number == 0
    assert doc_311_page_2.processed_page.number == 1

    # The processed pages must be different but the linked processed document must be the same
    assert doc_311_page_1.processed_page != doc_311_page_2.processed_page
    assert (
        doc_311_page_1.processed_page.processed_file
        == doc_311_page_2.processed_page.processed_file
    )


@pytest.mark.django_db
def test_move_last_alive_semantic_page(
    testuser1_client: AuthenticatedClient,
):
    """
    1. Delete all semantic page except for the last one from a document
    2. Move the remaining page from the document into a new document
    3. Verify that the first document still exists but in the trashcan. This did not work correctly until Mai 2025
    """
    dossier = Dossier.objects.get(name="sales pitch mix with errors dossier")

    response = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )

    dossier_data = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response.content
    )

    semantic_documents = dossier_data.semantic_documents

    doc_310_SemanticDocumentFullApiData_v1, doc_310_object_v1 = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents, "310"
        )
    )
    assert doc_310_object_v1.semantic_pages.count() == 13

    # Step 1a: Delete the pages 0..11, only last page is not deleted
    for i in range(12):
        doc_310_first_page = doc_310_object_v1.semantic_pages.get(number=i)
        result = testuser1_client.delete(
            reverse(
                "api:delete-semantic-page",
                kwargs={
                    "dossier_uuid": dossier.uuid,
                    "semantic_page_uuid": doc_310_first_page.uuid,
                },
            ),
        )
        assert result.status_code == 200

    doc_310_last_non_deleted_page = doc_310_object_v1.semantic_pages.get(number=12)

    # Step 1b: verify that now we have 1 page left
    response_v2 = testuser1_client.get(
        reverse("api:get-dossier-data-v2", kwargs={"dossier_uuid": dossier.uuid})
    )
    dossier_data_v2 = dossier_schemas.SemanticDossierSimple.model_validate_json(
        response_v2.content
    )
    semantic_documents_v2 = dossier_data_v2.semantic_documents
    doc_310_SemanticDocumentFullApiData_v2, doc_310_object_v2 = (
        get_semantic_document_schema_and_object_from_list_schema(
            semantic_documents_v2, "310"
        )
    )
    assert doc_310_object_v2.semantic_pages.count() == 1

    # Step 2: move all non-deleted pages of 310 to new document
    document_category = DocumentCategory.objects.get(
        name="FINANCING_MISC", account=dossier.account
    )

    page_request = MoveSemanticPageSchema(
        uuid=str(doc_310_last_non_deleted_page.uuid),
        source_file_uuid=str(
            doc_310_last_non_deleted_page.processed_page.processed_file.uuid
        ),
        number=12,
        page_objects=[],
    )

    request = MoveSemanticPagesToNewDocumentSchema(
        semantic_pages=[page_request],
        document_category_id=document_category.id,
        document_category_name=document_category.name,
    )

    result = testuser1_client.post(
        reverse(
            "api:move-semantic-pages-to-new-document",
            kwargs={"dossier_uuid": dossier.uuid},
        ),
        data=request.model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200

    response = MoveSemanticPagesToNewDocumentResponseSchema.model_validate_json(
        result.content
    )

    new_semantic_document = SemanticDocument.objects.get(
        uuid=response.semantic_document_uuid
    )

    assert new_semantic_document.semantic_pages.count() == 1
    assert new_semantic_document.semantic_pages.first().number == 0

    # Step 3: make sure the source document still exists as a soft-deleted document
    # This document should exist but with the old implementation the semantic document and the soft deleted pages got wrongly hard-deleted
    doc_310_object_v2 = SemanticDocument.all_objects.get(uuid=doc_310_object_v1.uuid)

    # Check that no alive pages exist
    assert doc_310_object_v2.semantic_pages.alive().count() == 0
    assert (
        SemanticPage.all_objects.alive()
        .filter(semantic_document=doc_310_object_v2)
        .count()
        == 0
    )  # Same check with different code

    # Check that all deleted pages still exist
    num_deleted_pages = (
        SemanticPage.all_objects.dead()
        .filter(semantic_document=doc_310_object_v2)
        .count()
    )
    assert num_deleted_pages == 12
