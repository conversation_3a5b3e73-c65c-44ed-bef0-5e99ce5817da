[{"title": "245 Strafregistera<PERSON><PERSON><PERSON><PERSON> 2020-09-01", "semantic_document_uuid": "a7e7b2da-a760-4ff9-851f-52e9c75ecdd5", "data": {"uuid": "a7e7b2da-a760-4ff9-851f-52e9c75ecdd5", "status_deleted": false, "updated_at": "2023-02-15T13:54:15.508183+00:00", "access_mode": "read_write", "semantic_pages": [{"lang": "de", "uuid": "95d02581-940b-4b93-8d09-8a089bd57b12", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 1, "rotation_angle": 0, "number": 1, "page_category": {"id": "120", "name": "TAX_DECLARATION_PAGE_INCOME", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "99%", "value": 0.*********, "level": "high"}, "confidence": 0.*********, "page_objects": [{"uuid": "e55ebd1e-3354-43ec-8fbf-825530f71bba", "key": "p2_income_employed_main", "title": "P2 Einkommen Haupterwerb", "titles": {"de": "P2 Einkommen Haupterwerb", "en": "P2 Main Income", "fr": "P2 Revenu net dépendante", "it": "[P2 Main Income]"}, "visible": true, "value": "CHF 128'991", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 360, "left": 2640, "right": 2796, "bottom": 427}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "63890512-dce5-4072-bbe5-54a67036feb8", "key": "fh_landwirtschaft", "title": "Landwirtschaft", "titles": {"de": "Landwirtschaft", "en": "Landwirtschaft", "fr": "Landwirtschaft", "it": "Landwirtschaft"}, "visible": false, "value": "{\"value\": \"Landwirtschaft\", \"value_found\": \"Landwirtschaft\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\\n2: Inklusive Ertr\\u00e4ge aus                 2.1  Haupterwerb           Ehemann / Einzelperson / P1                     Hilfsblatt   120\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 719, "left": 1343, "right": 1605, "bottom": 775}, "page_number": 1, "confidence": 0.04614436998963356, "confidence_summary": {"value_formatted": "5%", "value": 0.04614436998963356, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7ef6e61c-a037-4531-b58b-6aed24336d93", "key": "income_real_estate_net_primary", "title": "Primärliegenschaft Ertrag netto", "titles": {"de": "Primärliegenschaft Ertrag netto", "en": "Primary Self used Real Estate Income net", "fr": "[Primary Self used Real Estate Income net]", "it": "[Primary Self used Real Estate Income net]"}, "visible": true, "value": "CHF 20'560", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3588, "left": 2662, "right": 2802, "bottom": 3642}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d18b2c4d-33aa-45e9-a7ba-8bc55a04c245", "key": "income_portfolio", "title": "Wertschriftenerträge", "titles": {"de": "Wertschriftenerträge", "en": "Income Portfolio", "fr": "Revenue mobilier", "it": "[Income Portfolio]"}, "visible": true, "value": "CHF 15", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2341, "left": 2753, "right": 2803, "bottom": 2408}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "5ddb98b4-41d9-43f1-906f-42eb991010e4", "key": "p2_income_child_benefits", "title": "P2 Einkünfte Kinder- und Familienzulagen", "titles": {"de": "P2 Einkünfte Kinder- und Familienzulagen", "en": "P2 Income Child Benefits", "fr": "[P2 Income Child Benefits]", "it": "[P2 Income Child Benefits]"}, "visible": true, "value": "CHF 4'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2190, "left": 2686, "right": 2802, "bottom": 2244}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d95ffed1-f4c1-4889-89f4-c84e8045bdc0", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1194, "right": 1387, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "9bbbda43-492e-4ff0-aa16-14ad22518c3f", "key": "property_imputed_rental_value", "title": "Liegenschaft Eigenmietwert", "titles": {"de": "Liegenschaft Eigenmietwert", "en": "Imputed Rental Value", "fr": "Valeur locative", "it": "Valore locativo"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3111, "left": 1971, "right": 2110, "bottom": 3165}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7f885fe2-f238-4ff3-810d-d1dbdbeb1059", "key": "income_real_estate_gross", "title": "Liegenschaftenertrag brutto", "titles": {"de": "Liegenschaftenertrag brutto", "en": "Income Real Estate gross", "fr": "Revenu brut immobilier", "it": "[Income Real Estate gross]"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3305, "left": 1971, "right": 2110, "bottom": 3358}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "0de8e719-0480-479d-8d61-92a57279f370", "key": "fh_alimente", "title": "Alimente", "titles": {"de": "Alimente", "en": "Alimente", "fr": "Alimente", "it": "Alimente"}, "visible": true, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"Alimente\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.1 und 5.2\\n5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2566, "left": 366, "right": 497, "bottom": 2620}, "page_number": 1, "confidence": 0.16822287440299988, "confidence_summary": {"value_formatted": "17%", "value": 0.16822287440299988, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "635cd23c-1566-46dc-a916-9b4943833a7f", "key": "p1_income_employed_side", "title": "P1 Einkommen Nebenverdienst", "titles": {"de": "P1 Einkommen Nebenverdienst", "en": "P1 Additional Income", "fr": "[P1 Additional Income]", "it": "[P1 Additional Income]"}, "visible": true, "value": "CHF 7'502", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 459, "left": 2688, "right": 2802, "bottom": 514}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fb1d780e-24aa-4f87-8c35-ff266fc37331", "key": "property_maintenance_cost", "title": "Liegenschaft Unterhaltskosten", "titles": {"de": "Liegenschaft Unterhaltskosten", "en": "Maintenance Cost", "fr": "Charges et frais d'entretien d'immeuble", "it": "[Maintenance Cost]"}, "visible": true, "value": "CHF 5'140", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3397, "left": 1996, "right": 2110, "bottom": 3451}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e6cf3875-f643-45ce-ba5f-f9452fd8de7d", "key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "visible": true, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3765, "left": 2640, "right": 2802, "bottom": 3833}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "ee35de32-2f59-4f9d-b046-db06bb5aee29", "key": "fh_arbeitslos", "title": "arbeitslos", "titles": {"de": "arbeitslos", "en": "arbeitslos", "fr": "arbeitslos", "it": "arbeitslos"}, "visible": true, "value": "{\"value\": \"arbeitslos\", \"value_found\": \"Arbeitslos\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"3.3  Erwerbsausfallentsch\\u00e4digungen aus Arbeitslosenversicherung\\n3.3: Direkt ausbezahlte Erwerbs-\\nausfallentsch\\u00e4digungen.                   Ehemann / Einzelperson / P1                                    Bescheinigung 140\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1801, "left": 1441, "right": 1624, "bottom": 1857}, "page_number": 1, "confidence": 0.21327336132526398, "confidence_summary": {"value_formatted": "21%", "value": 0.21327336132526398, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e1569f2a-04aa-4eef-9f58-aa42728c36cb", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "05d008e8-9d82-4c03-8bec-ff45aebf447f", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fdddaa16-fcc1-401e-b4f2-a2357e073558", "key": "fh_gewerbe", "title": "Gewerbe", "titles": {"de": "Gewerbe", "en": "Gewerbe", "fr": "Gewerbe", "it": "Gewerbe"}, "visible": false, "value": "{\"value\": \"<PERSON><PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON>erbe\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Verwaltungsrats- und Vorstands-\\n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \\n2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 652, "left": 1731, "right": 1888, "bottom": 708}, "page_number": 1, "confidence": 0.07572177052497864, "confidence_summary": {"value_formatted": "8%", "value": 0.07572177052497864, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "17c44624-2bec-455a-8d98-2aa121b31d63", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1449, "right": 1593, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "2f642a4f-2c86-4b07-8bfc-80d7ee9cabc3", "key": "p1_income_employed_main", "title": "P1 Einkommen Haupterwerb", "titles": {"de": "P1 Einkommen Haupterwerb", "en": "P1 Main Income", "fr": "P1 Revenu net dépendante", "it": "[P1 Main Income]"}, "visible": true, "value": "CHF 22'506", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 280, "left": 2662, "right": 2802, "bottom": 336}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "3a5d2ebc-fd0a-4208-9bde-4640c0784d07", "key": "p2_income_eo", "title": "P2 Einkünfte Erwerbsausfallentschädigung", "titles": {"de": "P2 Einkünfte Erwerbsausfallentschädigung", "en": "P2 Income EO", "fr": "[P2 Income EO]", "it": "[P2 Income EO]"}, "visible": true, "value": "CHF 4'388", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1939, "left": 2686, "right": 2802, "bottom": 2006}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}], "document_category": {"id": "245", "name": "CRIMINAL_RECORDS", "de": "Strafregisterauszug", "en": "Extract of the Criminal Record", "fr": "Extrait du casier judiciaire", "it": "Estratto del casellario giudiziale", "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "aggregated_objects": [{"uuid": "d976d253-050c-4e2f-9a77-5a082ba7acd7", "key": "status", "title": "Status", "titles": {"de": "Status", "en": "Status", "fr": "État", "it": "Stato"}, "visible": true, "value": "<PERSON><PERSON>", "type": "OBJECT", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 0, "left": 0, "right": 2982, "bottom": 4215}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "dcdc3e1c-2e1c-4569-980d-5b6150c87e2d", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 829, "ref_height": 1171, "top": 31, "left": 47, "right": 280, "bottom": 95}, "page_number": 0, "confidence": 0.94, "confidence_summary": {"value_formatted": "94%", "value": 0.94, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "46b4eb5f-b1bf-4038-84a6-abb5c5fc2204", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 829, "ref_height": 1171, "top": 191, "left": 52, "right": 280, "bottom": 247}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "838be00b-9470-457b-96ef-379132e2016c", "key": "rest_misc_trash", "title": "rest_misc_trash", "titles": {"de": "rest_misc_trash", "en": "rest_misc_trash", "fr": "rest_misc_trash", "it": "rest_misc_trash"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 829, "ref_height": 1171, "top": 211, "left": 451, "right": 587, "bottom": 246}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "02a1a681-c250-4a9b-9543-a3ab31715536", "key": "address_block", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Address", "fr": "[Address]", "it": "[Address]"}, "visible": true, "value": "<PERSON>sdorferstrasse 578\n8055 Zürich", "type": "ADDRESS_BLOCK", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 960, "left": 1630, "right": 2305, "bottom": 1169}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "3ad4e7d4-5fca-494f-b2d5-64f33b82ada0", "key": "lastname", "title": "Nachname", "titles": {"de": "Nachname", "en": "Lastname", "fr": "[Lastname]", "it": "[Lastname]"}, "visible": true, "value": "<PERSON><PERSON><PERSON>", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2069, "left": 1387, "right": 1635, "bottom": 2125}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "e874f42e-f010-4d86-bae1-2d9125db68ee", "key": "firstname", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Firstname", "fr": "[Firstname]", "it": "[Firstname]"}, "visible": true, "value": "<PERSON>", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2134, "left": 1389, "right": 1801, "bottom": 2190}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "40d76e86-95a6-44d3-bbb6-5c5fc3328342", "key": "date_of_birth", "title": "Geburtsdatum", "titles": {"de": "Geburtsdatum", "en": "Date of Birth", "fr": "[Date of Birth]", "it": "[Date of Birth]"}, "visible": true, "value": "04.09.1977", "type": "DATE", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2200, "left": 1389, "right": 1666, "bottom": 2256}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "2109d48d-cfbe-4dcf-8bf9-1f51a1280176", "key": "native_place", "title": "Geburtsort", "titles": {"de": "Geburtsort", "en": "Native Place", "fr": "[Native Place]", "it": "[Native Place]"}, "visible": true, "value": "Ringgenberg BE", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2266, "left": 1389, "right": 1793, "bottom": 2322}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "4f8f8da6-7bd9-4805-b741-bed99afe90a5", "key": "nationality", "title": "Nationalität", "titles": {"de": "Nationalität", "en": "Nationality", "fr": "Nationalité", "it": "[Nationality]"}, "visible": true, "value": "CH", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2331, "left": 1388, "right": 1466, "bottom": 2387}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "3fc9a230-837a-49ff-a6ec-f4694dd4f08a", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "01.09.2020", "type": "DATE", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 3977, "left": 2314, "right": 2708, "bottom": 4042}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}], "filename": "245 Strafregistera<PERSON><PERSON><PERSON><PERSON> 2020-09-01.pdf", "suffix": "<PERSON><PERSON><PERSON> 2020-09-01", "title_elements": null, "confidence_summary": {"value_formatted": "99%", "value": 0.*********, "level": "high"}, "confidence_info": null, "confidence": 0.*********, "formatted_title": "245 Strafregistera<PERSON><PERSON><PERSON><PERSON> 2020-09-01", "document_category_translated": "Strafregisterauszug", "title_custom": "245 Strafregistera<PERSON><PERSON><PERSON><PERSON> 2020-09-01"}}, {"title": "310 Steuererklärung Mustermann Max ZH 2019", "semantic_document_uuid": "7bf53b35-0fff-4c5f-8988-e23896175836", "data": {"uuid": "7bf53b35-0fff-4c5f-8988-e23896175836", "status_deleted": false, "updated_at": "2023-02-15T13:54:15.508183+00:00", "access_mode": "read_write", "semantic_pages": [{"lang": "de", "uuid": "d36306e2-09f9-478d-a1db-b7da859e1857", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 0, "rotation_angle": 0, "number": 0, "page_category": {"id": "110", "name": "TAX_DECLARATION_PAGE_PERSONAL_DATA", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97671, "level": "high"}, "confidence": 0.97671, "page_objects": [{"uuid": "2478f538-1f7a-493b-842c-59a6573fb1e4", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2855, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "f90a75e5-971b-4f45-9ab1-6cc2b2b5ed77", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 633, "left": 2047, "right": 2407, "bottom": 703}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "415f515f-1e2a-46d7-ba15-fba595794640", "key": "zip", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "Zip Code", "fr": "[Zip Code]", "it": "[Zip Code]"}, "visible": true, "value": "8055", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 815, "right": 920, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "807a89ba-f0b7-4217-8669-ec3ff897cff7", "key": "p2_firstname", "title": "P2 Vorname", "titles": {"de": "P2 Vorname", "en": "P2 Lastname", "fr": "P2 Prénom", "it": "[P2 Lastname]"}, "visible": true, "value": "<PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1831, "left": 2043, "right": 2156, "bottom": 1894}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "f119af3b-578c-4e48-afd8-a99d9b860337", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": false, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Wegleitung verwendeten Begriffe     Zivilstand      verheiratet                                   Vorname    Maria\\nwie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder <PERSON>he,       Beruf             Informatiker                                Beruf          Solution Consultant\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1897, "left": 388, "right": 520, "bottom": 1960}, "page_number": 0, "confidence": 0.08460009843111038, "confidence_summary": {"value_formatted": "8%", "value": 0.08460009843111038, "level": "low"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "0c60c037-71a9-4c74-8f6b-3842b8d9443d", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"wie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder Ehe,       Beruf             Informatiker                                Beruf          Solution Consultant\\nEhegatten, Ehemann und Ehe-     Arbeitgeber \\nBeispielfirma AG                          Arbeitgeber   ServiceFirma\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2569, "left": 2687, "right": 2815, "bottom": 2615}, "page_number": 0, "confidence": 0.17452068626880646, "confidence_summary": {"value_formatted": "17%", "value": 0.17452068626880646, "level": "low"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "0e3ebe5b-0ee6-4c61-a538-dc5349cd62a5", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2387, "bottom": 414}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "4ac478f2-a892-4f77-8d5a-71c6dd31d84b", "key": "address_block", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Address", "fr": "[Address]", "it": "[Address]"}, "visible": true, "value": "<PERSON><PERSON><PERSON> Maria\nBe<PERSON>pielstrasse 42\n8055 Zürich", "type": "ADDRESS_BLOCK", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 781, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "fa180a62-d5cb-42c2-9917-e7b88093af74", "key": "p1_profession", "title": "P1 Beruf", "titles": {"de": "P1 Beruf", "en": "P1 Profession", "fr": "P1 Profession", "it": "[P1 Profession]"}, "visible": true, "value": "Informatiker", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 966, "right": 1207, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "bfab01a1-04ed-4682-b2a4-f2062153aff4", "key": "p1_employer_location", "title": "P1 Arbeitsort", "titles": {"de": "P1 Arbeitsort", "en": "P1 Place of work", "fr": "P1 Lieu de travail", "it": "[P1 Place of work]"}, "visible": true, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2074, "left": 963, "right": 1090, "bottom": 2137}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "93f038bd-c4ee-460a-8aad-97856dde2fe1", "key": "phone_secondary", "title": "Telefonnummer (2. Prio)", "titles": {"de": "Telefonnummer (2. Prio)", "en": "Phone number (2nd Priority)", "fr": "[Phone number (2nd Priority)]", "it": "[Phone number (2nd Priority)]"}, "visible": true, "value": "Telefon G       0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 1769, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "eaf11c28-ef77-48ec-87c5-5affedea989f", "key": "p1_fullname", "title": "P1 Name", "titles": {"de": "P1 Name", "en": "P1 Name", "fr": "P1 Nom", "it": "[P1 Name]"}, "visible": true, "value": "<PERSON><PERSON><PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 723, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "a17e229f-f073-4c7f-8b28-538e7a172b47", "key": "p2_profession", "title": "P2 Beruf", "titles": {"de": "P2 Beruf", "en": "P2 Profession", "fr": "P2 Profession", "it": "[P2 Profession]"}, "visible": true, "value": "Solution Consultant", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 2041, "right": 2435, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "4ede4fc2-ef15-49a7-93f8-cab7f7e5376d", "key": "p1_marital_status", "title": "P1 Zivilstand", "titles": {"de": "P1 Zivilstand", "en": "P1 Civil Status", "fr": "P1 Etat civil", "it": "[P1 Civil Status]"}, "visible": true, "value": "verheiratet", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1834, "left": 963, "right": 1181, "bottom": 1897}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "5f56f43a-f2d8-4385-ad6e-d641cc390d88", "key": "street", "title": "Strasse", "titles": {"de": "Strasse", "en": "Street", "fr": "[Street]", "it": "[Street]"}, "visible": true, "value": "Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 848, "left": 816, "right": 1183, "bottom": 911}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "22948305-7c3c-44d4-82c2-12747ee47434", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3799, "left": 139, "right": 2828, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "5a8b7d0b-09eb-4af6-8bb8-8a7e71000aca", "key": "p1_employer", "title": "P1 Arbeitgeber", "titles": {"de": "P1 Arbeitgeber", "en": "P1 Employer", "fr": "P1 Employeur", "it": "[P1 Employer]"}, "visible": true, "value": "Beispielfirma AG", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 965, "right": 1300, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "322f151d-0de5-40d2-b556-00039dc622f5", "key": "p2_phone_primary", "title": "P2 Telefonnummer", "titles": {"de": "P2 Telefonnummer", "en": "P2 Phone number", "fr": "P2 Téléphone", "it": "[P2 Phone number]"}, "visible": true, "value": "0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 2041, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "e4fa7ddb-dcf1-4641-801f-932dc4a5ffe6", "key": "phone_primary", "title": "Telefonnummer", "titles": {"de": "Telefonnummer", "en": "Phone number", "fr": "[Phone number]", "it": "[Phone number]"}, "visible": true, "value": "0763331234      P 07633", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2127, "left": 964, "right": 1539, "bottom": 2197}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "000e0a67-971b-4a32-ac6c-e35cd3adc9d6", "key": "section_children", "title": "Kinder", "titles": {"de": "Kinder", "en": "Children", "fr": "<PERSON><PERSON><PERSON>", "it": "[Children]"}, "visible": true, "value": "<PERSON>", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2534, "left": 137, "right": 572, "bottom": 2656}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "4f8b6b9b-8eaa-4fa0-8640-d66e88921667", "key": "city", "title": "Ort", "titles": {"de": "Ort", "en": "City", "fr": "[City]", "it": "[City]"}, "visible": true, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 925, "right": 1054, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "ab665111-edc5-49c7-acfd-88c0fd47bb04", "key": "p2_employer", "title": "P2 Arbeitgeber", "titles": {"de": "P2 Arbeitgeber", "en": "P2 Employer", "fr": "P2 Employeur", "it": "[P2 Employer]"}, "visible": true, "value": "ServiceFirma", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 2041, "right": 2308, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "caa095ca-b1d6-4471-a0a3-d8782498fd87", "key": "p2_date_of_birth", "title": "P2 Geburtsdatum", "titles": {"de": "P2 Geburtsdatum", "en": "P2 Date of Birth", "fr": "P2 Date de naissance", "it": "[P2 Date of Birth]"}, "visible": true, "value": "21.10.1976", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 2040, "right": 2267, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "87f369a3-b3c5-4917-a6a9-0639e2145831", "key": "p1_date_of_birth", "title": "P1 Geburtsdatum", "titles": {"de": "P1 Geburtsdatum", "en": "P1 Date of Birth", "fr": "P1 Date de naissance", "it": "[P1 Date of Birth]"}, "visible": true, "value": "04.09.1967", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 964, "right": 1191, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 2, "rotation_angle": 0, "number": 2, "page_category": {"id": "130", "name": "TAX_DECLARATION_PAGE_DEDUCTIONS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "99%", "value": 0.*********, "level": "high"}, "confidence": 0.*********, "page_objects": [{"uuid": "f5a1e024-5dd8-4a3f-b670-868ecdbee789", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "fe225db9-d5b6-4d53-820f-723686d20435", "key": "income_taxable_local", "title": "Steuerbares Einkommen im Kanton", "titles": {"de": "Steuerbares Einkommen im Kanton", "en": "Taxable Income in Canton", "fr": "[Taxable Income in Canton]", "it": "[Taxable Income in Canton]"}, "visible": true, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3757, "left": 2091, "right": 2230, "bottom": 3832}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "e09de8d0-e35c-4a7e-84c9-3cc92b8432cd", "key": "income_taxable_global", "title": "Steuerbares Einkommen gesamt", "titles": {"de": "Steuerbares Einkommen gesamt", "en": "Taxable Income total", "fr": "[Taxable Income total]", "it": "[Taxable Income total]"}, "visible": true, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3414, "left": 2091, "right": 2230, "bottom": 3488}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "ed3be4c7-233e-49de-bef8-bae6c5ae99c8", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "73fc4a8e-dafc-4214-a015-c03adb03bc6d", "key": "p1_contribution_pillar_3a", "title": "P1 Beiträge Säule 3a", "titles": {"de": "P1 Beiträge Säule 3a", "en": "P1 Contribution Pillar 3a", "fr": "[P1 Contribution Pillar 3a]", "it": "[P1 Contribution Pillar 3a]"}, "visible": true, "value": "CHF 3'350", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1026, "left": 2116, "right": 2230, "bottom": 1082}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "0dc74435-eedf-41c5-bc3b-e75f15136a50", "key": "insurance_premiums_and_interest_on_savings_accounts", "title": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "titles": {"de": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "en": "Insurance Premiums and Interest on Savings Accounts", "fr": "[Insurance Premiums and Interest on Savings Accounts]", "it": "[Insurance Premiums and Interest on Savings Accounts]"}, "visible": true, "value": "CHF 7'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1229, "left": 2116, "right": 2230, "bottom": 1282}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "1ad1a532-9055-4521-a100-7c02536dcd7c", "key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "visible": true, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 470, "left": 2093, "right": 2230, "bottom": 545}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "1ace674e-f162-4739-8f33-7b75f8604fa9", "key": "expense_children_daycare", "title": "Fremdbetreuung von Kindern", "titles": {"de": "Fremdbetreuung von Kindern", "en": "Expenses Daycare Children", "fr": "[Expenses Daycare Children]", "it": "[Expenses Daycare Children]"}, "visible": true, "value": "CHF 19'248", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1819, "left": 2093, "right": 2230, "bottom": 1894}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "3f049066-d109-443d-999f-59d78ff558e7", "key": "p2_contribution_pillar_3a", "title": "P2 Beiträge Säule 3a", "titles": {"de": "P2 Beiträge Säule 3a", "en": "P2 Contribution Pillar 3a", "fr": "[P2 Contribution Pillar 3a]", "it": "[P2 Contribution Pillar 3a]"}, "visible": true, "value": "CHF 6'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1117, "left": 2115, "right": 2230, "bottom": 1173}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "ba2846a1-d970-4f69-a1bf-21678dbde526", "key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "visible": true, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2345, "left": 2068, "right": 2230, "bottom": 2419}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "b9957e20-5a34-4b74-aeed-b68fe0a93ca2", "key": "p1_expense_employment", "title": "P1 Berufskosten", "titles": {"de": "P1 Berufskosten", "en": "P1 Employment Expenses", "fr": "P1 Déductions liées a l'activite dependante", "it": "[P1 Employment Expenses]"}, "visible": true, "value": "CHF 7'901", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 295, "left": 2116, "right": 2224, "bottom": 351}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "1b3be027-2247-44bf-8faa-2db9b88e2d44", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 966, "right": 1107, "bottom": 663}, "page_number": 2, "confidence": 0.19417862594127655, "confidence_summary": {"value_formatted": "19%", "value": 0.19417862594127655, "level": "low"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "8a5bd46c-1e7f-468d-a94e-b32ace490386", "key": "income_net_total", "title": "Total der Einkünfte (netto)", "titles": {"de": "Total der Einkünfte (netto)", "en": "Total Income net", "fr": "[Total Income net]", "it": "[Total Income net]"}, "visible": true, "value": "CHF 117'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2536, "left": 2068, "right": 2230, "bottom": 2610}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "3d6d0e10-24f8-49b7-92b5-0aa411b905e5", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 655, "right": 842, "bottom": 663}, "page_number": 2, "confidence": 0.19417862594127655, "confidence_summary": {"value_formatted": "19%", "value": 0.19417862594127655, "level": "low"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "22003cf5-31d0-4c6c-9b5a-a9be36d97e90", "key": "deductions_total", "title": "Total der Abzüge", "titles": {"de": "Total der Abzüge", "en": "Total Deductions", "fr": "[Total Deductions]", "it": "[Total Deductions]"}, "visible": true, "value": "CHF 71'006", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2112, "left": 2091, "right": 2230, "bottom": 2166}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "865af713-5dae-4e32-bab7-b93692dfab23", "key": "p2_expense_employment", "title": "P2 Berufskosten", "titles": {"de": "P2 Berufskosten", "en": "P2 Employment Expenses", "fr": "P2 Déductions liées a l'activite dependante", "it": "[P2 Employment Expenses]"}, "visible": true, "value": "CHF 8'850", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 385, "left": 2116, "right": 2230, "bottom": 441}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 3, "rotation_angle": 0, "number": 3, "page_category": {"id": "140", "name": "TAX_DECLARATION_PAGE_ASSETS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97765, "level": "high"}, "confidence": 0.97765, "page_objects": [{"uuid": "fdcea886-a6dc-40b1-8d37-4a3ff3d65d5a", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "720e346f-42f7-48d4-b34e-db246dae9172", "key": "fh_erben", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"erben\", \"value_found\": \"erben\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"30.2 Bargeld, Gold und andere Edelmetalle                                                                   404\\n30.3 Lebens- und Rentenversicherungen (Steuerwert gem. Bescheinigung der Versicherungsges.)\\nVersicherungsgesellschaft                                       Abschlussjahr Ablaufsjahr Steuerwert\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1520, "left": 742, "right": 849, "bottom": 1583}, "page_number": 3, "confidence": 0.3419880270957947, "confidence_summary": {"value_formatted": "34%", "value": 0.3419880270957947, "level": "low"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "a9427f18-6448-4c5f-b39b-604f57456904", "key": "assets_taxable_local", "title": "Steuerbares Vermögen im Kanton", "titles": {"de": "Steuerbares Vermögen im Kanton", "en": "Taxable Assets in Canton", "fr": "Fortune imposable (locale)", "it": "[Taxable Assets in Canton]"}, "visible": true, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2182, "left": 2617, "right": 2820, "bottom": 2245}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "52d5a917-ce82-490f-ac9e-d713136ad8e1", "key": "assets_portfolio", "title": "Wertschriften und Guthaben", "titles": {"de": "Wertschriften und Guthaben", "en": "Portfolio and Accounts", "fr": "[Portfolio and Accounts]", "it": "[Portfolio and Accounts]"}, "visible": true, "value": "CHF 2'458'532", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 319, "left": 2617, "right": 2865, "bottom": 394}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "ed56a616-90bf-4290-81dd-f6d1a4b2effa", "key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "visible": true, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Verm\\u00f6gen im In- und Ausland                                        Steuerwert am 31. Dezember 2019\\nEhemann / Einzelperson / P1, Ehefrau / P2 und minderj\\u00e4hrige Kinder, einschliesslich Nutzniessungsverm\\u00f6gen\\nCHF ohne Rappen\\n30. Bewegliches Verm\\u00f6gen\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 193, "left": 1371, "right": 1580, "bottom": 256}, "page_number": 3, "confidence": 0.*****************, "confidence_summary": {"value_formatted": "10%", "value": 0.*****************, "level": "low"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "ef5f06b8-bb6f-4fb8-b5f4-9318dbc475c4", "key": "assets_taxable_global", "title": "Steuerbares Vermögen gesamt", "titles": {"de": "Steuerbares Vermögen gesamt", "en": "Taxable Assets (global)", "fr": "Fortune imposable (globale)", "it": "[Taxable Assets (global)]"}, "visible": true, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1832, "left": 2617, "right": 2820, "bottom": 1892}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "30537b30-2f78-44df-978b-a048cdf0068a", "key": "assets_cars", "title": "Fahrzeuge", "titles": {"de": "Fahrzeuge", "en": "Vehicles", "fr": "[Vehicles]", "it": "[Vehicles]"}, "visible": true, "value": "CHF 1'040", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 841, "left": 2709, "right": 2820, "bottom": 904}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "d8cc3f37-0f42-476d-9270-b93e2eecab9d", "key": "assets_other", "title": "Übrige Vermögenswerte", "titles": {"de": "Übrige Vermögenswerte", "en": "Further Assets", "fr": "[Further Assets]", "it": "[Further Assets]"}, "visible": true, "value": "CHF 180'288", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1019, "left": 2658, "right": 2820, "bottom": 1082}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "a129f7c7-05b0-46e7-9dfd-5a1618da4b6f", "key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "visible": true, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1727, "left": 2621, "right": 2865, "bottom": 1790}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "008dbdc9-cbe2-4c56-abed-bb0fa286e09d", "key": "address_real_estate_primary", "title": "Primärliegenschaft Adresse", "titles": {"de": "Primärliegenschaft Adresse", "en": "Primary Self used Real Estate Address", "fr": "[Primary Self used Real Estate Address]", "it": "[Primary Self used Real Estate Address]"}, "visible": true, "value": "Gemeinde ZH                    Zürich           Strasse Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1240, "left": 238, "right": 1590, "bottom": 1303}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "9bca21e8-3f9e-4efc-8f0b-2d79910000c1", "key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"<PERSON>henkung\", \"value_found\": \"Schenkung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 293, "right": 495, "bottom": 2819}, "page_number": 3, "confidence": 0.212648406624794, "confidence_summary": {"value_formatted": "21%", "value": 0.212648406624794, "level": "low"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "44598361-f0f4-422a-86fc-488132d20bfd", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "47c0116b-c256-437b-b614-fdcfa47e7f9d", "key": "assets_gross_total", "title": "Total der Vermögenswerte", "titles": {"de": "Total der Vermögenswerte", "en": "Total Assets Gross", "fr": "Fortune brut", "it": "[Total Assets Gross]"}, "visible": true, "value": "CHF 3'811'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1630, "left": 2619, "right": 2820, "bottom": 1693}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "6e61f3c3-220b-4145-b4cd-7cfd2f0f210b", "key": "assets_real_estate_main_property", "title": "Liegenschaft EFH oder Stockwerkeigentum", "titles": {"de": "Liegenschaft EFH oder Stockwerkeigentum", "en": "Real Estate (house or appartment", "fr": "[Real Estate (house or appartment]", "it": "[Real Estate (house or appartment]"}, "visible": true, "value": "CHF 1'172'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1235, "left": 2621, "right": 2820, "bottom": 1298}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "da24dfbe-a514-4a04-9f20-29a21ab778e3", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 4, "rotation_angle": 0, "number": 4, "page_category": {"id": "199", "name": "TAX_DECLARATION_MISC", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97775, "level": "high"}, "confidence": 0.97775, "page_objects": [{"uuid": "4820812b-0c1a-42d5-a3ca-990bef869fde", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "da24dfbe-a514-4a04-9f20-29a21ab778e3"}, {"uuid": "7d12a427-ddce-4c7a-9900-e0b073666901", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "da24dfbe-a514-4a04-9f20-29a21ab778e3"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "ddd8b538-d1de-4a61-84a5-275d4abbea3a", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 5, "rotation_angle": 0, "number": 5, "page_category": {"id": "199", "name": "TAX_DECLARATION_MISC", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97775, "level": "high"}, "confidence": 0.97775, "page_objects": [{"uuid": "5345ff4e-98d3-41d8-8ca1-bcdb3e3f20dd", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "ddd8b538-d1de-4a61-84a5-275d4abbea3a"}, {"uuid": "5825ec09-f26b-45a4-b9df-c0ef9b899903", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "ddd8b538-d1de-4a61-84a5-275d4abbea3a"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "c870aff6-f59c-4125-871b-2acba898b212", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 6, "rotation_angle": 0, "number": 6, "page_category": {"id": "167", "name": "TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97774, "level": "high"}, "confidence": 0.97774, "page_objects": [{"uuid": "8a59e882-c5e0-4abe-a371-fc28568f791d", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "c870aff6-f59c-4125-871b-2acba898b212"}, {"uuid": "f9c84e63-2cf2-4c5d-aea4-98c8a67f2285", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "c870aff6-f59c-4125-871b-2acba898b212"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "74253870-08d1-43f7-9f2b-5b80bea5b51d", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 7, "rotation_angle": 0, "number": 7, "page_category": {"id": "167", "name": "TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97772, "level": "high"}, "confidence": 0.97772, "page_objects": [{"uuid": "188deafb-08eb-4cf3-8271-9ba6d3d1735c", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "74253870-08d1-43f7-9f2b-5b80bea5b51d"}, {"uuid": "65332b38-511c-4212-ad5b-2c7d154d2d72", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "74253870-08d1-43f7-9f2b-5b80bea5b51d"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 8, "rotation_angle": 0, "number": 8, "page_category": {"id": "150", "name": "TAX_DECLARATION_DEBT_INVENTORY", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97778, "level": "high"}, "confidence": 0.97778, "page_objects": [{"uuid": "3b4ba83b-1b67-4b0c-a474-141fed66264c", "key": "debt_detail_lines", "title": "Schulden Details", "titles": {"de": "Schulden Details", "en": "Debt Details", "fr": "[Debt Details]", "it": "[Debt Details]"}, "visible": true, "value": "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 926, "left": 142, "right": 2812, "bottom": 1001}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "dc01c147-dd9f-4293-80ac-6b9cc0abeae2", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "79f75a89-07cf-4f16-b9b6-4a639d06e7d6", "key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "visible": true, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3589, "left": 2047, "right": 2247, "bottom": 3656}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "5d4d64cf-c0c0-4172-8f5c-222bbb34a8a7", "key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "visible": true, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3583, "left": 2652, "right": 2788, "bottom": 3658}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "7475af18-59ab-48e7-9392-b90d07f7e90b", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "8ad75304-fcc2-4116-9d12-8f812833251e", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 9, "rotation_angle": 0, "number": 9, "page_category": {"id": "166", "name": "TAX_DECLARATION_INSURANCE_PREMIUMS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97774, "level": "high"}, "confidence": 0.97774, "page_objects": [{"uuid": "e3e33f04-0120-47ce-9f4a-b67e0a5b9328", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2827, "bottom": 4127}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "8ad75304-fcc2-4116-9d12-8f812833251e"}, {"uuid": "0abed3e7-66b3-4442-bb5b-83e03b39b1fb", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1942, "right": 2209, "bottom": 270}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "8ad75304-fcc2-4116-9d12-8f812833251e"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 10, "rotation_angle": 0, "number": 10, "page_category": {"id": "160", "name": "TAX_DECLARATION_ACCOUNTS_FORM_FRONT", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97698, "level": "high"}, "confidence": 0.97698, "page_objects": [{"uuid": "60d3500e-3a9c-40e2-ade2-0df6ac4f9be4", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 31, "left": 32, "right": 133, "bottom": 116}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "8009ca52-e3ea-4fb0-b2e0-582e4102f533", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 33, "right": 197, "bottom": 1148}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "f8fe286d-0101-4c2a-b90f-7954c9991d65", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2856, "bottom": 4127}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "5396c164-7cf5-4d0e-a61b-ee7cd8448961", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 67, "left": 713, "right": 798, "bottom": 110}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "becd196a-6b8a-4381-bf7e-2d1222907ec1", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 218, "left": 2586, "right": 2856, "bottom": 409}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 11, "rotation_angle": 0, "number": 11, "page_category": {"id": "161", "name": "TAX_DECLARATION_ACCOUNTS_FORM_DETAILS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97527, "level": "high"}, "confidence": 0.97527, "page_objects": [{"uuid": "c1e3ca9e-0d5b-4053-b65e-b9aed4286316", "key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": false, "value": "{\"value\": \"<PERSON>henkung\", \"value_found\": \"Schenkung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"* Code: G <PERSON>ch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                            0.00                                    | Seite 2, <PERSON><PERSON><PERSON> 4.1\\n540\\n151\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2435, "left": 846, "right": 995, "bottom": 2489}, "page_number": 11, "confidence": 0.038113147020339966, "confidence_summary": {"value_formatted": "4%", "value": 0.038113147020339966, "level": "low"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "9a978ba8-5605-41a5-b150-cf0b4cc3ce5f", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 33, "right": 193, "bottom": 802}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "071e1ad6-440b-4d5b-9fda-aac0a14cb965", "key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "visible": false, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5. Anteile an Stockwerkeigent\\u00fcmergemeinschaft                                                                                                                                                                                                                                                                \\n* Code: G Gesch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                                                                       | Seite 2, <PERSON><PERSON><PERSON> 4.1\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2396, "left": 846, "right": 1030, "bottom": 2449}, "page_number": 11, "confidence": 0.04971255734562874, "confidence_summary": {"value_formatted": "5%", "value": 0.04971255734562874, "level": "low"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "0819e0af-63c2-41aa-b439-421584cf73ba", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 118, "left": 139, "right": 4062, "bottom": 2898}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "f8277279-913a-4230-addd-f83d6964220e", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 291, "left": 3604, "right": 3686, "bottom": 354}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "48f3b7a0-9d2d-42b2-b3ac-24bd8078d7de", "key": "fh_da<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON><PERSON>\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"1. Kundenguthaben deren Bruttozins CHF 200.-nicht \\u00fcbersteigt                                                   \\u00dcbertrag Bruttoertrag A in Kolonne Bruttoertrag B                                                 l       +                  539\\n2. <PERSON><PERSON><PERSON>, Ko<PERSON> und Guthaben aller Art ohne Verrechnungssteuerabzug\\n3. Ausl\\u00e4ndische Wertschriften aller Art                                                                                                                                                                                                                                                                              I\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2217, "left": 184, "right": 301, "bottom": 2271}, "page_number": 11, "confidence": 0.*****************, "confidence_summary": {"value_formatted": "15%", "value": 0.*****************, "level": "low"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "766772e7-037f-4689-9b2e-aca37203a8b0", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 12, "rotation_angle": 0, "number": 12, "page_category": {"id": "161", "name": "TAX_DECLARATION_ACCOUNTS_FORM_DETAILS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "316", "name": "TAX_LIST_FINANCIAL_ASSETS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "98%", "value": 0.97696, "level": "high"}, "confidence": 0.97696, "page_objects": [{"uuid": "********-04c7-4f4f-b923-c057bcf8f822", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 32, "right": 192, "bottom": 804}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "766772e7-037f-4689-9b2e-aca37203a8b0"}, {"uuid": "b3710b5a-c213-4d3a-ade7-4773263e470a", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 34, "left": 33, "right": 119, "bottom": 93}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "766772e7-037f-4689-9b2e-aca37203a8b0"}, {"uuid": "25c9df39-2538-457e-b1fb-f9acc2dcbbd9", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 60, "left": 137, "right": 4062, "bottom": 2898}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "766772e7-037f-4689-9b2e-aca37203a8b0"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}], "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": "Steuererklärung", "en": "Tax Declaration", "fr": "<PERSON><PERSON><PERSON><PERSON><PERSON> d'imp<PERSON>", "it": "Dichiarazione d'imposta", "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "aggregated_objects": [{"uuid": "2478f538-1f7a-493b-842c-59a6573fb1e4", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2855, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "0e3ebe5b-0ee6-4c61-a538-dc5349cd62a5", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2387, "bottom": 414}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "f90a75e5-971b-4f45-9ab1-6cc2b2b5ed77", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 633, "left": 2047, "right": 2407, "bottom": 703}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "eaf11c28-ef77-48ec-87c5-5affedea989f", "key": "p1_fullname", "title": "P1 Name", "titles": {"de": "P1 Name", "en": "P1 Name", "fr": "P1 Nom", "it": "[P1 Name]"}, "visible": true, "value": "<PERSON><PERSON><PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 723, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "4ac478f2-a892-4f77-8d5a-71c6dd31d84b", "key": "address_block", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Address", "fr": "[Address]", "it": "[Address]"}, "visible": true, "value": "<PERSON><PERSON><PERSON> Maria\nBe<PERSON>pielstrasse 42\n8055 Zürich", "type": "ADDRESS_BLOCK", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 781, "left": 815, "right": 1192, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "5f56f43a-f2d8-4385-ad6e-d641cc390d88", "key": "street", "title": "Strasse", "titles": {"de": "Strasse", "en": "Street", "fr": "[Street]", "it": "[Street]"}, "visible": true, "value": "Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 848, "left": 816, "right": 1183, "bottom": 911}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "415f515f-1e2a-46d7-ba15-fba595794640", "key": "zip", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "Zip Code", "fr": "[Zip Code]", "it": "[Zip Code]"}, "visible": true, "value": "8055", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 815, "right": 920, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "4f8b6b9b-8eaa-4fa0-8640-d66e88921667", "key": "city", "title": "Ort", "titles": {"de": "Ort", "en": "City", "fr": "[City]", "it": "[City]"}, "visible": true, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 912, "left": 925, "right": 1054, "bottom": 972}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "87f369a3-b3c5-4917-a6a9-0639e2145831", "key": "p1_date_of_birth", "title": "P1 Geburtsdatum", "titles": {"de": "P1 Geburtsdatum", "en": "P1 Date of Birth", "fr": "P1 Date de naissance", "it": "[P1 Date of Birth]"}, "visible": true, "value": "04.09.1967", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 964, "right": 1191, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "caa095ca-b1d6-4471-a0a3-d8782498fd87", "key": "p2_date_of_birth", "title": "P2 Geburtsdatum", "titles": {"de": "P2 Geburtsdatum", "en": "P2 Date of Birth", "fr": "P2 Date de naissance", "it": "[P2 Date of Birth]"}, "visible": true, "value": "21.10.1976", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1772, "left": 2040, "right": 2267, "bottom": 1835}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "4ede4fc2-ef15-49a7-93f8-cab7f7e5376d", "key": "p1_marital_status", "title": "P1 Zivilstand", "titles": {"de": "P1 Zivilstand", "en": "P1 Civil Status", "fr": "P1 Etat civil", "it": "[P1 Civil Status]"}, "visible": true, "value": "verheiratet", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1834, "left": 963, "right": 1181, "bottom": 1897}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "807a89ba-f0b7-4217-8669-ec3ff897cff7", "key": "p2_firstname", "title": "P2 Vorname", "titles": {"de": "P2 Vorname", "en": "P2 Lastname", "fr": "P2 Prénom", "it": "[P2 Lastname]"}, "visible": true, "value": "<PERSON>", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1831, "left": 2043, "right": 2156, "bottom": 1894}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "f119af3b-578c-4e48-afd8-a99d9b860337", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": false, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Wegleitung verwendeten Begriffe     Zivilstand      verheiratet                                   Vorname    Maria\\nwie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder <PERSON>he,       Beruf             Informatiker                                Beruf          Solution Consultant\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1897, "left": 388, "right": 520, "bottom": 1960}, "page_number": 0, "confidence": 0.08460009843111038, "confidence_summary": {"value_formatted": "8%", "value": 0.08460009843111038, "level": "low"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "fa180a62-d5cb-42c2-9917-e7b88093af74", "key": "p1_profession", "title": "P1 Beruf", "titles": {"de": "P1 Beruf", "en": "P1 Profession", "fr": "P1 Profession", "it": "[P1 Profession]"}, "visible": true, "value": "Informatiker", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 966, "right": 1207, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "a17e229f-f073-4c7f-8b28-538e7a172b47", "key": "p2_profession", "title": "P2 Beruf", "titles": {"de": "P2 Beruf", "en": "P2 Profession", "fr": "P2 Profession", "it": "[P2 Profession]"}, "visible": true, "value": "Solution Consultant", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1954, "left": 2041, "right": 2435, "bottom": 2017}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "5a8b7d0b-09eb-4af6-8bb8-8a7e71000aca", "key": "p1_employer", "title": "P1 Arbeitgeber", "titles": {"de": "P1 Arbeitgeber", "en": "P1 Employer", "fr": "P1 Employeur", "it": "[P1 Employer]"}, "visible": true, "value": "Beispielfirma AG", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 965, "right": 1300, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "ab665111-edc5-49c7-acfd-88c0fd47bb04", "key": "p2_employer", "title": "P2 Arbeitgeber", "titles": {"de": "P2 Arbeitgeber", "en": "P2 Employer", "fr": "P2 Employeur", "it": "[P2 Employer]"}, "visible": true, "value": "ServiceFirma", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2015, "left": 2041, "right": 2308, "bottom": 2078}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "bfab01a1-04ed-4682-b2a4-f2062153aff4", "key": "p1_employer_location", "title": "P1 Arbeitsort", "titles": {"de": "P1 Arbeitsort", "en": "P1 Place of work", "fr": "P1 Lieu de travail", "it": "[P1 Place of work]"}, "visible": true, "value": "Zürich", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2074, "left": 963, "right": 1090, "bottom": 2137}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "e4fa7ddb-dcf1-4641-801f-932dc4a5ffe6", "key": "phone_primary", "title": "Telefonnummer", "titles": {"de": "Telefonnummer", "en": "Phone number", "fr": "[Phone number]", "it": "[Phone number]"}, "visible": true, "value": "0763331234      P 07633", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2127, "left": 964, "right": 1539, "bottom": 2197}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "93f038bd-c4ee-460a-8aad-97856dde2fe1", "key": "phone_secondary", "title": "Telefonnummer (2. Prio)", "titles": {"de": "Telefonnummer (2. Prio)", "en": "Phone number (2nd Priority)", "fr": "[Phone number (2nd Priority)]", "it": "[Phone number (2nd Priority)]"}, "visible": true, "value": "Telefon G       0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 1769, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "322f151d-0de5-40d2-b556-00039dc622f5", "key": "p2_phone_primary", "title": "P2 Telefonnummer", "titles": {"de": "P2 Telefonnummer", "en": "P2 Phone number", "fr": "P2 Téléphone", "it": "[P2 Phone number]"}, "visible": true, "value": "0799991234", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2124, "left": 2041, "right": 2292, "bottom": 2198}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "000e0a67-971b-4a32-ac6c-e35cd3adc9d6", "key": "section_children", "title": "Kinder", "titles": {"de": "Kinder", "en": "Children", "fr": "<PERSON><PERSON><PERSON>", "it": "[Children]"}, "visible": true, "value": "<PERSON>", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2534, "left": 137, "right": 572, "bottom": 2656}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "0c60c037-71a9-4c74-8f6b-3842b8d9443d", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"wie verheiratet, getrennt, ge-       Konfession     keine                                         Konfession     keine\\nschieden, verwitwet oder Ehe,       Beruf             Informatiker                                Beruf          Solution Consultant\\nEhegatten, Ehemann und Ehe-     Arbeitgeber \\nBeispielfirma AG                          Arbeitgeber   ServiceFirma\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2569, "left": 2687, "right": 2815, "bottom": 2615}, "page_number": 0, "confidence": 0.17452068626880646, "confidence_summary": {"value_formatted": "17%", "value": 0.17452068626880646, "level": "low"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "22948305-7c3c-44d4-82c2-12747ee47434", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3799, "left": 139, "right": 2828, "bottom": 4126}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d36306e2-09f9-478d-a1db-b7da859e1857"}, {"uuid": "2f642a4f-2c86-4b07-8bfc-80d7ee9cabc3", "key": "p1_income_employed_main", "title": "P1 Einkommen Haupterwerb", "titles": {"de": "P1 Einkommen Haupterwerb", "en": "P1 Main Income", "fr": "P1 Revenu net dépendante", "it": "[P1 Main Income]"}, "visible": true, "value": "CHF 22'506", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 280, "left": 2662, "right": 2802, "bottom": 336}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e55ebd1e-3354-43ec-8fbf-825530f71bba", "key": "p2_income_employed_main", "title": "P2 Einkommen Haupterwerb", "titles": {"de": "P2 Einkommen Haupterwerb", "en": "P2 Main Income", "fr": "P2 Revenu net dépendante", "it": "[P2 Main Income]"}, "visible": true, "value": "CHF 128'991", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 360, "left": 2640, "right": 2796, "bottom": 427}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "635cd23c-1566-46dc-a916-9b4943833a7f", "key": "p1_income_employed_side", "title": "P1 Einkommen Nebenverdienst", "titles": {"de": "P1 Einkommen Nebenverdienst", "en": "P1 Additional Income", "fr": "[P1 Additional Income]", "it": "[P1 Additional Income]"}, "visible": true, "value": "CHF 7'502", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 459, "left": 2688, "right": 2802, "bottom": 514}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fdddaa16-fcc1-401e-b4f2-a2357e073558", "key": "fh_gewerbe", "title": "Gewerbe", "titles": {"de": "Gewerbe", "en": "Gewerbe", "fr": "Gewerbe", "it": "Gewerbe"}, "visible": false, "value": "{\"value\": \"<PERSON><PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON>erbe\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Verwaltungsrats- und Vorstands-\\n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \\n2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 652, "left": 1731, "right": 1888, "bottom": 708}, "page_number": 1, "confidence": 0.07572177052497864, "confidence_summary": {"value_formatted": "8%", "value": 0.07572177052497864, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "63890512-dce5-4072-bbe5-54a67036feb8", "key": "fh_landwirtschaft", "title": "Landwirtschaft", "titles": {"de": "Landwirtschaft", "en": "Landwirtschaft", "fr": "Landwirtschaft", "it": "Landwirtschaft"}, "visible": false, "value": "{\"value\": \"Landwirtschaft\", \"value_found\": \"Landwirtschaft\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\\n2: Inklusive Ertr\\u00e4ge aus                 2.1  Haupterwerb           Ehemann / Einzelperson / P1                     Hilfsblatt   120\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 719, "left": 1343, "right": 1605, "bottom": 775}, "page_number": 1, "confidence": 0.04614436998963356, "confidence_summary": {"value_formatted": "5%", "value": 0.04614436998963356, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "ee35de32-2f59-4f9d-b046-db06bb5aee29", "key": "fh_arbeitslos", "title": "arbeitslos", "titles": {"de": "arbeitslos", "en": "arbeitslos", "fr": "arbeitslos", "it": "arbeitslos"}, "visible": true, "value": "{\"value\": \"arbeitslos\", \"value_found\": \"Arbeitslos\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"3.3  Erwerbsausfallentsch\\u00e4digungen aus Arbeitslosenversicherung\\n3.3: Direkt ausbezahlte Erwerbs-\\nausfallentsch\\u00e4digungen.                   Ehemann / Einzelperson / P1                                    Bescheinigung 140\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1801, "left": 1441, "right": 1624, "bottom": 1857}, "page_number": 1, "confidence": 0.21327336132526398, "confidence_summary": {"value_formatted": "21%", "value": 0.21327336132526398, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "3a5d2ebc-fd0a-4208-9bde-4640c0784d07", "key": "p2_income_eo", "title": "P2 Einkünfte Erwerbsausfallentschädigung", "titles": {"de": "P2 Einkünfte Erwerbsausfallentschädigung", "en": "P2 Income EO", "fr": "[P2 Income EO]", "it": "[P2 Income EO]"}, "visible": true, "value": "CHF 4'388", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1939, "left": 2686, "right": 2802, "bottom": 2006}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "5ddb98b4-41d9-43f1-906f-42eb991010e4", "key": "p2_income_child_benefits", "title": "P2 Einkünfte Kinder- und Familienzulagen", "titles": {"de": "P2 Einkünfte Kinder- und Familienzulagen", "en": "P2 Income Child Benefits", "fr": "[P2 Income Child Benefits]", "it": "[P2 Income Child Benefits]"}, "visible": true, "value": "CHF 4'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2190, "left": 2686, "right": 2802, "bottom": 2244}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d18b2c4d-33aa-45e9-a7ba-8bc55a04c245", "key": "income_portfolio", "title": "Wertschriftenerträge", "titles": {"de": "Wertschriftenerträge", "en": "Income Portfolio", "fr": "Revenue mobilier", "it": "[Income Portfolio]"}, "visible": true, "value": "CHF 15", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2341, "left": 2753, "right": 2803, "bottom": 2408}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "0de8e719-0480-479d-8d61-92a57279f370", "key": "fh_alimente", "title": "Alimente", "titles": {"de": "Alimente", "en": "Alimente", "fr": "Alimente", "it": "Alimente"}, "visible": true, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"Alimente\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.1 und 5.2\\n5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2566, "left": 366, "right": 497, "bottom": 2620}, "page_number": 1, "confidence": 0.16822287440299988, "confidence_summary": {"value_formatted": "17%", "value": 0.16822287440299988, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d95ffed1-f4c1-4889-89f4-c84e8045bdc0", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1194, "right": 1387, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "17c44624-2bec-455a-8d98-2aa121b31d63", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1449, "right": 1593, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "9bbbda43-492e-4ff0-aa16-14ad22518c3f", "key": "property_imputed_rental_value", "title": "Liegenschaft Eigenmietwert", "titles": {"de": "Liegenschaft Eigenmietwert", "en": "Imputed Rental Value", "fr": "Valeur locative", "it": "Valore locativo"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3111, "left": 1971, "right": 2110, "bottom": 3165}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7f885fe2-f238-4ff3-810d-d1dbdbeb1059", "key": "income_real_estate_gross", "title": "Liegenschaftenertrag brutto", "titles": {"de": "Liegenschaftenertrag brutto", "en": "Income Real Estate gross", "fr": "Revenu brut immobilier", "it": "[Income Real Estate gross]"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3305, "left": 1971, "right": 2110, "bottom": 3358}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fb1d780e-24aa-4f87-8c35-ff266fc37331", "key": "property_maintenance_cost", "title": "Liegenschaft Unterhaltskosten", "titles": {"de": "Liegenschaft Unterhaltskosten", "en": "Maintenance Cost", "fr": "Charges et frais d'entretien d'immeuble", "it": "[Maintenance Cost]"}, "visible": true, "value": "CHF 5'140", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3397, "left": 1996, "right": 2110, "bottom": 3451}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7ef6e61c-a037-4531-b58b-6aed24336d93", "key": "income_real_estate_net_primary", "title": "Primärliegenschaft Ertrag netto", "titles": {"de": "Primärliegenschaft Ertrag netto", "en": "Primary Self used Real Estate Income net", "fr": "[Primary Self used Real Estate Income net]", "it": "[Primary Self used Real Estate Income net]"}, "visible": true, "value": "CHF 20'560", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3588, "left": 2662, "right": 2802, "bottom": 3642}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e6cf3875-f643-45ce-ba5f-f9452fd8de7d", "key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "visible": true, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3765, "left": 2640, "right": 2802, "bottom": 3833}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e1569f2a-04aa-4eef-9f58-aa42728c36cb", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "05d008e8-9d82-4c03-8bec-ff45aebf447f", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "b9957e20-5a34-4b74-aeed-b68fe0a93ca2", "key": "p1_expense_employment", "title": "P1 Berufskosten", "titles": {"de": "P1 Berufskosten", "en": "P1 Employment Expenses", "fr": "P1 Déductions liées a l'activite dependante", "it": "[P1 Employment Expenses]"}, "visible": true, "value": "CHF 7'901", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 295, "left": 2116, "right": 2224, "bottom": 351}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "865af713-5dae-4e32-bab7-b93692dfab23", "key": "p2_expense_employment", "title": "P2 Berufskosten", "titles": {"de": "P2 Berufskosten", "en": "P2 Employment Expenses", "fr": "P2 Déductions liées a l'activite dependante", "it": "[P2 Employment Expenses]"}, "visible": true, "value": "CHF 8'850", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 385, "left": 2116, "right": 2230, "bottom": 441}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "1ad1a532-9055-4521-a100-7c02536dcd7c", "key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "visible": true, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 470, "left": 2093, "right": 2230, "bottom": 545}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "3d6d0e10-24f8-49b7-92b5-0aa411b905e5", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 655, "right": 842, "bottom": 663}, "page_number": 2, "confidence": 0.19417862594127655, "confidence_summary": {"value_formatted": "19%", "value": 0.19417862594127655, "level": "low"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "1b3be027-2247-44bf-8faa-2db9b88e2d44", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"13. Unterhaltsbeitr\\u00e4ge und Rentenleistungen\\n1\\\"3 .1' Unterhaltsbeitr\\u00e4ge an den geschiedenen oder getrennt lebenden Ehegatten / Partn.\\n(mit der Steuererkl\\u00e4rung 2019 sind alle Belege einzureichen)                              254\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 609, "left": 966, "right": 1107, "bottom": 663}, "page_number": 2, "confidence": 0.19417862594127655, "confidence_summary": {"value_formatted": "19%", "value": 0.19417862594127655, "level": "low"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "73fc4a8e-dafc-4214-a015-c03adb03bc6d", "key": "p1_contribution_pillar_3a", "title": "P1 Beiträge Säule 3a", "titles": {"de": "P1 Beiträge Säule 3a", "en": "P1 Contribution Pillar 3a", "fr": "[P1 Contribution Pillar 3a]", "it": "[P1 Contribution Pillar 3a]"}, "visible": true, "value": "CHF 3'350", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1026, "left": 2116, "right": 2230, "bottom": 1082}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "3f049066-d109-443d-999f-59d78ff558e7", "key": "p2_contribution_pillar_3a", "title": "P2 Beiträge Säule 3a", "titles": {"de": "P2 Beiträge Säule 3a", "en": "P2 Contribution Pillar 3a", "fr": "[P2 Contribution Pillar 3a]", "it": "[P2 Contribution Pillar 3a]"}, "visible": true, "value": "CHF 6'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1117, "left": 2115, "right": 2230, "bottom": 1173}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "0dc74435-eedf-41c5-bc3b-e75f15136a50", "key": "insurance_premiums_and_interest_on_savings_accounts", "title": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "titles": {"de": "Versicherungsprämien und Z<PERSON>en von Sparkapitalien", "en": "Insurance Premiums and Interest on Savings Accounts", "fr": "[Insurance Premiums and Interest on Savings Accounts]", "it": "[Insurance Premiums and Interest on Savings Accounts]"}, "visible": true, "value": "CHF 7'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1229, "left": 2116, "right": 2230, "bottom": 1282}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "1ace674e-f162-4739-8f33-7b75f8604fa9", "key": "expense_children_daycare", "title": "Fremdbetreuung von Kindern", "titles": {"de": "Fremdbetreuung von Kindern", "en": "Expenses Daycare Children", "fr": "[Expenses Daycare Children]", "it": "[Expenses Daycare Children]"}, "visible": true, "value": "CHF 19'248", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1819, "left": 2093, "right": 2230, "bottom": 1894}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "22003cf5-31d0-4c6c-9b5a-a9be36d97e90", "key": "deductions_total", "title": "Total der Abzüge", "titles": {"de": "Total der Abzüge", "en": "Total Deductions", "fr": "[Total Deductions]", "it": "[Total Deductions]"}, "visible": true, "value": "CHF 71'006", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2112, "left": 2091, "right": 2230, "bottom": 2166}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "ba2846a1-d970-4f69-a1bf-21678dbde526", "key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "visible": true, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2345, "left": 2068, "right": 2230, "bottom": 2419}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "8a5bd46c-1e7f-468d-a94e-b32ace490386", "key": "income_net_total", "title": "Total der Einkünfte (netto)", "titles": {"de": "Total der Einkünfte (netto)", "en": "Total Income net", "fr": "[Total Income net]", "it": "[Total Income net]"}, "visible": true, "value": "CHF 117'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2536, "left": 2068, "right": 2230, "bottom": 2610}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "e09de8d0-e35c-4a7e-84c9-3cc92b8432cd", "key": "income_taxable_global", "title": "Steuerbares Einkommen gesamt", "titles": {"de": "Steuerbares Einkommen gesamt", "en": "Taxable Income total", "fr": "[Taxable Income total]", "it": "[Taxable Income total]"}, "visible": true, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3414, "left": 2091, "right": 2230, "bottom": 3488}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "fe225db9-d5b6-4d53-820f-723686d20435", "key": "income_taxable_local", "title": "Steuerbares Einkommen im Kanton", "titles": {"de": "Steuerbares Einkommen im Kanton", "en": "Taxable Income in Canton", "fr": "[Taxable Income in Canton]", "it": "[Taxable Income in Canton]"}, "visible": true, "value": "CHF 99'756", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3757, "left": 2091, "right": 2230, "bottom": 3832}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "ed3be4c7-233e-49de-bef8-bae6c5ae99c8", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "f5a1e024-5dd8-4a3f-b670-868ecdbee789", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3837, "left": 269, "right": 2836, "bottom": 4124}, "page_number": 2, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "0fcfdd24-5f80-4860-a616-6d1d7b3f1ea3"}, {"uuid": "ed56a616-90bf-4290-81dd-f6d1a4b2effa", "key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "visible": true, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Verm\\u00f6gen im In- und Ausland                                        Steuerwert am 31. Dezember 2019\\nEhemann / Einzelperson / P1, Ehefrau / P2 und minderj\\u00e4hrige Kinder, einschliesslich Nutzniessungsverm\\u00f6gen\\nCHF ohne Rappen\\n30. Bewegliches Verm\\u00f6gen\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 193, "left": 1371, "right": 1580, "bottom": 256}, "page_number": 3, "confidence": 0.*****************, "confidence_summary": {"value_formatted": "10%", "value": 0.*****************, "level": "low"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "52d5a917-ce82-490f-ac9e-d713136ad8e1", "key": "assets_portfolio", "title": "Wertschriften und Guthaben", "titles": {"de": "Wertschriften und Guthaben", "en": "Portfolio and Accounts", "fr": "[Portfolio and Accounts]", "it": "[Portfolio and Accounts]"}, "visible": true, "value": "CHF 2'458'532", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 319, "left": 2617, "right": 2865, "bottom": 394}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "30537b30-2f78-44df-978b-a048cdf0068a", "key": "assets_cars", "title": "Fahrzeuge", "titles": {"de": "Fahrzeuge", "en": "Vehicles", "fr": "[Vehicles]", "it": "[Vehicles]"}, "visible": true, "value": "CHF 1'040", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 841, "left": 2709, "right": 2820, "bottom": 904}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "d8cc3f37-0f42-476d-9270-b93e2eecab9d", "key": "assets_other", "title": "Übrige Vermögenswerte", "titles": {"de": "Übrige Vermögenswerte", "en": "Further Assets", "fr": "[Further Assets]", "it": "[Further Assets]"}, "visible": true, "value": "CHF 180'288", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1019, "left": 2658, "right": 2820, "bottom": 1082}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "008dbdc9-cbe2-4c56-abed-bb0fa286e09d", "key": "address_real_estate_primary", "title": "Primärliegenschaft Adresse", "titles": {"de": "Primärliegenschaft Adresse", "en": "Primary Self used Real Estate Address", "fr": "[Primary Self used Real Estate Address]", "it": "[Primary Self used Real Estate Address]"}, "visible": true, "value": "Gemeinde ZH                    Zürich           Strasse Beispielstrasse 42", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1240, "left": 238, "right": 1590, "bottom": 1303}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "6e61f3c3-220b-4145-b4cd-7cfd2f0f210b", "key": "assets_real_estate_main_property", "title": "Liegenschaft EFH oder Stockwerkeigentum", "titles": {"de": "Liegenschaft EFH oder Stockwerkeigentum", "en": "Real Estate (house or appartment", "fr": "[Real Estate (house or appartment]", "it": "[Real Estate (house or appartment]"}, "visible": true, "value": "CHF 1'172'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1235, "left": 2621, "right": 2820, "bottom": 1298}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "720e346f-42f7-48d4-b34e-db246dae9172", "key": "fh_erben", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"erben\", \"value_found\": \"erben\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"30.2 Bargeld, Gold und andere Edelmetalle                                                                   404\\n30.3 Lebens- und Rentenversicherungen (Steuerwert gem. Bescheinigung der Versicherungsges.)\\nVersicherungsgesellschaft                                       Abschlussjahr Ablaufsjahr Steuerwert\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1520, "left": 742, "right": 849, "bottom": 1583}, "page_number": 3, "confidence": 0.3419880270957947, "confidence_summary": {"value_formatted": "34%", "value": 0.3419880270957947, "level": "low"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "47c0116b-c256-437b-b614-fdcfa47e7f9d", "key": "assets_gross_total", "title": "Total der Vermögenswerte", "titles": {"de": "Total der Vermögenswerte", "en": "Total Assets Gross", "fr": "Fortune brut", "it": "[Total Assets Gross]"}, "visible": true, "value": "CHF 3'811'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1630, "left": 2619, "right": 2820, "bottom": 1693}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "a129f7c7-05b0-46e7-9dfd-5a1618da4b6f", "key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "visible": true, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1727, "left": 2621, "right": 2865, "bottom": 1790}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "ef5f06b8-bb6f-4fb8-b5f4-9318dbc475c4", "key": "assets_taxable_global", "title": "Steuerbares Vermögen gesamt", "titles": {"de": "Steuerbares Vermögen gesamt", "en": "Taxable Assets (global)", "fr": "Fortune imposable (globale)", "it": "[Taxable Assets (global)]"}, "visible": true, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1832, "left": 2617, "right": 2820, "bottom": 1892}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "a9427f18-6448-4c5f-b39b-604f57456904", "key": "assets_taxable_local", "title": "Steuerbares Vermögen im Kanton", "titles": {"de": "Steuerbares Vermögen im Kanton", "en": "Taxable Assets in Canton", "fr": "Fortune imposable (locale)", "it": "[Taxable Assets in Canton]"}, "visible": true, "value": "CHF 2'676'860", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2182, "left": 2617, "right": 2820, "bottom": 2245}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "9bca21e8-3f9e-4efc-8f0b-2d79910000c1", "key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"<PERSON>henkung\", \"value_found\": \"Schenkung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"50.  Schenkungen  Erbvorbezug  Erbschaften            Beteiligung an Erbengemeinschaften\\n(Name, Adresse und Verwandtschaftsgrad einsetzen)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2756, "left": 293, "right": 495, "bottom": 2819}, "page_number": 3, "confidence": 0.212648406624794, "confidence_summary": {"value_formatted": "21%", "value": 0.212648406624794, "level": "low"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "fdcea886-a6dc-40b1-8d37-4a3ff3d65d5a", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "44598361-f0f4-422a-86fc-488132d20bfd", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3909, "left": 269, "right": 2834, "bottom": 4126}, "page_number": 3, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "f950b05e-24f4-4ffc-9b87-cd119a7073b4"}, {"uuid": "7d12a427-ddce-4c7a-9900-e0b073666901", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "da24dfbe-a514-4a04-9f20-29a21ab778e3"}, {"uuid": "4820812b-0c1a-42d5-a3ca-990bef869fde", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 4, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "da24dfbe-a514-4a04-9f20-29a21ab778e3"}, {"uuid": "5345ff4e-98d3-41d8-8ca1-bcdb3e3f20dd", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 139, "right": 2853, "bottom": 4125}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "ddd8b538-d1de-4a61-84a5-275d4abbea3a"}, {"uuid": "5825ec09-f26b-45a4-b9df-c0ef9b899903", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 80, "left": 2578, "right": 2853, "bottom": 278}, "page_number": 5, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "ddd8b538-d1de-4a61-84a5-275d4abbea3a"}, {"uuid": "f9c84e63-2cf2-4c5d-aea4-98c8a67f2285", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "c870aff6-f59c-4125-871b-2acba898b212"}, {"uuid": "8a59e882-c5e0-4abe-a371-fc28568f791d", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 6, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "c870aff6-f59c-4125-871b-2acba898b212"}, {"uuid": "188deafb-08eb-4cf3-8271-9ba6d3d1735c", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2828, "bottom": 4126}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "74253870-08d1-43f7-9f2b-5b80bea5b51d"}, {"uuid": "65332b38-511c-4212-ad5b-2c7d154d2d72", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1560, "right": 1827, "bottom": 270}, "page_number": 7, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "74253870-08d1-43f7-9f2b-5b80bea5b51d"}, {"uuid": "3b4ba83b-1b67-4b0c-a474-141fed66264c", "key": "debt_detail_lines", "title": "Schulden Details", "titles": {"de": "Schulden Details", "en": "Debt Details", "fr": "[Debt Details]", "it": "[Debt Details]"}, "visible": true, "value": "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257", "type": "PARAGRAPH", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 926, "left": 142, "right": 2812, "bottom": 1001}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "79f75a89-07cf-4f16-b9b6-4a639d06e7d6", "key": "debt_total", "title": "Total Schulden", "titles": {"de": "Total Schulden", "en": "Total Debt", "fr": "[Total Debt]", "it": "[Total Debt]"}, "visible": true, "value": "CHF 1'135'000", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3589, "left": 2047, "right": 2247, "bottom": 3656}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "5d4d64cf-c0c0-4172-8f5c-222bbb34a8a7", "key": "interest_paid_on_debt", "title": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "Private <PERSON><PERSON><PERSON><PERSON><PERSON>", "en": "Interest paid on Debt", "fr": "[Interest paid on Debt]", "it": "[Interest paid on Debt]"}, "visible": true, "value": "CHF 11'257", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3583, "left": 2652, "right": 2788, "bottom": 3658}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "7475af18-59ab-48e7-9392-b90d07f7e90b", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "dc01c147-dd9f-4293-80ac-6b9cc0abeae2", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3805, "left": 139, "right": 2827, "bottom": 4127}, "page_number": 8, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "d407069b-e52c-4eaa-9c1e-cc5fbc8b1883"}, {"uuid": "e3e33f04-0120-47ce-9f4a-b67e0a5b9328", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 13, "left": 133, "right": 2827, "bottom": 4127}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "8ad75304-fcc2-4116-9d12-8f812833251e"}, {"uuid": "0abed3e7-66b3-4442-bb5b-83e03b39b1fb", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 79, "left": 1942, "right": 2209, "bottom": 270}, "page_number": 9, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "8ad75304-fcc2-4116-9d12-8f812833251e"}, {"uuid": "f8fe286d-0101-4c2a-b90f-7954c9991d65", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 31, "left": 133, "right": 2856, "bottom": 4127}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "60d3500e-3a9c-40e2-ade2-0df6ac4f9be4", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 31, "left": 32, "right": 133, "bottom": 116}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "becd196a-6b8a-4381-bf7e-2d1222907ec1", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 218, "left": 2586, "right": 2856, "bottom": 409}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "5396c164-7cf5-4d0e-a61b-ee7cd8448961", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 67, "left": 713, "right": 798, "bottom": 110}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "8009ca52-e3ea-4fb0-b2e0-582e4102f533", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 827, "ref_height": 1170, "top": 1085, "left": 33, "right": 197, "bottom": 1148}, "page_number": 10, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "3e81c275-ae95-4509-85d2-89156e8a6afa"}, {"uuid": "0819e0af-63c2-41aa-b439-421584cf73ba", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 118, "left": 139, "right": 4062, "bottom": 2898}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "f8277279-913a-4230-addd-f83d6964220e", "key": "year", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Year", "fr": "[Year]", "it": "[Year]"}, "visible": true, "value": "2019", "type": "INT", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 291, "left": 3604, "right": 3686, "bottom": 354}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "48f3b7a0-9d2d-42b2-b3ac-24bd8078d7de", "key": "fh_da<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON><PERSON>\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"1. Kundenguthaben deren Bruttozins CHF 200.-nicht \\u00fcbersteigt                                                   \\u00dcbertrag Bruttoertrag A in Kolonne Bruttoertrag B                                                 l       +                  539\\n2. <PERSON><PERSON><PERSON>, Ko<PERSON> und Guthaben aller Art ohne Verrechnungssteuerabzug\\n3. Ausl\\u00e4ndische Wertschriften aller Art                                                                                                                                                                                                                                                                              I\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2217, "left": 184, "right": 301, "bottom": 2271}, "page_number": 11, "confidence": 0.*****************, "confidence_summary": {"value_formatted": "15%", "value": 0.*****************, "level": "low"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "071e1ad6-440b-4d5b-9fda-aac0a14cb965", "key": "fh_nutzniessung", "title": "Nutzniessungs-Thema", "titles": {"de": "Nutzniessungs-Thema", "en": "Nutzniessungs-Thema", "fr": "Nutzniessungs-Thema", "it": "Nutzniessungs-Thema"}, "visible": false, "value": "{\"value\": \"Nutzniessung\", \"value_found\": \"Nutzniessung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5. Anteile an Stockwerkeigent\\u00fcmergemeinschaft                                                                                                                                                                                                                                                                \\n* Code: G Gesch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                                                                       | Seite 2, <PERSON><PERSON><PERSON> 4.1\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2396, "left": 846, "right": 1030, "bottom": 2449}, "page_number": 11, "confidence": 0.04971255734562874, "confidence_summary": {"value_formatted": "5%", "value": 0.04971255734562874, "level": "low"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "c1e3ca9e-0d5b-4053-b65e-b9aed4286316", "key": "fh_schenkung", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": false, "value": "{\"value\": \"<PERSON>henkung\", \"value_found\": \"Schenkung\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"* Code: G <PERSON>ch\\u00e4ftsverm\\u00f6gen            N Nutzniessungsverm\\u00f6gen    Q Qualifizierte Beteiligung ^                                                                              \\u25ba Zu \\u00fcbertragen in die\\nIhr Verrechnungssteueranspruch                                  davon 35% '            Steuererkl\\u00e4rung\\nE Wertschriften aus Erbschaften      S Schenkung\\n35% von Total Bruttoertrag A                            0.00                                    | Seite 2, <PERSON><PERSON><PERSON> 4.1\\n540\\n151\"}", "type": "FINHURDLE", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 2435, "left": 846, "right": 995, "bottom": 2489}, "page_number": 11, "confidence": 0.038113147020339966, "confidence_summary": {"value_formatted": "4%", "value": 0.038113147020339966, "level": "low"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "9a978ba8-5605-41a5-b150-cf0b4cc3ce5f", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 33, "right": 193, "bottom": 802}, "page_number": 11, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "4b9ab8c2-efa9-46ff-840d-eac01e33789d"}, {"uuid": "25c9df39-2538-457e-b1fb-f9acc2dcbbd9", "key": "canton_short", "title": "<PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON>", "en": "Canton", "fr": "[Canton]", "it": "[Canton]"}, "visible": true, "value": "ZH", "type": "STRING", "bbox": {"ref_width": 4209, "ref_height": 2974, "top": 60, "left": 137, "right": 4062, "bottom": 2898}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "766772e7-037f-4689-9b2e-aca37203a8b0"}, {"uuid": "b3710b5a-c213-4d3a-ade7-4773263e470a", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 34, "left": 33, "right": 119, "bottom": 93}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "766772e7-037f-4689-9b2e-aca37203a8b0"}, {"uuid": "********-04c7-4f4f-b923-c057bcf8f822", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 1170, "ref_height": 827, "top": 744, "left": 32, "right": 192, "bottom": 804}, "page_number": 12, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "766772e7-037f-4689-9b2e-aca37203a8b0"}], "filename": "310 Steuererklärung Mustermann Max ZH 2019.pdf", "suffix": "Mustermann Max ZH 2019", "title_elements": null, "confidence_summary": {"value_formatted": "100%", "value": 1, "level": "high"}, "confidence_info": null, "confidence": 1, "formatted_title": "310 Steuererklärung Mustermann Max ZH 2019", "document_category_translated": "Steuererklärung", "title_custom": "310 Steuererklärung Mustermann Max ZH 2019"}}, {"title": "210 Pass CH Andreas <PERSON>»", "semantic_document_uuid": "4b2299ee-6f31-465c-b2f5-68631bca45de", "data": {"uuid": "4b2299ee-6f31-465c-b2f5-68631bca45de", "status_deleted": false, "updated_at": "2023-02-15T13:54:15.508183+00:00", "access_mode": "read_write", "semantic_pages": [{"lang": "de", "uuid": "993e3f85-5562-4869-82d0-f510173c818b", "status_deleted": false, "source_file_uuid": "6418888c-b718-41c2-8f08-a547597c370f", "source_page_number": 0, "rotation_angle": 0, "number": 0, "page_category": {"id": "2", "name": "GENERIC_SINGLE_PAGE", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "210", "name": "PASSPORT_CH", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "99%", "value": 0.*********, "level": "high"}, "confidence": 0.*********, "page_objects": [{"uuid": "c3ec1a98-0059-45c1-a2c7-10a979cd2282", "key": "firstname", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Firstname", "fr": "[Firstname]", "it": "[Firstname]"}, "visible": true, "value": "<PERSON>»", "type": "STRING", "bbox": {"ref_width": 2379, "ref_height": 3990, "top": 3041, "left": 1080, "right": 1414, "bottom": 3129}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}, {"uuid": "07f9566f-bff0-40b3-9066-d46f9d9f37f2", "key": "identity_cover_misc", "title": "identity_cover_misc", "titles": {"de": "identity_cover_misc", "en": "identity_cover_misc", "fr": "identity_cover_misc", "it": "identity_cover_misc"}, "visible": true, "value": null, "type": "IMAGE", "bbox": {"ref_width": 661, "ref_height": 1109, "top": 384, "left": 146, "right": 649, "bottom": 745}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}, {"uuid": "a9058558-9d58-446a-a343-064cacce1bf0", "key": "identity_swiss_passport_first", "title": "identity_swiss_passport_first", "titles": {"de": "identity_swiss_passport_first", "en": "identity_swiss_passport_first", "fr": "identity_swiss_passport_first", "it": "identity_swiss_passport_first"}, "visible": true, "value": null, "type": "IMAGE", "bbox": {"ref_width": 661, "ref_height": 1109, "top": 744, "left": 151, "right": 637, "bottom": 1105}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "afa3f050-19e4-4837-98cf-54f4193054e6", "status_deleted": false, "source_file_uuid": "2174bbaa-f606-4926-961d-af3eb788c194", "source_page_number": 0, "rotation_angle": 0, "number": 0, "page_category": {"id": "100", "name": "CRIMINAL_RECORDS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "245", "name": "CRIMINAL_RECORDS", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "99%", "value": 0.*********, "level": "high"}, "confidence": 0.*********, "page_objects": [{"uuid": "46b4eb5f-b1bf-4038-84a6-abb5c5fc2204", "key": "rest_qr_code", "title": "rest_qr_code", "titles": {"de": "rest_qr_code", "en": "rest_qr_code", "fr": "rest_qr_code", "it": "rest_qr_code"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 829, "ref_height": 1171, "top": 191, "left": 52, "right": 280, "bottom": 247}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "e874f42e-f010-4d86-bae1-2d9125db68ee", "key": "firstname", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Firstname", "fr": "[Firstname]", "it": "[Firstname]"}, "visible": true, "value": "<PERSON>", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2134, "left": 1389, "right": 1801, "bottom": 2190}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "2109d48d-cfbe-4dcf-8bf9-1f51a1280176", "key": "native_place", "title": "Geburtsort", "titles": {"de": "Geburtsort", "en": "Native Place", "fr": "[Native Place]", "it": "[Native Place]"}, "visible": true, "value": "Ringgenberg BE", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2266, "left": 1389, "right": 1793, "bottom": 2322}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "838be00b-9470-457b-96ef-379132e2016c", "key": "rest_misc_trash", "title": "rest_misc_trash", "titles": {"de": "rest_misc_trash", "en": "rest_misc_trash", "fr": "rest_misc_trash", "it": "rest_misc_trash"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 829, "ref_height": 1171, "top": 211, "left": 451, "right": 587, "bottom": 246}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "02a1a681-c250-4a9b-9543-a3ab31715536", "key": "address_block", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Address", "fr": "[Address]", "it": "[Address]"}, "visible": true, "value": "<PERSON>sdorferstrasse 578\n8055 Zürich", "type": "ADDRESS_BLOCK", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 960, "left": 1630, "right": 2305, "bottom": 1169}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "3fc9a230-837a-49ff-a6ec-f4694dd4f08a", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "01.09.2020", "type": "DATE", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 3977, "left": 2314, "right": 2708, "bottom": 4042}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "3ad4e7d4-5fca-494f-b2d5-64f33b82ada0", "key": "lastname", "title": "Nachname", "titles": {"de": "Nachname", "en": "Lastname", "fr": "[Lastname]", "it": "[Lastname]"}, "visible": true, "value": "<PERSON><PERSON><PERSON>", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2069, "left": 1387, "right": 1635, "bottom": 2125}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "4f8f8da6-7bd9-4805-b741-bed99afe90a5", "key": "nationality", "title": "Nationalität", "titles": {"de": "Nationalität", "en": "Nationality", "fr": "Nationalité", "it": "[Nationality]"}, "visible": true, "value": "CH", "type": "STRING", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2331, "left": 1388, "right": 1466, "bottom": 2387}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "dcdc3e1c-2e1c-4569-980d-5b6150c87e2d", "key": "rest_logos", "title": "rest_logos", "titles": {"de": "rest_logos", "en": "rest_logos", "fr": "rest_logos", "it": "rest_logos"}, "visible": false, "value": null, "type": "IMAGE", "bbox": {"ref_width": 829, "ref_height": 1171, "top": 31, "left": 47, "right": 280, "bottom": 95}, "page_number": 0, "confidence": 0.94, "confidence_summary": {"value_formatted": "94%", "value": 0.94, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "d976d253-050c-4e2f-9a77-5a082ba7acd7", "key": "status", "title": "Status", "titles": {"de": "Status", "en": "Status", "fr": "État", "it": "Stato"}, "visible": true, "value": "<PERSON><PERSON>", "type": "OBJECT", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 0, "left": 0, "right": 2982, "bottom": 4215}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}, {"uuid": "40d76e86-95a6-44d3-bbb6-5c5fc3328342", "key": "date_of_birth", "title": "Geburtsdatum", "titles": {"de": "Geburtsdatum", "en": "Date of Birth", "fr": "[Date of Birth]", "it": "[Date of Birth]"}, "visible": true, "value": "04.09.1977", "type": "DATE", "bbox": {"ref_width": 2982, "ref_height": 4215, "top": 2200, "left": 1389, "right": 1666, "bottom": 2256}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "afa3f050-19e4-4837-98cf-54f4193054e6"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}, {"lang": "de", "uuid": "95d02581-940b-4b93-8d09-8a089bd57b12", "status_deleted": false, "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "source_page_number": 1, "rotation_angle": 0, "number": 1, "page_category": {"id": "120", "name": "TAX_DECLARATION_PAGE_INCOME", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "document_category": {"id": "310", "name": "TAX_DECLARATION", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "confidence_summary": {"value_formatted": "99%", "value": 0.*********, "level": "high"}, "confidence": 0.*********, "page_objects": [{"uuid": "e55ebd1e-3354-43ec-8fbf-825530f71bba", "key": "p2_income_employed_main", "title": "P2 Einkommen Haupterwerb", "titles": {"de": "P2 Einkommen Haupterwerb", "en": "P2 Main Income", "fr": "P2 Revenu net dépendante", "it": "[P2 Main Income]"}, "visible": true, "value": "CHF 128'991", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 360, "left": 2640, "right": 2796, "bottom": 427}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "63890512-dce5-4072-bbe5-54a67036feb8", "key": "fh_landwirtschaft", "title": "Landwirtschaft", "titles": {"de": "Landwirtschaft", "en": "Landwirtschaft", "fr": "Landwirtschaft", "it": "Landwirtschaft"}, "visible": false, "value": "{\"value\": \"Landwirtschaft\", \"value_found\": \"Landwirtschaft\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\\n2: Inklusive Ertr\\u00e4ge aus                 2.1  Haupterwerb           Ehemann / Einzelperson / P1                     Hilfsblatt   120\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 719, "left": 1343, "right": 1605, "bottom": 775}, "page_number": 1, "confidence": 0.04614436998963356, "confidence_summary": {"value_formatted": "5%", "value": 0.04614436998963356, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7ef6e61c-a037-4531-b58b-6aed24336d93", "key": "income_real_estate_net_primary", "title": "Primärliegenschaft Ertrag netto", "titles": {"de": "Primärliegenschaft Ertrag netto", "en": "Primary Self used Real Estate Income net", "fr": "[Primary Self used Real Estate Income net]", "it": "[Primary Self used Real Estate Income net]"}, "visible": true, "value": "CHF 20'560", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3588, "left": 2662, "right": 2802, "bottom": 3642}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d18b2c4d-33aa-45e9-a7ba-8bc55a04c245", "key": "income_portfolio", "title": "Wertschriftenerträge", "titles": {"de": "Wertschriftenerträge", "en": "Income Portfolio", "fr": "Revenue mobilier", "it": "[Income Portfolio]"}, "visible": true, "value": "CHF 15", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2341, "left": 2753, "right": 2803, "bottom": 2408}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "5ddb98b4-41d9-43f1-906f-42eb991010e4", "key": "p2_income_child_benefits", "title": "P2 Einkünfte Kinder- und Familienzulagen", "titles": {"de": "P2 Einkünfte Kinder- und Familienzulagen", "en": "P2 Income Child Benefits", "fr": "[P2 Income Child Benefits]", "it": "[P2 Income Child Benefits]"}, "visible": true, "value": "CHF 4'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2190, "left": 2686, "right": 2802, "bottom": 2244}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d95ffed1-f4c1-4889-89f4-c84e8045bdc0", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1194, "right": 1387, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "9bbbda43-492e-4ff0-aa16-14ad22518c3f", "key": "property_imputed_rental_value", "title": "Liegenschaft Eigenmietwert", "titles": {"de": "Liegenschaft Eigenmietwert", "en": "Imputed Rental Value", "fr": "Valeur locative", "it": "Valore locativo"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3111, "left": 1971, "right": 2110, "bottom": 3165}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7f885fe2-f238-4ff3-810d-d1dbdbeb1059", "key": "income_real_estate_gross", "title": "Liegenschaftenertrag brutto", "titles": {"de": "Liegenschaftenertrag brutto", "en": "Income Real Estate gross", "fr": "Revenu brut immobilier", "it": "[Income Real Estate gross]"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3305, "left": 1971, "right": 2110, "bottom": 3358}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "0de8e719-0480-479d-8d61-92a57279f370", "key": "fh_alimente", "title": "Alimente", "titles": {"de": "Alimente", "en": "Alimente", "fr": "Alimente", "it": "Alimente"}, "visible": true, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"Alimente\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.1 und 5.2\\n5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2566, "left": 366, "right": 497, "bottom": 2620}, "page_number": 1, "confidence": 0.16822287440299988, "confidence_summary": {"value_formatted": "17%", "value": 0.16822287440299988, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "635cd23c-1566-46dc-a916-9b4943833a7f", "key": "p1_income_employed_side", "title": "P1 Einkommen Nebenverdienst", "titles": {"de": "P1 Einkommen Nebenverdienst", "en": "P1 Additional Income", "fr": "[P1 Additional Income]", "it": "[P1 Additional Income]"}, "visible": true, "value": "CHF 7'502", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 459, "left": 2688, "right": 2802, "bottom": 514}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fb1d780e-24aa-4f87-8c35-ff266fc37331", "key": "property_maintenance_cost", "title": "Liegenschaft Unterhaltskosten", "titles": {"de": "Liegenschaft Unterhaltskosten", "en": "Maintenance Cost", "fr": "Charges et frais d'entretien d'immeuble", "it": "[Maintenance Cost]"}, "visible": true, "value": "CHF 5'140", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3397, "left": 1996, "right": 2110, "bottom": 3451}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e6cf3875-f643-45ce-ba5f-f9452fd8de7d", "key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "visible": true, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3765, "left": 2640, "right": 2802, "bottom": 3833}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "ee35de32-2f59-4f9d-b046-db06bb5aee29", "key": "fh_arbeitslos", "title": "arbeitslos", "titles": {"de": "arbeitslos", "en": "arbeitslos", "fr": "arbeitslos", "it": "arbeitslos"}, "visible": true, "value": "{\"value\": \"arbeitslos\", \"value_found\": \"Arbeitslos\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"3.3  Erwerbsausfallentsch\\u00e4digungen aus Arbeitslosenversicherung\\n3.3: Direkt ausbezahlte Erwerbs-\\nausfallentsch\\u00e4digungen.                   Ehemann / Einzelperson / P1                                    Bescheinigung 140\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1801, "left": 1441, "right": 1624, "bottom": 1857}, "page_number": 1, "confidence": 0.21327336132526398, "confidence_summary": {"value_formatted": "21%", "value": 0.21327336132526398, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e1569f2a-04aa-4eef-9f58-aa42728c36cb", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "05d008e8-9d82-4c03-8bec-ff45aebf447f", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fdddaa16-fcc1-401e-b4f2-a2357e073558", "key": "fh_gewerbe", "title": "Gewerbe", "titles": {"de": "Gewerbe", "en": "Gewerbe", "fr": "Gewerbe", "it": "Gewerbe"}, "visible": false, "value": "{\"value\": \"<PERSON><PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON>erbe\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Verwaltungsrats- und Vorstands-\\n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \\n2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 652, "left": 1731, "right": 1888, "bottom": 708}, "page_number": 1, "confidence": 0.07572177052497864, "confidence_summary": {"value_formatted": "8%", "value": 0.07572177052497864, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "17c44624-2bec-455a-8d98-2aa121b31d63", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1449, "right": 1593, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "2f642a4f-2c86-4b07-8bfc-80d7ee9cabc3", "key": "p1_income_employed_main", "title": "P1 Einkommen Haupterwerb", "titles": {"de": "P1 Einkommen Haupterwerb", "en": "P1 Main Income", "fr": "P1 Revenu net dépendante", "it": "[P1 Main Income]"}, "visible": true, "value": "CHF 22'506", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 280, "left": 2662, "right": 2802, "bottom": 336}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "3a5d2ebc-fd0a-4208-9bde-4640c0784d07", "key": "p2_income_eo", "title": "P2 Einkünfte Erwerbsausfallentschädigung", "titles": {"de": "P2 Einkünfte Erwerbsausfallentschädigung", "en": "P2 Income EO", "fr": "[P2 Income EO]", "it": "[P2 Income EO]"}, "visible": true, "value": "CHF 4'388", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1939, "left": 2686, "right": 2802, "bottom": 2006}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}], "confidence_info": null, "searchable_pdf": null, "searchable_txt": null}], "document_category": {"id": "210", "name": "PASSPORT_CH", "de": "Pass CH", "en": "Passport CH", "fr": "Passeport CH", "it": "Passaporto CH", "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}, "aggregated_objects": [{"uuid": "07f9566f-bff0-40b3-9066-d46f9d9f37f2", "key": "identity_cover_misc", "title": "identity_cover_misc", "titles": {"de": "identity_cover_misc", "en": "identity_cover_misc", "fr": "identity_cover_misc", "it": "identity_cover_misc"}, "visible": true, "value": null, "type": "IMAGE", "bbox": {"ref_width": 661, "ref_height": 1109, "top": 384, "left": 146, "right": 649, "bottom": 745}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}, {"uuid": "a9058558-9d58-446a-a343-064cacce1bf0", "key": "identity_swiss_passport_first", "title": "identity_swiss_passport_first", "titles": {"de": "identity_swiss_passport_first", "en": "identity_swiss_passport_first", "fr": "identity_swiss_passport_first", "it": "identity_swiss_passport_first"}, "visible": true, "value": null, "type": "IMAGE", "bbox": {"ref_width": 661, "ref_height": 1109, "top": 744, "left": 151, "right": 637, "bottom": 1105}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}, {"uuid": "c3ec1a98-0059-45c1-a2c7-10a979cd2282", "key": "firstname", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Firstname", "fr": "[Firstname]", "it": "[Firstname]"}, "visible": true, "value": "<PERSON>»", "type": "STRING", "bbox": {"ref_width": 2379, "ref_height": 3990, "top": 3041, "left": 1080, "right": 1414, "bottom": 3129}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}], "filename": "210 Pass CH Andreas <PERSON>».pdf", "suffix": "<PERSON>»", "title_elements": null, "confidence_summary": {"value_formatted": "99%", "value": 0.*********, "level": "high"}, "confidence_info": null, "confidence": 0.*********, "formatted_title": "210 Pass CH Andreas <PERSON>»", "document_category_translated": "Pass CH", "title_custom": "210 Pass CH Andreas <PERSON>»"}}]