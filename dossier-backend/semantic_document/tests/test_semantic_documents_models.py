import pytest

from dossier.fakes import load_initial_document_categories
from dossier.models import (
    <PERSON>esta<PERSON><PERSON><PERSON><PERSON>,
    Dossie<PERSON>,
    Account,
)
from semantic_document import helpers
from semantic_document.models import (
    SemanticDocument,
    AssignedRealEstatePropertySemanticDocument,
)


@pytest.fixture
def dossier():
    account = Account.objects.create(key="test", name="Test")
    dossier = Dossier.objects.create(name="Testdossier", account=account)
    return dossier


@pytest.fixture
def semantic_document(dossier) -> SemanticDocument:
    document_categories, _, _, _ = load_initial_document_categories(
        account=dossier.account
    )
    return helpers.create_unnamed_semantic_document(dossier, {})


@pytest.fixture
def realestate_property(dossier):
    return RealestateProperty.objects.create(dossier=dossier, key="unique_key")


@pytest.mark.django_db
def test_assigned_realestate_property_semantic_document_file_creation(
    semantic_document, realestate_property
):
    relationship = AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=semantic_document, realestate_property=realestate_property
    )
    assert relationship.semantic_document == semantic_document
    assert relationship.realestate_property == realestate_property

    # Test its possible to lookup from the realestate property
    assert (
        RealestateProperty.objects.filter(
            assignedrealestatepropertysemanticdocument__semantic_document=semantic_document
        ).first()
        == realestate_property
    )

    # Test its possible to do a reverse lookup from the semantic document
    assert (
        SemanticDocument.objects.filter(
            assignedrealestatepropertysemanticdocument__realestate_property=realestate_property
        ).first()
        == semantic_document
    )
