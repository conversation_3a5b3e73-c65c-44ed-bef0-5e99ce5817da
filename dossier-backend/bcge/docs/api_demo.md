1. Introduction
2. Authentication
3. Dossier Operations
   3.1. Creating a Dossier
   3.2. Updating a Dossier
   3.3. Deleting a Dossier
4. File Operations
   4.1. Uploading a File
   4.2. Checking File Existence
5. Semantic Document Operations
   5.1. Fetching Semantic Documents
   5.2. Updating Semantic Documents
   5.3. Exporting Semantic Documents
   5.4. Deleting and Restoring Semantic Documents
6. Original File Operations
   6.1. Fetching Original Files
   6.2. Exporting Original Files
7. Error Handling and Best Practices

# Hypodossier API Demo

## 1. Introduction

The Hypodossier API allows you to manage dossiers, files, and semantic documents.

## 2. Authentication

The Hypodossier API uses JWT authentication. To obtain an access token, make a POST request to the authentication endpoint:

```bash
curl -X POST "https://auth.bcge.test.hypodossier.ch/realms/dev-hypodossier/protocol/openid-connect/token" \
     -d "client_id=your_client_id" \
     -d "client_secret=your_client_secret" \
     -d "grant_type=client_credentials"
```

The response will contain an `access_token`, which you'll use in subsequent requests.

## 3. Dossier Operations

### 3.1. Creating a Dossier

To create a new dossier, make a POST request to the `/partner/bcge/api/v1/dossier` endpoint:

```bash
curl -X POST "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "external_dossier_id": "your_external_dossier_id",
       "name": "Demo Dossier",
       "lang": "fr"
     }'
```

### 3.2. Updating a Dossier

To update a dossier, make a PATCH request to the `/partner/bcge/api/v1/dossier/{external_dossier_id}` endpoint:

```bash
curl -X PATCH "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "external_dossier_id": "new_external_dossier_id",
       "name": "Updated Dossier Name"
     }'
```

### 3.3. Deleting a Dossier

To delete a dossier, make a DELETE request to the `/partner/bcge/api/v1/dossier/{external_dossier_id}` endpoint:

```bash
curl -X DELETE "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 4. File Operations

### 4.1. Uploading a File

To upload a file to a dossier, make a POST request to the `/partner/bcge/api/v1/dossier/{external_dossier_id}/original-files` endpoint:

```bash
curl -X POST "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id/original-files" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -F "file=@/path/to/your/file.pdf"
```

### 4.2. Checking File Existence

To check if a file with a specific name exists in a dossier, make a GET request to the `/partner/bcge/api/v1/dossier/{external_dossier_id}/filename/{original_filename}` endpoint:

```bash
curl -X GET "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id/filename/your_filename.pdf" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 5. Semantic Document Operations

### 5.1. Fetching Semantic Documents

To fetch semantic documents for a dossier, make a GET request to the `/partner/bcge/api/v1/dossier/{external_dossier_id}/semantic-documents` endpoint:

```bash
curl -X GET "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id/semantic-documents" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 5.2. Updating Semantic Documents

To update a semantic document, make a PATCH request to the `/partner/bcge/api/v1/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}` endpoint:

```bash
curl -X PATCH "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id/semantic-documents/semantic_document_uuid" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "external_semantic_document_id": "new_external_id",
       "access_mode": "read_only"
     }'
```

### 5.3. Exporting Semantic Documents

To export a semantic document, first set it to ready for export:

```bash
curl -X POST "http://localhost:8000/partner/bcge/api/v1/export/dossier/your_external_dossier_id/semantic-documents/semantic_document_uuid/set-state-ready-for-export" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

Then, check the export status:

```bash
curl -X GET "http://localhost:8000/partner/bcge/api/v1/export/your_external_dossier_id/semantic-documents-available" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

# We can also get a list of all semantic documents that are ready for export for a given account

```bash
curl -X GET "http://localhost:8000/partner/bcge/api/v1/export/all-semantic-documents-availabl" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```


### 5.4. Deleting and Restoring Semantic Documents

To delete a semantic document:

```bash
curl -X DELETE "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id/semantic-document/semantic_document_uuid" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

To restore a deleted semantic document:

```bash
curl -X PUT "https://dms.bcgeevo.test.hypodossier.ch/partner/bcge/api/v1/your_external_dossier_id/semantic-document/semantic_document_uuid/restore" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 6. Original File Operations

### 6.1. Fetching Original Files

To fetch original files, make a GET request to the `/partner/bcge/api/v1/original-files/` endpoint:

```bash
curl -X GET "http://localhost:8000/partner/bcge/api/v1/original-files/" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 6.2. Exporting Original Files

To export an original file, make a GET request to the `/partner/bcge/api/v1/export/original-file/{original_file_uuid}` endpoint:

```bash
curl -X GET "http://localhost:8000/partner/bcge/api/v1/export/original-file/original_file_uuid" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```