import json
import os
import uuid
from pathlib import Path
from typing import List, Optional

import pytest
from django.conf import settings
from django.urls import reverse

from faker import Faker
from pytest_mock import MockerFixture

from bcge.schemas import schemas as bcge_schemas, schemas
from bcge.schemas.schemas import AccountName
from core.authentication import AuthenticatedClient
from dossier.fakes import add_some_fake_semantic_documents
from dossier.management.commands.dossier_event_consumer_v2 import (
    set_semantic_document_export_done,
)

from dossier.models import (
    Account,
    DocumentCategory,
    JWK,
    Dossier,
)
from dossier.services import create_expiration_date
from projectconfig.jwk import load_jwk_from_env
from bcge.factories import BCGEAccountFactoryFaker
import jwt
from jwcrypto import jwk
from bcge.tests.data import DATA_PATH
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
    STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    SemanticDocumentState,
)
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine, Status
from workers import schemas as worker_schemas
from workers.workers import process_semantic_dossier_pdf_request
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401


@pytest.fixture(scope="session")
def faker():
    return Faker(locale="de_CH")


@pytest.fixture
def bcge_account():
    account, _ = Account.objects.update_or_create(
        defaults=dict(
            name="bcge development account",
            default_bucket_name="dms-default-bucket",
            dmf_endpoint="https://www.localhost",
        ),
        key=AccountName.bcged.value,
    )

    # Ensure that a semantic_document state machine is created
    created_objects = create_semantic_document_state_machine()

    semantic_document_state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = (
        semantic_document_state_machine
    )

    # Load the dossier state machine configuration
    update_state_machine(
        Path(__file__).parent.parent
        / "statemgmt/configurations/default/default_state_machine_2025_02_21.json",
        "Dossier Default",
    )

    account.active_work_status_state_machine = StateMachine.objects.get(
        name="Dossier Default"
    )

    account.save()

    account.refresh_from_db()
    return account


@pytest.fixture(scope="session")
def mock_jwks_public_private():
    # JWKS with both public and private keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example.json")
    ).model_dump()


@pytest.fixture
def mock_jwks_public():
    # JWKS with only public keys
    return load_jwk_from_env(
        jwk_path=os.path.join(DATA_PATH, "jwks-example-public.json")
    ).model_dump(exclude_unset=True)


@pytest.fixture
def set_bcge_JWK(bcge_account, mock_jwks_public):
    # Set public key used for bcge authentication
    return JWK.objects.create(jwk=mock_jwks_public["keys"][1], account=bcge_account)


@pytest.fixture(scope="session")
def token_data(faker):
    return {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-bcge-first service-bcge-last",
        "given_name": "service-bcge-first",
        "family_name": "service-bcge-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        # "external_dossier_id": schemas.DossierName(
        #     faker.sentence()
        # )
        # during dossier creation via API parameter
        "user_roles": ["api_role"],
        "account_key": AccountName.bcged.value,
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }


@pytest.fixture(scope="session")
def mock_token(mock_jwks_public_private, token_data):
    # need a PEM-formatted key
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    return jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,
        algorithm="RS256",
    )


@pytest.fixture
def bcge_account_factory(bcge_account):
    return BCGEAccountFactoryFaker(
        account=bcge_account, default_bucket_name="dms-default-bucket"
    )


@pytest.fixture
def document_categories(bcge_account_factory) -> List[DocumentCategory]:
    doc_cats = bcge_account_factory.load_initial_document_categories()
    return doc_cats


@pytest.fixture(scope="session")
def bcge_authenticated_client(mock_token):
    return AuthenticatedClient(mock_token)


@pytest.fixture(scope="session")
def bcge_miss_signed_authenticated_client(mock_jwks_public_private, token_data):
    """Test auth failure using key signed by bcge"""
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][0]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    wrong_token = jwt.encode(
        payload={"aud": "account", "user_roles": [settings.API_ROLE], **token_data},
        key=pem,  # use wrong key, key [0] from bcge
        algorithm="RS256",
    )
    return AuthenticatedClient(wrong_token)


@pytest.fixture()
def prepare_data_export(
    mocked_get_dossier,  # noqa: W0404
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):

    external_dossier_id = str(uuid.uuid4())

    state_machine = StateMachine.objects.get(
        name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS
    )
    assert (
        bcge_account.active_semantic_document_work_status_state_machine == state_machine
    )

    api_bcge_create_dossier(bcge_authenticated_client, external_dossier_id)

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier,
        num_docs=1,
        max_pages=10,
        min_num_pages=10,
        no_page_objects_per_page=10,
        valid_document_category_keys=["PASSPORT_CH"],
        allow_empty_docs=False,
        title_suffix="custom_suffix_for_test",
    )
    assert len(semantic_documents) == 1

    # Pick a first one
    semantic_document = semantic_documents[0]

    in_front_office_state = Status.objects.get(
        key=SemanticDocumentState.IN_FRONT_OFFICE.value, state_machine=state_machine
    )

    assert semantic_document.work_status == in_front_office_state

    def mock_publish_side_effect(*args, **kwargs):
        request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
            kwargs["message"]
        )

        # Process the pdf generation
        # Returns json dump in format SemanticDocumentPDFResponseV1
        process_semantic_document_response = process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request
        )

        # which is then collected by dossier events consumer
        # and sets event as done
        set_semantic_document_export_done(process_semantic_document_response)

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect,
    )

    return external_dossier_id, semantic_document, mock_dispatch_publish_request


def api_bcge_create_dossier(
    bcge_authenticated_client,
    external_dossier_id: str,
    name: str = "Test Dossier",
    lang: str = "fr",
    expected_response_code: Optional[int] = 201,
) -> bcge_schemas.Dossier:
    response = bcge_authenticated_client.post(
        path=reverse("bcge-api:create-dossier"),
        data=schemas.CreateDossier(
            name=name,
            external_dossier_id=external_dossier_id,
            lang=lang,
        ).model_dump_json(),
        content_type="application/json",
    )
    if expected_response_code:
        if not response.status_code == expected_response_code:
            raise Exception(
                f"wrong response code. Expected {expected_response_code} but got {response.status_code}"
            )

    return response.content
