

1. Modify / extend state management
    1. Dump prod db and restore locally. Then run commands to dump state management export
   2. 





----------------------------------------------------

# BEKB Core Mortage State Machine

DocCheck

Document Category

Sync:
doccheck.models.DocumentCategory must be synced with dossier.models.DocumentCategory (otherwise can not be selected as condition)
python manage.py export_import_doccheck sync-doccat-names-from-account bekbe bekbe

Export:
export
python manage.py doccheck export-doccat-names-to-json doccheck/configurations/document_category/bekb_doccat_names_240404.json bekbe
python manage.py doccheck import-doccat-names-from-json doccheck/configurations/document_category/bekb_doccat_names_240404.json bekbe
python manage.py doccheck sync-doccat-names-from-account bekbe bekbe

python manage.py doccheck sync-businesscase-types-from-account bekbe bekbe



To upgrade BEKB account to new version with new business cases and doccheck:
python manage.py bekbconf update-240407-account-for-new-businesscases default -dry_run=False

python manage.py bekbconf update-240407-account-for-new-businesscases bekbz -dry_run=False

Export current doccheck:
python manage.py export_import_doccheck export-doccheck-to-json doccheck/configurations/bekb_doccheck_2405022.json bekbp_doccheck


Export current state managemenet
python manage.py load_statemgmt dump-state-machine-local statemgmt/configurations/bekb/bekb_state_machine_20240420.json 'bekbz'




To deploy:

On test systems:
   python manage.py update_dossier_categories bekbe bekb/data/document_categories/DocumentCategory-2024-05-07-bekbp-before-upgrade.json

Everywhere:
   python manage.py bekbconf update-240407-account-for-new-businesscases bekbe -dry_run=False
   python manage.py bekbconf update-240407-account-for-new-businesscases bekbp -dry_run=False

On test systems: 

python manage.py load_bekb_data load-account bekbe
python manage.py load_bekb_data load-dossiers-all bekbe

python manage.py copy_dossier source_dossier_uuid new_ext_id

Calculate what will be merged: 
python manage.py bekb_semdoc_grouping_for_archive calculate e81ca2be-f218-428a-9542-b2defe6c6834
python manage.py bekb_semdoc_grouping_for_archive calculate e81ca2be-f218-428a-9542-b2defe6c6834

Create bekb export

python manage.py process_ready_for_export_dossier now bekbe 2835ffcd-aa1f-4895-9a4b-955b1970c9f3

python manage.py process_ready_for_export_dossier now bekbe DOSSIER_UUID

Show index file:

python manage.py archive show-index-file bekbe a22ac946-bf74-440e-a4ff-2586c39258f8


# BEKB FIPLA State Machine
## FIPLA Export local
python manage.py load_statemgmt dump-state-machine-local statemgmt/configurations/bekb/fipla/bekbfipla_state_machine_20250103.json 'Dossier State Machine BEKB Fipla'

## Fipla Import local
python manage.py load_statemgmt update-state-machine-local statemgmt/configurations/bekb/fipla/bekbfipla_state_machine_20250103.json 'Dossier State Machine BEKB Fipla'


