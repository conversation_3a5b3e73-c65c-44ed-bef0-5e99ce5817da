import json
from datetime import datetime, timezone
from typing import Final
from uuid import uuid4

import jwt
import structlog
from django.conf import settings
from jwt import <PERSON>yJW<PERSON>
from ninja import Router
from ninja.errors import HttpError

from dossier.models import Account
from multi_tenancy.authentication import Multi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uth
from multi_tenancy.schemas import (
    AccountTokenResponse,
    ErrorResponse,
    AccessibleAccountsResponse,
)
from projectconfig.authentication import JWTAuthRequest

logger = structlog.get_logger(__name__)

router = Router()

TOKEN_ALGORITHM: Final[str] = "RS256"


def validate_account_access(account_key: str, account_keys: list[str]) -> None:
    """Validate user has access to the requested account.

    Args:
        account_key: The requested account key
        account_keys: List of account keys the user has access to

    Raises:
        HttpError: If validation fails
    """
    if account_key not in account_keys:
        logger.warning(
            "Account access denied",
            requested_account=account_key,
            available_accounts=account_keys,
        )
        # Note: should return a 403, but the frontend relies on hard reloads for every 403 it receives, need to fix that first
        raise HttpError(404, "You don't have access to this account.")

    existing_account = Account.objects.filter(key=account_key).exists()
    if not existing_account:
        logger.warning("Account does not exist", account_key=account_key)
        raise HttpError(500, "Please contact your system administrator.")


@router.post(
    "/{account_key}/token",
    response={
        200: AccountTokenResponse,
        404: ErrorResponse,
        500: ErrorResponse,
    },
    auth=MultiTenancyJWTAuth(),
    url_name="exchange-keycloak-with-instance-token",
)
def exchange_token(request: JWTAuthRequest, account_key: str) -> AccountTokenResponse:
    """Exchange multi-account token for a single account token.

    Takes a token signed with Keycloak keys and returns a new token signed with instance key
    for a specific account. Accepts either a list of account_keys or a single account_key
    in the JWT.

    Args:
        request: The authenticated request containing the original JWT
        account_key: The account key to generate token for

    Returns:
        AccountTokenResponse containing the new token

    Raises:
        HttpError: If account access is invalid
    """
    account_keys = []
    if request.jwt.get("account_keys", None):
        account_keys = request.jwt.pop("account_keys")
    elif request.jwt.get("account_key", None):
        account_keys = [request.jwt.get("account_key")]
    validate_account_access(account_key, account_keys)

    try:
        jwk_dict = json.loads(settings.INSTANCE_SIGNING_KEY)
        jwk = PyJWK(jwk_dict)

        # Create new token payload
        old_payload = request.jwt
        new_payload = {
            **old_payload,
            "iss": account_key,
            "iat": int(datetime.now(timezone.utc).timestamp()),
            "jti": str(uuid4()),
            "account_key": account_key,
            "kid": jwk_dict.get("kid", str(uuid4())),
        }
        new_token = jwt.encode(
            payload=new_payload,
            key=jwk,
            algorithm=TOKEN_ALGORITHM,
        )

        return AccountTokenResponse(token=new_token)

    except Exception:
        logger.exception(
            "Failed to generate account token", account_key=account_key, exc_info=True
        )
        raise HttpError(500, "Failed to generate account token")


@router.get(
    "/accessible",
    response=AccessibleAccountsResponse,
    auth=MultiTenancyJWTAuth(),
    url_name="get-accessible-account-metadata",
)
def get_accessible_account_metadata(
    request: JWTAuthRequest,
) -> AccessibleAccountsResponse:
    """Get metadata for accounts that the user has access to.

    Retrieves metadata (like name) for each accessible account based on the account keys
    in the user's JWT token. Only returns data for accounts that exist in the system.

    Args:
        request: The authenticated request containing the JWT with account_keys

    Returns:
        AccessibleAccountsResponse containing metadata for each accessible account,
        keyed by account key
    """
    account_keys = request.jwt.get("account_keys", [])

    accounts_dict = {
        account.key: {"name": account.name}
        for account in Account.objects.filter(key__in=account_keys)
    }

    return AccessibleAccountsResponse(accounts=accounts_dict)
