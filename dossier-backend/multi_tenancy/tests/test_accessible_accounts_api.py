import pytest
from django.urls import reverse

from core.authentication import create_token

pytestmark = pytest.mark.django_db


def test_get_accessible_accounts(client):
    token = create_token(
        given_name="Test",
        family_name="User",
        email="<EMAIL>",
        extra_fields={"account_keys": ["default", "account2", "nonexistent"]},
        signed_with_keycloak_key=True,
    )

    response = client.get(
        reverse("api:get-accessible-account-metadata"),
        HTTP_AUTHORIZATION=f"Bearer {token}",
    )

    assert response.status_code == 200
    data = response.json()

    assert len(data["accounts"]) == 2
    assert data["accounts"]["default"]["name"] == "HyppoDossier account"
    assert data["accounts"]["account2"]["name"] == "Account 2"
    assert "account3" not in data["accounts"]
    assert "nonexistent" not in data["accounts"]


def test_get_accessible_accounts_no_accounts(client):
    token = create_token(
        given_name="Test",
        family_name="User",
        email="<EMAIL>",
        extra_fields={"account_keys": []},
        signed_with_keycloak_key=True,
    )

    response = client.get(
        reverse("api:get-accessible-account-metadata"),
        HTTP_AUTHORIZATION=f"Bearer {token}",
    )

    assert response.status_code == 200
    data = response.json()
    assert data["accounts"] == {}


def test_get_accessible_accounts_unauthorized(client):
    response = client.get(
        reverse("api:get-accessible-account-metadata"),
        HTTP_AUTHORIZATION="Bearer invalid",
    )

    assert response.status_code == 401
