
Set this flag to true for Silk performance profiling:

# Enable Silk Django SQL profiling
Add this to .env file:
```
DEBUG_ENABLE_SILK=True
```

python manage.py makemigrations silk
python manage.py migrate silk

(if you do not apply these you get django.db.utils.ProgrammingError: relation "silk_request" does not exist LINE 1: INSERT INTO "silk_request")

# Access Silk Reports

http://localhost:8000/silk/

# Clear profiling data

http://localhost:8000/silk/cleardb/