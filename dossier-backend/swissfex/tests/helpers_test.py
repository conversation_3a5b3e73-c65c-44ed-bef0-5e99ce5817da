import uuid
from typing import List

from django.urls import reverse
from pydantic import TypeAdapter

from swissfex.schemas import schemas


def swissfex_api_post_create_dossier(
    authenticated_client,
    name="Test Dossier",
    external_dossier_id: str = str(uuid.uuid4()),
    language: str = "de",
):
    response = authenticated_client.post(
        path=reverse("swissfex-api:create-dossier"),
        data=schemas.CreateDossier(
            name=name, external_dossier_id=external_dossier_id, language=language
        ).model_dump_json(),
        content_type="application/json",
    )
    return response, external_dossier_id


def swissfex_api_post_add_original_file(
    authenticated_client, external_dossier_id, data
):
    response = authenticated_client.post(
        reverse(
            "swissfex-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=data,
        format="multipart",
    )
    return response


def swissfex_api_get_semantic_documents(
    authenticated_client, external_dossier_id, show_pages: bool = False
):
    path_base = reverse(
        "swissfex-api:semantic-documents",
        kwargs={"external_dossier_id": external_dossier_id},
    )
    path_suffix = "?show_pages=true" if show_pages else ""
    response = authenticated_client.get(path=path_base + path_suffix)
    parsed = None
    if response.status_code == 200:
        parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(
            response.content
        )
    return response, parsed
