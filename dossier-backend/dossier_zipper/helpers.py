import concurrent
from typing import Optional

from pandas import <PERSON>F<PERSON><PERSON>, ExcelWriter

from dossier.schemas import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

rotate_params = {90: 1, 180: 2, 270: 3}
executor = concurrent.futures.ThreadPoolExecutor()


def rotate_pdf(pdf_page, rotate_angel: Optional[int]):
    if not rotate_angel:
        return pdf_page

    if rotate_angel in [0, 360]:
        return pdf_page

    return pdf_page.rotateClockwise(rotate_angel)


def add_document_extraction_excel(semantic_dossier: SemanticDossier, package_path):
    dest_file_path = package_path / "000 Hypodossier Datenextraktion.xlsx"

    sheet_name = "Extraktion"

    docs = []
    all_data = []
    for semantic_document in semantic_dossier.semantic_documents:
        doc = {
            "title": semantic_document.formatted_title,
            "filename": semantic_document.filename,
            "num pages": len(semantic_document.semantic_pages),
        }
        docs.append(doc)

        for semantic_page in semantic_document.semantic_pages:
            # page_index = page_object.page_number
            #
            # semantic_page = semantic_document.semantic_pages[page_index]
            # filename_old = semantic_page.source_file_path

            for page_object in semantic_page.page_objects:
                if page_object.visible:
                    data = {
                        "Dateiname": semantic_document.filename,
                        "Seite": page_object.page_number + 1,
                        "Attribut": page_object.title,
                        "Wert": page_object.value,
                        "Typ": page_object.type,
                        "Zuverlässigkeit Extraktion": page_object.confidence_summary.value_formatted,
                        "Seite Breite": page_object.bbox.ref_width,
                        "Seite Höhe": page_object.bbox.ref_height,
                        "Seite Pos links": page_object.bbox.left,
                        "Seite Pos rechts": page_object.bbox.right,
                        "Seite Pos oben": page_object.bbox.top,
                        "Seite Pos unten": page_object.bbox.bottom,
                        # 'Dateiname Original': filename_old,
                        # 'Seite Original': semantic_page.source_page_number,
                        "Dokumenten-Kategorie": semantic_document.document_category.name,
                        "Zuverlässigkeit Dokument": semantic_document.confidence_summary.value_formatted,
                        "Winkel drehen": semantic_page.rotation_angle,
                        # 'Seiten-Kategorie': semantic_page.page_category.name,
                        # 'Zuverlässigkeit Seite': semantic_page.confidence_summary.value_formatted
                    }
                    all_data.append(data)

    # df = DataFrame({'States': ['California', 'Florida', 'Montana', 'Colorodo', 'Washington', 'Virginia'],
    #                    'Capitals': ['Sacramento', 'Tallahassee', 'Helena', 'Denver', 'Olympia', 'Richmond'],
    #                    'Population': ['508529', '193551', '32315', '619968', '52555', '227032']})
    df = DataFrame(data=all_data)
    # df.to_excel(dest_file_path, sheet_name='Dokumentenübersicht')

    # Order the columns if necessary.
    #    df = df[['Rank', 'Country', 'Population']]

    # Create a Pandas Excel writer using XlsxWriter as the engine.
    writer = ExcelWriter(dest_file_path, engine="xlsxwriter")

    # Write the dataframe data to XlsxWriter. Turn off the default header and
    # index and skip one row to allow us to insert a user defined header.
    df.to_excel(writer, sheet_name=sheet_name, startrow=0, header=True, index=False)

    # Get the xlsxwriter workbook and worksheet objects.
    workbook = writer.book
    worksheet = writer.sheets[sheet_name]
    workbook.add_format({"text_wrap": True})

    # Get the dimensions of the dataframe.
    (max_row, max_col) = df.shape

    # Create a list of column headers, to use in add_table().
    column_settings = [{"header": column} for column in df.columns]

    # Add the Excel table structure. Pandas will add the data.
    worksheet.add_table(0, 0, max_row, max_col - 1, {"columns": column_settings})

    # Make the columns wider for clarity.
    # Make the columns wider for clarity.
    worksheet.set_column(0, 0, 64)
    worksheet.set_column(2, 3, 24)  # name, value
    worksheet.set_column(12, 12, 24)
    # worksheet.set_column(14, 14, 24)
    worksheet.set_column(16, 16, 24)

    # worksheet.set_column(2, 0, 48)
    # worksheet.set_column(3, 0, 48)
    # worksheet.set_column(2, max_col - 1,24)

    writer.close()
