import pytest
from asgiref.sync import sync_to_async

from dossier_zipper.schemas import DossierZipRequestV1, DossierZipResponseV1
from dossier_zipper.workers import process_dossier_zip_request


@pytest.fixture
def mocked_get_dossier(mocker):
    async def _mocked_get_dossier(request: DossierZipRequestV1) -> DossierZipResponseV1:
        return DossierZipResponseV1.model_validate_json(
            await sync_to_async(process_dossier_zip_request)(
                dossier_zip_request=request
            )
        )

    mock_external_function = mocker.patch(
        "dossier_zipper.dossier_zipper_api.get_dossier"
    )
    mock_external_function.side_effect = _mocked_get_dossier

    return mock_external_function
