from pathlib import Path

import djclick as click

from doccheck.export import export_doccheck, import_doccheck


@click.group()
def cli():
    pass


@cli.command()
@click.argument("dest_path")
@click.argument("doc_check_key")
def export_doccheck_to_json(dest_path: str, doc_check_key: str):
    """
    Example:

        python manage.py export_import_doccheck export-doccheck-to-json doccheck/configurations/bekb_doccheck_240507.json bekbz

    @param dest_path:
    @param doc_check_key:
    @return:
    """
    export_doccheck(doc_check_key, Path(dest_path))


@cli.command()
@click.argument("source_path")
@click.argument("doc_check_key")
def import_doccheck_from_json(source_path: str, doc_check_key: str = None):
    """
    Example:

        python manage.py export_import_doccheck import-doccheck-from-json doccheck/configurations/bekb_doccheck_240522.json default
    @param source_path:
    @param doc_check_key:
    @return:
    """
    import_doccheck(Path(source_path), doc_check_key)
