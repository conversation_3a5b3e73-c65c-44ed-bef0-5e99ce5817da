from pathlib import Path

import djclick as click

from doccheck import export
from doccheck.export import import_doccat_names, export_doccat_names


@click.group()
def cli():
    pass


@cli.command()
@click.argument("source_path")
@click.argument("doc_check_key")
def import_doccat_names_from_json(source_path: str, doc_check_key: str):
    """
    Example

    python manage.py doccheck import-doccat-names-from-json doccheck/configurations/document_category/bekb_doccat_names_240404.json bekbe

    @param source_path:
    @param doc_check_key:
    @return:
    """
    import_doccat_names(Path(source_path), doc_check_key)


@cli.command()
@click.argument("dest_path")
@click.argument("doc_check_key")
def export_doccat_names_to_json(dest_path: str, doc_check_key: str):
    """
    Example:

        python manage.py doccheck export-doccat-names-to-json doccheck/configurations/document_category/bekb_doccat_names_240404.json bekbe

    @param dest_path:
    @param doc_check_key:
    @return:
    """
    export_doccat_names(doc_check_key, Path(dest_path))


@cli.command()
@click.argument("account_key")
@click.argument("doc_check_key")
def sync_doccat_names_from_account(account_key: str, doc_check_key: str):
    """
    Copy all existing doc cats into doccheck

    python manage.py doccheck sync-doccat-names-from-account bekbe bekbe

    @param account_key:
    @param doc_check_key:
    @return:
    """
    export.sync_doccat_names_from_account(account_key, doc_check_key)


@cli.command()
@click.argument("account_key")
@click.argument("doc_check_key")
def sync_businesscase_types_from_account(account_key: str, doc_check_key):
    """
    Copy all existing business case types of an account into doccheck

    python manage.py doccheck sync-businesscase-types-from-account bekbe bekbe

    @param account_key:
    @param doc_check_key:
    @return:
    """
    export.sync_businesscase_types_from_account(account_key, doc_check_key)
