# Document Universe

## Integration Sketch

![Integration Sketch](./docs/document%20browser%20integration.png)

## Projects

- docs: architecture documentation
- dossier-backend: all backend related django stuff
- dossier-client: uses to dossier backend apis to crud dossier

- sample-spa: Simple OIDC integration with keycloak based on keycloak.js

## Some external documentations and modules

- Caddy Reverse Proxy: https://caddyserver.com/
- Keycloak: https://www.keycloak.org/documentation
- Documentation of the javascript Adapter keycloak.js: https://www.keycloak.org/docs/latest/securing_apps/#_javascript_adapter
- Oauth2Proxy: https://oauth2-proxy.github.io/oauth2-proxy/
- Django Ninja Framework: https://django-ninja.rest-framework.com/


## Clone repo with submodule

```shell
git clone --recurse-submodules clone_link
```


## Add submodules after cloning repository

```shell
git submodule update --init
```
## Initialize, fetch and checkout any nested submodules

```shell
git submodule update --init --recursive
```

## Update git submodule up to the latest commit

```shell
git submodule update --remote
```

## Run project with docker-compose

1. Clone git submodule

### Run Backend

1. `cd dossier-backend/`
2. Create `.env` file in the document-universe according to the `.env-example`
3. Create database and user for it based on credential from `.env` file
4. `docker-compose up -d`

### Run manager frontend

1. `cd dossier-manager-frontend/`
2. Create `.env` file in the dossier-manager-frontend according to the `dossier-manager-frontend/.env-example`
3. Set up the environment variable globally: `export NPM_TOKEN=YOUR_NPM_AUTH_TOKEN`
4. `docker-compose -f docker-compose.local.dev.yml build dossier-frontend &&  docker-compose -f docker-compose.local.dev.yml up dossier-frontend`
5. Open [https://service.hypo.duckdns.org](https://service.hypo.duckdns.org)

## Django Tests

`
$ docker-compose -f docker-compose.yml -f docker-compose.test.yml run django

## Cypress Tests

```shell
docker-compose -f docker-compose.yml -f docker-compose.cypress.yml up cypress-run
```

### Using the interactive UI

xhost + isn't really save. Is there another solution?

```shell
xhost +
docker-compose -f docker-compose.yml -f docker-compose.cypress.yml up cypress-gui
xhost -
```




### How to run django inside dms with the hot-reload function

```shell
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build dms && docker-compose -f docker-compose.yml -f docker-compose.dev.yml up dms
```

### How to delete the expired dossiers?

```shell
docker-compose exec dms python manage.py delete_expired_dossier
```

### How to add a new django superuser?
```shell
docker-compose exec demo-dms_dms DJANGO_SUPERUSER_PASSWORD=somepassword python manage.py createsuperuser --no-input --username=joe  --email=<EMAIL>

```

#### The `--without_delete` flag does not allow deleting the dossiers found in the `delete_expired_dossier` django command

```shell
docker-compose exec dms python manage.py delete_expired_dossier --without_delete
```

# Keycloak

## Export keylock realm
After executing the following command and pressing Ctrl-C the keycloak/realm-export.json file should be updated.

```shell
docker-compose exec keycloak /opt/jboss/keycloak/bin/standalone.sh \
-Dkeycloak.migration.action=export \
-Djboss.socket.binding.port-offset=100 \
-Dkeycloak.migration.provider=singleFile \
-Dkeycloak.migration.realmName=dev-hypodossier \
-Dkeycloak.migration.usersExportStrategy=REALM_FILE \
-Dkeycloak.migration.file=/config/realm-export.json
```

### How to change the background image to keycloak login?
Replace the image: `document-universe/keycloak/themes/hypodossier/login/resources/img/keycloak-bg.png` and restart the keycloak container. The Background image will automatically change after restarting. If the image did not change after the container is restarted, change the theme in the "Login Theme" section with the one named "keycloak".

### How to add account_name to JWT

1. Create a keylock user.
2. Create a keylock group.
3. In the created group, go to the “Attributes” tab and add the “account_name” key (without quotes “ “ ) to the “Key” column.
4. After the “account_name” is added to the Key column, any value should be added to the Value column. This value will be rendered in JWT. 
5. After attribute value is added with the “account_name” key click the “Add” button to the right from the attribute value. And then click the “Save” button at the bottom of the attributes table.
6. Once the changes are saved, add a user to the group going to “Users” tab -> Find and select a user -> Groups -> Select groups -> Click the “Join” button.

### How to render a list of groups in JWT

1. Create a user.
2. Create a group.
3. Add the user to the group, go to the “Users” tab -> Find and select user -> Groups -> Select group -> Click the “Join” button.

### How to render a list of roles in JWT

1. Create a user.
2. Create a role.
3. Add the role to the user by any convenient method.

## There is no need to connect the “client scope” in the current implementation, because it is already connected the keycloak client with “Sample-react” name in the realm-export.json

### How to connect the “client scope”

1. Select a created keycloak client
2. Go to it
3. Go to  the “client scopes” tab
4. In the “Default Client Scopes” section select our client scope, it is named “Auth_scope”, a list of groups, roles, and the account_name fiedl in JWT payload is added to it.
5. Once selected, click the “Add selected” button, and our client scope will rebase to the “Assigned Default Client Scopes” section.
6. Done
