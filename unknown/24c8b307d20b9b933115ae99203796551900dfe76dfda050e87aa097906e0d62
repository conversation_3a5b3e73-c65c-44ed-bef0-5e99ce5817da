import pytest
from cdp.models import (
    FieldSet,
    AssignedField,
    PageObjectType,
    FieldDefinition,
    RelevantPageObject,
)
from cdp.services import get_page_object_types


@pytest.fixture
def field_set():
    return FieldSet.objects.create(key="hd_internal_test")


@pytest.fixture(params=["land_register_area", "plot_size"])
def page_object_type(request):
    return PageObjectType.objects.create(key=request.param)


@pytest.fixture
def field_definition():
    return FieldDefinition.objects.create(key="Grundstückfläche", flavour="hd")


@pytest.fixture
def field_definition_no_flavour():
    return FieldDefinition.objects.create(key="Grundstückfläche")


@pytest.fixture
def relevant_page_object(
    field_definition,
    page_object_type,
):
    return RelevantPageObject.objects.create(
        field_definition=field_definition,
        page_object=page_object_type,
    )


@pytest.fixture
def relevant_page_object_no_flavour(
    field_definition_no_flavour,
    page_object_type,
):
    return RelevantPageObject.objects.create(
        field_definition=field_definition_no_flavour,
        page_object=page_object_type,
    )


@pytest.fixture
def assigned_field(field_set, field_definition):
    """Fixture to create an AssignedField."""
    return AssignedField.objects.create(
        field_set=field_set,
        field_definition=field_definition,
    )


@pytest.mark.django_db
def test_page_object_types(page_object_type):
    """Test that PageObjectType is created successfully."""
    assert page_object_type.key in ["land_register_area", "plot_size"]


@pytest.mark.django_db
def test_returns_correct_field_mapping(
    field_set, assigned_field, relevant_page_object, field_definition
):
    """Test that get_field_mapping returns the correct field mapping."""
    page_objects = get_page_object_types(assigned_field)
    for po in page_objects:
        assert po.key in ["land_register_area", "plot_size"]


@pytest.mark.django_db
def test_returns_empty_list_if_field_definition_not_in_field_set(
    field_set, assigned_field
):
    """Test that get_field_mapping returns an empty list if FieldDefinition is not in FieldSet"""
    field_definition_not_in_field_set = FieldDefinition.objects.create(
        key="Cadaster", flavour="hd"
    )
    AssignedField.objects.create(
        field_set=FieldSet.objects.create(key="some_other_field_set"),
        field_definition=field_definition_not_in_field_set,
    )
    page_objects = get_page_object_types(assigned_field)
    assert page_objects == []
