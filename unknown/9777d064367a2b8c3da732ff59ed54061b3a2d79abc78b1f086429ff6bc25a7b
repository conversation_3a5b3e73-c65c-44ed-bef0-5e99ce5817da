from pathlib import Path

import pytest
from django.contrib.auth.models import User

from dossier.models import Account
from dossier.services import (
    create_dossier,
    change_dossier_work_status,
    create_bekb_dossier_state_context,
)
from dossier.statemachine_types import Do<PERSON><PERSON><PERSON>tate, DOSSIER_READ_ONLY_WORK_STATUS
from events.models import Event
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine, Status
from statemgmt.services import StateTransitionError


def test_new_dossier_has_initial_work_state_if_there_is_an_active_work_status_engine(
    db,
):
    account = Account.objects.create(key="test", name="Test Account")

    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    state_machine.start_status = state_machine.status_set.first()
    assert state_machine.start_status

    account.active_work_status_state_machine = state_machine

    assert account.active_work_status_state_machine
    user = User.objects.create_user(
        "testuser", "<EMAIL>", "some randomeness"
    )
    new_dossier = create_dossier(account, "sample dossier", "de", user)

    assert new_dossier.work_status == state_machine.start_status

    account.active_work_status_state_machine = None
    assert not account.active_work_status_state_machine

    new_dossier = create_dossier(account, "sample dossier", "de", user)
    assert new_dossier.work_status is None


def test_dossier_work_status_change_event(db):
    account = Account.objects.create(key="test", name="Test Account")

    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    state_machine.start_status = state_machine.status_set.first()
    assert state_machine.start_status

    account.active_work_status_state_machine = state_machine

    user = User.objects.create_user(
        "testuser", "<EMAIL>", "some randomeness"
    )
    new_dossier = create_dossier(account, "sample dossier", "de", user)
    new_dossier.save()

    Event.objects.all().delete()
    new_dossier.work_status = state_machine.status_set.all()[1]
    new_dossier.save()

    list(Event.objects.all())

    assert (
        Event.objects.filter(
            type="dossier.schemas.DossierWorkStatusChangedEvent"
        ).count()
        == 1
    )


def test_basic_status_change(db):
    account = Account.objects.create(key="test", name="Test Account")
    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    state_machine.start_status = Status.objects.get(
        key="IN_FRONT_OFFICE", state_machine=state_machine
    )
    account.active_work_status_state_machine = state_machine
    account.save()

    user = User.objects.create_user(
        "testuser", "<EMAIL>", "some randomeness"
    )

    dossier = create_dossier(account, "sample dossier", "de", user)
    context = create_bekb_dossier_state_context()
    new_status = Status.objects.get(
        key="READY_FOR_CREDIT_OFFICE",
        state_machine=account.active_work_status_state_machine,
    )

    change_dossier_work_status(dossier, context, new_status)

    assert dossier.work_status == new_status

    # changing to an invalid state should fail
    new_status = Status.objects.get(
        key="EXPORT_DONE", state_machine=account.active_work_status_state_machine
    )

    with pytest.raises(StateTransitionError):
        change_dossier_work_status(dossier, context, new_status)


def test_ensure_state_keys_in_state_machine_dossier_default(db):
    account = Account.objects.create(key="test", name="Test Account")

    # Load the dossier state machine configuration
    update_state_machine(
        Path(__file__).parent.parent.parent
        / "statemgmt/configurations/default/default_state_machine_2025_02_21.json",
        "Dossier Default",
    )

    account.active_work_status_state_machine = StateMachine.objects.get(
        name="Dossier Default"
    )

    account.save()

    state_machine = account.active_work_status_state_machine

    start_status = state_machine.start_status

    assert start_status.key == DossierState.OPEN.value

    # These states must exist in "statemgmt/configurations/default/default_state_machine_2025_02_21.json"

    assert (
        Status.objects.filter(
            state_machine=state_machine,
            key__in=[
                DossierState.OPEN.value,
                DossierState.CLOSING.value,
                DossierState.CLOSED.value,
            ],
        ).count()
        == 3
    )


def test_ensure_read_only_state_keys_in_dossier_read_only_work_status_keys_dossier_default():
    # These must exist in DOSSIER_READ_ONLY_WORK_STATUS so that the Dossier is considered read-only
    # when in these states
    assert DossierState.CLOSING.value in DOSSIER_READ_ONLY_WORK_STATUS
    assert DossierState.CLOSED.value in DOSSIER_READ_ONLY_WORK_STATUS
