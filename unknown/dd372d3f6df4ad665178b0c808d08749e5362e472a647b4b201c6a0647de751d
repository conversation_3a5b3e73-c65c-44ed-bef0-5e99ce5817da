import pytest
from django.core.cache import cache
from freezegun import freeze_time

from cdp.models import ReturnType

from cdp.value_parser import (
    get_parser_strategy_map,
    <PERSON><PERSON><PERSON><PERSON>,
    VolumeParser,
    build_parser_strategy_map,
    get_parser,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
)

CACHE_KEY = "parser_strategy_map"
CACHE_TIMEOUT = 300  # Cache timeout in seconds (5 minutes)


# def get_parser_strategy_map() -> dict:
#     # Attempt to fetch the parser strategy map from the cache
#     parser_strategy_map = cache.get(CACHE_KEY)
#
#     if parser_strategy_map is None:
#         # If not in cache, build the parser strategy map
#         return_type_keys = ReturnType.objects.values_list("key", flat=True)
#
#         parser_strategy_map = {}
#         for key in return_type_keys:
#             parser_class_name = f"{key.capitalize()}Parser"
#             parser_class = globals().get(parser_class_name)
#
#             if parser_class:
#                 parser_strategy_map[key] = parser_class
#             else:
#                 logger.warning(f"No parser class found for ReturnType: {key}")
#
#         # Store the parser strategy map in the cache
#         cache.set(CACHE_KEY, parser_strategy_map, CACHE_TIMEOUT)
#
#     return parser_strategy_map


@pytest.mark.django_db
def test_build_parser_strategy_map():
    ReturnType.objects.create(key="area")
    ReturnType.objects.create(key="volume")

    parser_strategy_map = build_parser_strategy_map()

    assert (
        "area" in parser_strategy_map
    ), "Parser strategy map should contain 'area' key"
    assert (
        parser_strategy_map["area"] == AreaParser
    ), "'area' key should map to AreaParser"

    assert (
        "volume" in parser_strategy_map
    ), "Parser strategy map should contain 'volume' key"
    assert (
        parser_strategy_map["volume"] == VolumeParser
    ), "'volume' key should map to VolumeParser"


@pytest.mark.django_db
@pytest.mark.usefixtures("cache")
def test_get_parser_strategy_map_populates_cache():
    """
    Test that get_parser_strategy_map populates the cache when it is empty.
    """
    # Ensure the cache is empty before the test
    cache.delete(CACHE_KEY)

    # Create test ReturnType objects in the database
    ReturnType.objects.create(key="area")
    ReturnType.objects.create(key="volume")
    ReturnType.objects.create(key="integer")

    # Call get_parser_strategy_map to populate the cache
    _ = get_parser_strategy_map(cache_key=CACHE_KEY, cache_timeout=CACHE_TIMEOUT)

    # Verify the cache is populated
    cached_data = cache.get("parser_strategy_map")
    assert cached_data is not None, "Cache should be populated"
    assert "area" in cached_data, "Cache should contain the 'area' parser"
    assert "volume" in cached_data, "Cache should contain the 'volume' parser"

    # Verify the parser map is correctly built
    assert callable(cached_data["area"]), "The 'area' parser should be callable"
    assert callable(cached_data["volume"]), "The 'volume' parser should be callable"


@pytest.mark.django_db
@pytest.mark.usefixtures("cache")
def test_get_parser_strategy_map_uses_cache(mocker):
    cache.delete(CACHE_KEY)
    ReturnType.objects.create(key="currency")

    mock_query = mocker.patch("cdp.models.ReturnType.objects.values_list")

    # First call to populate the cache
    parser_strategy_map = get_parser_strategy_map(
        cache_key=CACHE_KEY, cache_timeout=CACHE_TIMEOUT
    )

    # Second call should use the cache, not hit the database
    parser_strategy_map_cached = get_parser_strategy_map(
        cache_key=CACHE_KEY, cache_timeout=CACHE_TIMEOUT
    )

    mock_query.assert_called_once()

    assert (
        parser_strategy_map == parser_strategy_map_cached
    ), "Data should be fetched from cache"


@pytest.mark.django_db
@pytest.mark.usefixtures("cache")
def test_get_parser_strategy_map_cache_invalidation():
    cache.delete(CACHE_KEY)

    ReturnType.objects.create(key="boolean")

    # First call to populate the cache
    get_parser_strategy_map(CACHE_KEY, CACHE_TIMEOUT)

    assert cache.get(CACHE_KEY) is not None, "Cache should be populated"

    with freeze_time() as frozen_time:
        frozen_time.tick(299)
        assert cache.get(CACHE_KEY) is not None, "Cache should still be populated"

    with freeze_time() as frozen_time:
        frozen_time.tick(301)
        assert cache.get(CACHE_KEY) is None, "Cache should be empty after invalidation"

        # Second call to repopulate the cache
        get_parser_strategy_map(CACHE_KEY, CACHE_TIMEOUT)

        assert (
            cache.get("parser_strategy_map") is not None
        ), "Cache should be repopulated"


@pytest.mark.django_db
@pytest.mark.usefixtures("cache")
def test_get_parser_returns_correct_parser():
    """
    Test that get_parser returns the correct parser type based on ReturnType.key.
    """
    cache.delete(CACHE_KEY)

    ReturnType.objects.create(key="area")
    ReturnType.objects.create(key="volume")
    ReturnType.objects.create(key="integer")

    area_parser = get_parser("area")
    assert isinstance(
        area_parser, AreaParser
    ), "get_parser('area') should return an instance of AreaParser"

    volume_parser = get_parser("volume")
    assert isinstance(
        volume_parser, VolumeParser
    ), "get_parser('volume') should return an instance of VolumeParser"

    integer_parser = get_parser("integer")
    assert isinstance(
        integer_parser, IntegerParser
    ), "get_parser('integer') should return an instance of IntegerParser"

    unknown_parser = get_parser("unknown")
    assert isinstance(
        unknown_parser, StringParser
    ), "get_parser('unknown') should return an instance of IdentityParser"
