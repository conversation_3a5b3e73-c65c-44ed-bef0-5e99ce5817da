version: '3.8'
services:
  dms:
    build:
      context: dossier-backend
    env_file:
      - ./.env
    volumes:
      - test_result:/data
    depends_on:
      rabbitmq:
        condition: service_healthy
      django-db:
        condition: service_healthy
    command: sh -c "pytest --junitxml=/data/report.xml"

  keycloak:
    depends_on:
      keycloak-db:
        condition: service_healthy

volumes:
  test_result:
